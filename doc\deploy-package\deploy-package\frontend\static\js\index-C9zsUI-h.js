import{_ as r,O as g,y as c,e as d,c as p,k as m,l as f,o as h,f as y}from"./index-D_FV2sri.js";const z={key:0,class:"custom-pagination-wrapper"},b=g({name:"CustomPagination"}),C=Object.assign(b,{props:{currentPage:{type:Number,default:1},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:()=>[10,20,50,100]},total:{type:Number,required:!0},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},small:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},hideOnSinglePage:{type:Boolean,default:!1},show:{type:Boolean,default:!0}},emits:["update:currentPage","update:pageSize","pagination","size-change","current-change"],setup(e,{emit:o}){const n=e,t=o,i=c(()=>n.show&&n.total>0),l=a=>{t("update:pageSize",a),t("size-change",a),t("pagination",{page:n.currentPage,limit:a})},s=a=>{t("update:currentPage",a),t("current-change",a),t("pagination",{page:a,limit:n.pageSize})};return(a,S)=>{const u=d("el-pagination");return f(i)?(h(),p("div",z,[y(u,{"current-page":e.currentPage,"page-size":e.pageSize,"page-sizes":e.pageSizes,total:e.total,layout:e.layout,background:e.background,small:e.small,disabled:e.disabled,"hide-on-single-page":e.hideOnSinglePage,onSizeChange:l,onCurrentChange:s,class:"custom-pagination"},null,8,["current-page","page-size","page-sizes","total","layout","background","small","disabled","hide-on-single-page"])])):m("",!0)}}}),k=r(C,[["__scopeId","data-v-ce537d99"]]);export{k as C};
