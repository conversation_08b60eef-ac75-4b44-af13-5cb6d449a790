import{g as re,_ as ie,b as ne,u as se,a as oe,d as ae,r as ft,w as he,e as ut,c as Rt,f as F,h as G,i as Ot,j as ue,k as ce,t as le,l as j,m as kt,o as wt,n as fe,p as pe,q as ge,s as $}from"./index-Cox2ohSR.js";var Ft={exports:{}};/*! For license information please see jsencrypt.min.js.LICENSE.txt */(function(st,J){(function(pt,it){st.exports=it()})(window,()=>(()=>{var pt={155:m=>{var y,D,v=m.exports={};function U(){throw new Error("setTimeout has not been defined")}function Y(){throw new Error("clearTimeout has not been defined")}function X(w){if(y===setTimeout)return setTimeout(w,0);if((y===U||!y)&&setTimeout)return y=setTimeout,setTimeout(w,0);try{return y(w,0)}catch{try{return y.call(null,w,0)}catch{return y.call(this,w,0)}}}(function(){try{y=typeof setTimeout=="function"?setTimeout:U}catch{y=U}try{D=typeof clearTimeout=="function"?clearTimeout:Y}catch{D=Y}})();var L,N=[],E=!1,B=-1;function P(){E&&L&&(E=!1,L.length?N=L.concat(N):B=-1,N.length&&K())}function K(){if(!E){var w=X(P);E=!0;for(var M=N.length;M;){for(L=N,N=[];++B<M;)L&&L[B].run();B=-1,M=N.length}L=null,E=!1,function(V){if(D===clearTimeout)return clearTimeout(V);if((D===Y||!D)&&clearTimeout)return D=clearTimeout,clearTimeout(V);try{return D(V)}catch{try{return D.call(null,V)}catch{return D.call(this,V)}}}(w)}}function I(w,M){this.fun=w,this.array=M}function q(){}v.nextTick=function(w){var M=new Array(arguments.length-1);if(arguments.length>1)for(var V=1;V<arguments.length;V++)M[V-1]=arguments[V];N.push(new I(w,M)),N.length!==1||E||X(K)},I.prototype.run=function(){this.fun.apply(null,this.array)},v.title="browser",v.browser=!0,v.env={},v.argv=[],v.version="",v.versions={},v.on=q,v.addListener=q,v.once=q,v.off=q,v.removeListener=q,v.removeAllListeners=q,v.emit=q,v.prependListener=q,v.prependOnceListener=q,v.listeners=function(w){return[]},v.binding=function(w){throw new Error("process.binding is not supported")},v.cwd=function(){return"/"},v.chdir=function(w){throw new Error("process.chdir is not supported")},v.umask=function(){return 0}}},it={};function Q(m){var y=it[m];if(y!==void 0)return y.exports;var D=it[m]={exports:{}};return pt[m](D,D.exports,Q),D.exports}Q.d=(m,y)=>{for(var D in y)Q.o(y,D)&&!Q.o(m,D)&&Object.defineProperty(m,D,{enumerable:!0,get:y[D]})},Q.o=(m,y)=>Object.prototype.hasOwnProperty.call(m,y);var gt={};return(()=>{Q.d(gt,{default:()=>te});var m="0123456789abcdefghijklmnopqrstuvwxyz";function y(i){return m.charAt(i)}function D(i,t){return i&t}function v(i,t){return i|t}function U(i,t){return i^t}function Y(i,t){return i&~t}function X(i){if(i==0)return-1;var t=0;return!(65535&i)&&(i>>=16,t+=16),!(255&i)&&(i>>=8,t+=8),!(15&i)&&(i>>=4,t+=4),!(3&i)&&(i>>=2,t+=2),!(1&i)&&++t,t}function L(i){for(var t=0;i!=0;)i&=i-1,++t;return t}var N,E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",B="=";function P(i){var t,e,r="";for(t=0;t+3<=i.length;t+=3)e=parseInt(i.substring(t,t+3),16),r+=E.charAt(e>>6)+E.charAt(63&e);for(t+1==i.length?(e=parseInt(i.substring(t,t+1),16),r+=E.charAt(e<<2)):t+2==i.length&&(e=parseInt(i.substring(t,t+2),16),r+=E.charAt(e>>2)+E.charAt((3&e)<<4));(3&r.length)>0;)r+=B;return r}function K(i){var t,e="",r=0,n=0;for(t=0;t<i.length&&i.charAt(t)!=B;++t){var s=E.indexOf(i.charAt(t));s<0||(r==0?(e+=y(s>>2),n=3&s,r=1):r==1?(e+=y(n<<2|s>>4),n=15&s,r=2):r==2?(e+=y(n),e+=y(s>>2),n=3&s,r=3):(e+=y(n<<2|s>>4),e+=y(15&s),r=0))}return r==1&&(e+=y(n<<2)),e}var I,q={decode:function(i){var t;if(I===void 0){var e=`= \f
\r	 \u2028\u2029`;for(I=Object.create(null),t=0;t<64;++t)I["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(I["-"]=62,I._=63,t=0;t<e.length;++t)I[e.charAt(t)]=-1}var r=[],n=0,s=0;for(t=0;t<i.length;++t){var o=i.charAt(t);if(o=="=")break;if((o=I[o])!=-1){if(o===void 0)throw new Error("Illegal character at offset "+t);n|=o,++s>=4?(r[r.length]=n>>16,r[r.length]=n>>8&255,r[r.length]=255&n,n=0,s=0):n<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=n>>10;break;case 3:r[r.length]=n>>16,r[r.length]=n>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(i){var t=q.re.exec(i);if(t)if(t[1])i=t[1];else{if(!t[2])throw new Error("RegExp out of sync");i=t[2]}return q.decode(i)}},w=1e13,M=function(){function i(t){this.buf=[+t||0]}return i.prototype.mulAdd=function(t,e){var r,n,s=this.buf,o=s.length;for(r=0;r<o;++r)(n=s[r]*t+e)<w?e=0:n-=(e=0|n/w)*w,s[r]=n;e>0&&(s[r]=e)},i.prototype.sub=function(t){var e,r,n=this.buf,s=n.length;for(e=0;e<s;++e)(r=n[e]-t)<0?(r+=w,t=1):t=0,n[e]=r;for(;n[n.length-1]===0;)n.pop()},i.prototype.toString=function(t){if((t||10)!=10)throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),n=e.length-2;n>=0;--n)r+=(w+e[n]).toString().substring(1);return r},i.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*w+t[r];return e},i.prototype.simplify=function(){var t=this.buf;return t.length==1?t[0]:this},i}(),V="…",It=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,Nt=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function ot(i,t){return i.length>t&&(i=i.substring(0,t)+V),i}var tt,Dt=function(){function i(t,e){this.hexDigits="0123456789ABCDEF",t instanceof i?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=e)}return i.prototype.get=function(t){if(t===void 0&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return typeof this.enc=="string"?this.enc.charCodeAt(t):this.enc[t]},i.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},i.prototype.hexDump=function(t,e,r){for(var n="",s=t;s<e;++s)if(n+=this.hexByte(this.get(s)),r!==!0)switch(15&s){case 7:n+="  ";break;case 15:n+=`
`;break;default:n+=" "}return n},i.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0},i.prototype.parseStringISO=function(t,e){for(var r="",n=t;n<e;++n)r+=String.fromCharCode(this.get(n));return r},i.prototype.parseStringUTF=function(t,e){for(var r="",n=t;n<e;){var s=this.get(n++);r+=s<128?String.fromCharCode(s):s>191&&s<224?String.fromCharCode((31&s)<<6|63&this.get(n++)):String.fromCharCode((15&s)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r},i.prototype.parseStringBMP=function(t,e){for(var r,n,s="",o=t;o<e;)r=this.get(o++),n=this.get(o++),s+=String.fromCharCode(r<<8|n);return s},i.prototype.parseTime=function(t,e,r){var n=this.parseStringISO(t,e),s=(r?It:Nt).exec(n);return s?(r&&(s[1]=+s[1],s[1]+=+s[1]<70?2e3:1900),n=s[1]+"-"+s[2]+"-"+s[3]+" "+s[4],s[5]&&(n+=":"+s[5],s[6]&&(n+=":"+s[6],s[7]&&(n+="."+s[7]))),s[8]&&(n+=" UTC",s[8]!="Z"&&(n+=s[8],s[9]&&(n+=":"+s[9]))),n):"Unrecognized time: "+n},i.prototype.parseInteger=function(t,e){for(var r,n=this.get(t),s=n>127,o=s?255:0,a="";n==o&&++t<e;)n=this.get(t);if((r=e-t)==0)return s?-1:0;if(r>4){for(a=n,r<<=3;!(128&(+a^o));)a=+a<<1,--r;a="("+r+` bit)
`}s&&(n-=256);for(var h=new M(n),c=t+1;c<e;++c)h.mulAdd(256,this.get(c));return a+h.toString()},i.prototype.parseBitString=function(t,e,r){for(var n=this.get(t),s="("+((e-t-1<<3)-n)+` bit)
`,o="",a=t+1;a<e;++a){for(var h=this.get(a),c=a==e-1?n:0,l=7;l>=c;--l)o+=h>>l&1?"1":"0";if(o.length>r)return s+ot(o,r)}return s+o},i.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return ot(this.parseStringISO(t,e),r);var n=e-t,s="("+n+` byte)
`;n>(r/=2)&&(e=t+r);for(var o=t;o<e;++o)s+=this.hexByte(this.get(o));return n>r&&(s+=V),s},i.prototype.parseOID=function(t,e,r){for(var n="",s=new M,o=0,a=t;a<e;++a){var h=this.get(a);if(s.mulAdd(128,127&h),o+=7,!(128&h)){if(n==="")if((s=s.simplify())instanceof M)s.sub(80),n="2."+s.toString();else{var c=s<80?s<40?0:1:2;n=c+"."+(s-40*c)}else n+="."+s.toString();if(n.length>r)return ot(n,r);s=new M,o=0}}return o>0&&(n+=".incomplete"),n},i}(),zt=function(){function i(t,e,r,n,s){if(!(n instanceof Mt))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=n,this.sub=s}return i.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},i.prototype.content=function(t){if(this.tag===void 0)return null;t===void 0&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return this.sub!==null?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return this.stream.get(e)===0?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return this.sub!==null?"("+this.sub.length+" elem)":"(no elem)";case 12:return ot(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return ot(this.stream.parseStringISO(e,e+r),t);case 30:return ot(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,this.tag.tagNumber==23)}return null},i.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(this.sub===null?"null":this.sub.length)+"]"},i.prototype.toPrettyString=function(t){t===void 0&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||this.tag.tagNumber!=3&&this.tag.tagNumber!=4||this.sub===null||(e+=" (encapsulates)"),e+=`
`,this.sub!==null){t+="  ";for(var r=0,n=this.sub.length;r<n;++r)e+=this.sub[r].toPrettyString(t)}return e},i.prototype.posStart=function(){return this.stream.pos},i.prototype.posContent=function(){return this.stream.pos+this.header},i.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},i.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},i.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(r===0)return null;e=0;for(var n=0;n<r;++n)e=256*e+t.get();return e},i.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},i.decode=function(t){var e;e=t instanceof Dt?t:new Dt(t,0);var r=new Dt(e),n=new Mt(e),s=i.decodeLength(e),o=e.pos,a=o-r.pos,h=null,c=function(){var b=[];if(s!==null){for(var g=o+s;e.pos<g;)b[b.length]=i.decode(e);if(e.pos!=g)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var x=i.decode(e);if(x.tag.isEOC())break;b[b.length]=x}s=o-e.pos}catch(S){throw new Error("Exception while decoding undefined length content: "+S)}return b};if(n.tagConstructed)h=c();else if(n.isUniversal()&&(n.tagNumber==3||n.tagNumber==4))try{if(n.tagNumber==3&&e.get()!=0)throw new Error("BIT STRINGs with unused bits cannot encapsulate.");h=c();for(var l=0;l<h.length;++l)if(h[l].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch{h=null}if(h===null){if(s===null)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);e.pos=o+Math.abs(s)}return new i(r,a,s,n,h)},i}(),Mt=function(){function i(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=(32&e)!=0,this.tagNumber=31&e,this.tagNumber==31){var r=new M;do e=t.get(),r.mulAdd(128,127&e);while(128&e);this.tagNumber=r.simplify()}}return i.prototype.isUniversal=function(){return this.tagClass===0},i.prototype.isEOC=function(){return this.tagClass===0&&this.tagNumber===0},i}(),C=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Zt=(1<<26)/C[C.length-1],p=function(){function i(t,e,r){t!=null&&(typeof t=="number"?this.fromNumber(t,e,r):e==null&&typeof t!="string"?this.fromString(t,256):this.fromString(t,e))}return i.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(t==16)e=4;else if(t==8)e=3;else if(t==2)e=1;else if(t==32)e=5;else{if(t!=4)return this.toRadix(t);e=2}var r,n=(1<<e)-1,s=!1,o="",a=this.t,h=this.DB-a*this.DB%e;if(a-- >0)for(h<this.DB&&(r=this[a]>>h)>0&&(s=!0,o=y(r));a>=0;)h<e?(r=(this[a]&(1<<h)-1)<<e-h,r|=this[--a]>>(h+=this.DB-e)):(r=this[a]>>(h-=e)&n,h<=0&&(h+=this.DB,--a)),r>0&&(s=!0),s&&(o+=y(r));return s?o:"0"},i.prototype.negate=function(){var t=d();return i.ZERO.subTo(this,t),t},i.prototype.abs=function(){return this.s<0?this.negate():this},i.prototype.compareTo=function(t){var e=this.s-t.s;if(e!=0)return e;var r=this.t;if((e=r-t.t)!=0)return this.s<0?-e:e;for(;--r>=0;)if((e=this[r]-t[r])!=0)return e;return 0},i.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+mt(this[this.t-1]^this.s&this.DM)},i.prototype.mod=function(t){var e=d();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(i.ZERO)>0&&t.subTo(e,e),e},i.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new Pt(e):new qt(e),this.exp(t,r)},i.prototype.clone=function(){var t=d();return this.copyTo(t),t},i.prototype.intValue=function(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},i.prototype.byteValue=function(){return this.t==0?this.s:this[0]<<24>>24},i.prototype.shortValue=function(){return this.t==0?this.s:this[0]<<16>>16},i.prototype.signum=function(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1},i.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,n=this.DB-t*this.DB%8,s=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[s++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),128&r&&(r|=-256),s==0&&(128&this.s)!=(128&r)&&++s,(s>0||r!=this.s)&&(e[s++]=r);return e},i.prototype.equals=function(t){return this.compareTo(t)==0},i.prototype.min=function(t){return this.compareTo(t)<0?this:t},i.prototype.max=function(t){return this.compareTo(t)>0?this:t},i.prototype.and=function(t){var e=d();return this.bitwiseTo(t,D,e),e},i.prototype.or=function(t){var e=d();return this.bitwiseTo(t,v,e),e},i.prototype.xor=function(t){var e=d();return this.bitwiseTo(t,U,e),e},i.prototype.andNot=function(t){var e=d();return this.bitwiseTo(t,Y,e),e},i.prototype.not=function(){for(var t=d(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},i.prototype.shiftLeft=function(t){var e=d();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},i.prototype.shiftRight=function(t){var e=d();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},i.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(this[t]!=0)return t*this.DB+X(this[t]);return this.s<0?this.t*this.DB:-1},i.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=L(this[r]^e);return t},i.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?this.s!=0:(this[e]&1<<t%this.DB)!=0},i.prototype.setBit=function(t){return this.changeBit(t,v)},i.prototype.clearBit=function(t){return this.changeBit(t,Y)},i.prototype.flipBit=function(t){return this.changeBit(t,U)},i.prototype.add=function(t){var e=d();return this.addTo(t,e),e},i.prototype.subtract=function(t){var e=d();return this.subTo(t,e),e},i.prototype.multiply=function(t){var e=d();return this.multiplyTo(t,e),e},i.prototype.divide=function(t){var e=d();return this.divRemTo(t,e,null),e},i.prototype.remainder=function(t){var e=d();return this.divRemTo(t,null,e),e},i.prototype.divideAndRemainder=function(t){var e=d(),r=d();return this.divRemTo(t,e,r),[e,r]},i.prototype.modPow=function(t,e){var r,n,s=t.bitLength(),o=et(1);if(s<=0)return o;r=s<18?1:s<48?3:s<144?4:s<768?5:6,n=s<8?new Pt(e):e.isEven()?new Qt(e):new qt(e);var a=[],h=3,c=r-1,l=(1<<r)-1;if(a[1]=n.convert(this),r>1){var b=d();for(n.sqrTo(a[1],b);h<=l;)a[h]=d(),n.mulTo(b,a[h-2],a[h]),h+=2}var g,x,S=t.t-1,T=!0,A=d();for(s=mt(t[S])-1;S>=0;){for(s>=c?g=t[S]>>s-c&l:(g=(t[S]&(1<<s+1)-1)<<c-s,S>0&&(g|=t[S-1]>>this.DB+s-c)),h=r;!(1&g);)g>>=1,--h;if((s-=h)<0&&(s+=this.DB,--S),T)a[g].copyTo(o),T=!1;else{for(;h>1;)n.sqrTo(o,A),n.sqrTo(A,o),h-=2;h>0?n.sqrTo(o,A):(x=o,o=A,A=x),n.mulTo(A,a[g],o)}for(;S>=0&&!(t[S]&1<<s);)n.sqrTo(o,A),x=o,o=A,A=x,--s<0&&(s=this.DB-1,--S)}return n.revert(o)},i.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||t.signum()==0)return i.ZERO;for(var r=t.clone(),n=this.clone(),s=et(1),o=et(0),a=et(0),h=et(1);r.signum()!=0;){for(;r.isEven();)r.rShiftTo(1,r),e?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(t,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),e?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(t,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(t,h),h.rShiftTo(1,h);r.compareTo(n)>=0?(r.subTo(n,r),e&&s.subTo(a,s),o.subTo(h,o)):(n.subTo(r,n),e&&a.subTo(s,a),h.subTo(o,h))}return n.compareTo(i.ONE)!=0?i.ZERO:h.compareTo(t)>=0?h.subtract(t):h.signum()<0?(h.addTo(t,h),h.signum()<0?h.add(t):h):h},i.prototype.pow=function(t){return this.exp(t,new Gt)},i.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var s=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(s<o&&(o=s),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(s=e.getLowestSetBit())>0&&e.rShiftTo(s,e),(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},i.prototype.isProbablePrime=function(t){var e,r=this.abs();if(r.t==1&&r[0]<=C[C.length-1]){for(e=0;e<C.length;++e)if(r[0]==C[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<C.length;){for(var n=C[e],s=e+1;s<C.length&&n<Zt;)n*=C[s++];for(n=r.modInt(n);e<s;)if(n%C[e++]==0)return!1}return r.millerRabin(t)},i.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},i.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},i.prototype.fromString=function(t,e){var r;if(e==16)r=4;else if(e==8)r=3;else if(e==256)r=8;else if(e==2)r=1;else if(e==32)r=5;else{if(e!=4)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var n=t.length,s=!1,o=0;--n>=0;){var a=r==8?255&+t[n]:_t(t,n);a<0?t.charAt(n)=="-"&&(s=!0):(s=!1,o==0?this[this.t++]=a:o+r>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=r)>=this.DB&&(o-=this.DB))}r==8&&128&+t[0]&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&i.ZERO.subTo(this,this)},i.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},i.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},i.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},i.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,n=this.DB-r,s=(1<<n)-1,o=Math.floor(t/this.DB),a=this.s<<r&this.DM,h=this.t-1;h>=0;--h)e[h+o+1]=this[h]>>n|a,a=(this[h]&s)<<r;for(h=o-1;h>=0;--h)e[h]=0;e[o]=a,e.t=this.t+o+1,e.s=this.s,e.clamp()},i.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,s=this.DB-n,o=(1<<n)-1;e[0]=this[r]>>n;for(var a=r+1;a<this.t;++a)e[a-r-1]|=(this[a]&o)<<s,e[a-r]=this[a]>>n;n>0&&(e[this.t-r-1]|=(this.s&o)<<s),e.t=this.t-r,e.clamp()}},i.prototype.subTo=function(t,e){for(var r=0,n=0,s=Math.min(t.t,this.t);r<s;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},i.prototype.multiplyTo=function(t,e){var r=this.abs(),n=t.abs(),s=r.t;for(e.t=s+n.t;--s>=0;)e[s]=0;for(s=0;s<n.t;++s)e[s+r.t]=r.am(0,n[s],e,s,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&i.ZERO.subTo(e,e)},i.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},i.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return e!=null&&e.fromInt(0),void(r!=null&&this.copyTo(r));r==null&&(r=d());var o=d(),a=this.s,h=t.s,c=this.DB-mt(n[n.t-1]);c>0?(n.lShiftTo(c,o),s.lShiftTo(c,r)):(n.copyTo(o),s.copyTo(r));var l=o.t,b=o[l-1];if(b!=0){var g=b*(1<<this.F1)+(l>1?o[l-2]>>this.F2:0),x=this.FV/g,S=(1<<this.F1)/g,T=1<<this.F2,A=r.t,W=A-l,_=e??d();for(o.dlShiftTo(W,_),r.compareTo(_)>=0&&(r[r.t++]=1,r.subTo(_,r)),i.ONE.dlShiftTo(l,_),_.subTo(o,o);o.t<l;)o[o.t++]=0;for(;--W>=0;){var H=r[--A]==b?this.DM:Math.floor(r[A]*x+(r[A-1]+T)*S);if((r[A]+=o.am(0,H,r,W,0,l))<H)for(o.dlShiftTo(W,_),r.subTo(_,r);r[A]<--H;)r.subTo(_,r)}e!=null&&(r.drShiftTo(l,e),a!=h&&i.ZERO.subTo(e,e)),r.t=l,r.clamp(),c>0&&r.rShiftTo(c,r),a<0&&i.ZERO.subTo(r,r)}}},i.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},i.prototype.isEven=function(){return(this.t>0?1&this[0]:this.s)==0},i.prototype.exp=function(t,e){if(t>4294967295||t<1)return i.ONE;var r=d(),n=d(),s=e.convert(this),o=mt(t)-1;for(s.copyTo(r);--o>=0;)if(e.sqrTo(r,n),(t&1<<o)>0)e.mulTo(n,s,r);else{var a=r;r=n,n=a}return e.revert(r)},i.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},i.prototype.toRadix=function(t){if(t==null&&(t=10),this.signum()==0||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=et(r),s=d(),o=d(),a="";for(this.divRemTo(n,s,o);s.signum()>0;)a=(r+o.intValue()).toString(t).substr(1)+a,s.divRemTo(n,s,o);return o.intValue().toString(t)+a},i.prototype.fromRadix=function(t,e){this.fromInt(0),e==null&&(e=10);for(var r=this.chunkSize(e),n=Math.pow(e,r),s=!1,o=0,a=0,h=0;h<t.length;++h){var c=_t(t,h);c<0?t.charAt(h)=="-"&&this.signum()==0&&(s=!0):(a=e*a+c,++o>=r&&(this.dMultiply(n),this.dAddOffset(a,0),o=0,a=0))}o>0&&(this.dMultiply(Math.pow(e,o)),this.dAddOffset(a,0)),s&&i.ZERO.subTo(this,this)},i.prototype.fromNumber=function(t,e,r){if(typeof e=="number")if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(i.ONE.shiftLeft(t-1),v,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(i.ONE.shiftLeft(t-1),this);else{var n=[],s=7&t;n.length=1+(t>>3),e.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},i.prototype.bitwiseTo=function(t,e,r){var n,s,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(s=t.s&this.DM,n=o;n<this.t;++n)r[n]=e(this[n],s);r.t=this.t}else{for(s=this.s&this.DM,n=o;n<t.t;++n)r[n]=e(s,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},i.prototype.changeBit=function(t,e){var r=i.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r},i.prototype.addTo=function(t,e){for(var r=0,n=0,s=Math.min(t.t,this.t);r<s;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},i.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},i.prototype.dAddOffset=function(t,e){if(t!=0){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},i.prototype.multiplyLowerTo=function(t,e,r){var n=Math.min(this.t+t.t,e);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var s=r.t-this.t;n<s;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(s=Math.min(t.t,e);n<s;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()},i.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},i.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(e==0)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},i.prototype.millerRabin=function(t){var e=this.subtract(i.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var n=e.shiftRight(r);(t=t+1>>1)>C.length&&(t=C.length);for(var s=d(),o=0;o<t;++o){s.fromInt(C[Math.floor(Math.random()*C.length)]);var a=s.modPow(n,this);if(a.compareTo(i.ONE)!=0&&a.compareTo(e)!=0){for(var h=1;h++<r&&a.compareTo(e)!=0;)if((a=a.modPowInt(2,this)).compareTo(i.ONE)==0)return!1;if(a.compareTo(e)!=0)return!1}}return!0},i.prototype.square=function(){var t=d();return this.squareTo(t),t},i.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(r.compareTo(n)<0){var s=r;r=n,n=s}var o=r.getLowestSetBit(),a=n.getLowestSetBit();if(a<0)e(r);else{o<a&&(a=o),a>0&&(r.rShiftTo(a,r),n.rShiftTo(a,n));var h=function(){(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(h,0):(a>0&&n.lShiftTo(a,n),setTimeout(function(){e(n)},0))};setTimeout(h,10)}},i.prototype.fromNumberAsync=function(t,e,r,n){if(typeof e=="number")if(t<2)this.fromInt(1);else{this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(i.ONE.shiftLeft(t-1),v,this),this.isEven()&&this.dAddOffset(1,0);var s=this,o=function(){s.dAddOffset(2,0),s.bitLength()>t&&s.subTo(i.ONE.shiftLeft(t-1),s),s.isProbablePrime(e)?setTimeout(function(){n()},0):setTimeout(o,0)};setTimeout(o,0)}else{var a=[],h=7&t;a.length=1+(t>>3),e.nextBytes(a),h>0?a[0]&=(1<<h)-1:a[0]=0,this.fromString(a,256)}},i}(),Gt=function(){function i(){}return i.prototype.convert=function(t){return t},i.prototype.revert=function(t){return t},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},i.prototype.sqrTo=function(t,e){t.squareTo(e)},i}(),Pt=function(){function i(t){this.m=t}return i.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},i.prototype.revert=function(t){return t},i.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}(),qt=function(){function i(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return i.prototype.convert=function(t){var e=d();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(p.ZERO)>0&&this.m.subTo(e,e),e},i.prototype.revert=function(t){var e=d();return t.copyTo(e),this.reduce(e),e},i.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}(),Qt=function(){function i(t){this.m=t,this.r2=d(),this.q3=d(),p.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return i.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=d();return t.copyTo(e),this.reduce(e),e},i.prototype.revert=function(t){return t},i.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}();function d(){return new p(null)}function R(i,t){return new p(i,t)}var Ct=typeof navigator<"u";Ct&&navigator.appName=="Microsoft Internet Explorer"?(p.prototype.am=function(i,t,e,r,n,s){for(var o=32767&t,a=t>>15;--s>=0;){var h=32767&this[i],c=this[i++]>>15,l=a*h+c*o;n=((h=o*h+((32767&l)<<15)+e[r]+(1073741823&n))>>>30)+(l>>>15)+a*c+(n>>>30),e[r++]=1073741823&h}return n},tt=30):Ct&&navigator.appName!="Netscape"?(p.prototype.am=function(i,t,e,r,n,s){for(;--s>=0;){var o=t*this[i++]+e[r]+n;n=Math.floor(o/67108864),e[r++]=67108863&o}return n},tt=26):(p.prototype.am=function(i,t,e,r,n,s){for(var o=16383&t,a=t>>14;--s>=0;){var h=16383&this[i],c=this[i++]>>14,l=a*h+c*o;n=((h=o*h+((16383&l)<<14)+e[r]+n)>>28)+(l>>14)+a*c,e[r++]=268435455&h}return n},tt=28),p.prototype.DB=tt,p.prototype.DM=(1<<tt)-1,p.prototype.DV=1<<tt,p.prototype.FV=Math.pow(2,52),p.prototype.F1=52-tt,p.prototype.F2=2*tt-52;var at,k,dt=[];for(at=48,k=0;k<=9;++k)dt[at++]=k;for(at=97,k=10;k<36;++k)dt[at++]=k;for(at=65,k=10;k<36;++k)dt[at++]=k;function _t(i,t){var e=dt[i.charCodeAt(t)];return e??-1}function et(i){var t=d();return t.fromInt(i),t}function mt(i){var t,e=1;return(t=i>>>16)!=0&&(i=t,e+=16),(t=i>>8)!=0&&(i=t,e+=8),(t=i>>4)!=0&&(i=t,e+=4),(t=i>>2)!=0&&(i=t,e+=2),(t=i>>1)!=0&&(i=t,e+=1),e}p.ZERO=et(0),p.ONE=et(1);var vt,z,Yt=function(){function i(){this.i=0,this.j=0,this.S=[]}return i.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},i.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},i}(),jt=256,rt=null;if(rt==null){rt=[],z=0;var yt=void 0;if(typeof window<"u"&&window.crypto&&window.crypto.getRandomValues){var xt=new Uint32Array(256);for(window.crypto.getRandomValues(xt),yt=0;yt<xt.length;++yt)rt[z++]=255&xt[yt]}var At=0,bt=function(i){if((At=At||0)>=256||z>=jt)window.removeEventListener?window.removeEventListener("mousemove",bt,!1):window.detachEvent&&window.detachEvent("onmousemove",bt);else try{var t=i.x+i.y;rt[z++]=255&t,At+=1}catch{}};typeof window<"u"&&(window.addEventListener?window.addEventListener("mousemove",bt,!1):window.attachEvent&&window.attachEvent("onmousemove",bt))}function Wt(){if(vt==null){for(vt=new Yt;z<jt;){var i=Math.floor(65536*Math.random());rt[z++]=255&i}for(vt.init(rt),z=0;z<rt.length;++z)rt[z]=0;z=0}return vt.next()}var Bt=function(){function i(){}return i.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=Wt()},i}(),$t=function(){function i(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return i.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},i.prototype.doPrivate=function(t){if(this.p==null||this.q==null)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},i.prototype.setPublic=function(t,e){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=R(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},i.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(h,c){if(c<h.length+11)return console.error("Message too long for RSA"),null;for(var l=[],b=h.length-1;b>=0&&c>0;){var g=h.charCodeAt(b--);g<128?l[--c]=g:g>127&&g<2048?(l[--c]=63&g|128,l[--c]=g>>6|192):(l[--c]=63&g|128,l[--c]=g>>6&63|128,l[--c]=g>>12|224)}l[--c]=0;for(var x=new Bt,S=[];c>2;){for(S[0]=0;S[0]==0;)x.nextBytes(S);l[--c]=S[0]}return l[--c]=2,l[--c]=0,new p(l)}(t,e);if(r==null)return null;var n=this.doPublic(r);if(n==null)return null;for(var s=n.toString(16),o=s.length,a=0;a<2*e-o;a++)s="0"+s;return s},i.prototype.setPrivate=function(t,e,r){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=R(t,16),this.e=parseInt(e,16),this.d=R(r,16)):console.error("Invalid RSA private key")},i.prototype.setPrivateEx=function(t,e,r,n,s,o,a,h){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=R(t,16),this.e=parseInt(e,16),this.d=R(r,16),this.p=R(n,16),this.q=R(s,16),this.dmp1=R(o,16),this.dmq1=R(a,16),this.coeff=R(h,16)):console.error("Invalid RSA private key")},i.prototype.generate=function(t,e){var r=new Bt,n=t>>1;this.e=parseInt(e,16);for(var s=new p(e,16);;){for(;this.p=new p(t-n,1,r),this.p.subtract(p.ONE).gcd(s).compareTo(p.ONE)!=0||!this.p.isProbablePrime(10););for(;this.q=new p(n,1,r),this.q.subtract(p.ONE).gcd(s).compareTo(p.ONE)!=0||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q,this.q=o}var a=this.p.subtract(p.ONE),h=this.q.subtract(p.ONE),c=a.multiply(h);if(c.gcd(s).compareTo(p.ONE)==0){this.n=this.p.multiply(this.q),this.d=s.modInverse(c),this.dmp1=this.d.mod(a),this.dmq1=this.d.mod(h),this.coeff=this.q.modInverse(this.p);break}}},i.prototype.decrypt=function(t){var e=R(t,16),r=this.doPrivate(e);return r==null?null:function(n,s){for(var o=n.toByteArray(),a=0;a<o.length&&o[a]==0;)++a;if(o.length-a!=s-1||o[a]!=2)return null;for(++a;o[a]!=0;)if(++a>=o.length)return null;for(var h="";++a<o.length;){var c=255&o[a];c<128?h+=String.fromCharCode(c):c>191&&c<224?(h+=String.fromCharCode((31&c)<<6|63&o[a+1]),++a):(h+=String.fromCharCode((15&c)<<12|(63&o[a+1])<<6|63&o[a+2]),a+=2)}return h}(r,this.n.bitLength()+7>>3)},i.prototype.generateAsync=function(t,e,r){var n=new Bt,s=t>>1;this.e=parseInt(e,16);var o=new p(e,16),a=this,h=function(){var c=function(){if(a.p.compareTo(a.q)<=0){var g=a.p;a.p=a.q,a.q=g}var x=a.p.subtract(p.ONE),S=a.q.subtract(p.ONE),T=x.multiply(S);T.gcd(o).compareTo(p.ONE)==0?(a.n=a.p.multiply(a.q),a.d=o.modInverse(T),a.dmp1=a.d.mod(x),a.dmq1=a.d.mod(S),a.coeff=a.q.modInverse(a.p),setTimeout(function(){r()},0)):setTimeout(h,0)},l=function(){a.q=d(),a.q.fromNumberAsync(s,1,n,function(){a.q.subtract(p.ONE).gcda(o,function(g){g.compareTo(p.ONE)==0&&a.q.isProbablePrime(10)?setTimeout(c,0):setTimeout(l,0)})})},b=function(){a.p=d(),a.p.fromNumberAsync(t-s,1,n,function(){a.p.subtract(p.ONE).gcda(o,function(g){g.compareTo(p.ONE)==0&&a.p.isProbablePrime(10)?setTimeout(l,0):setTimeout(b,0)})})};setTimeout(b,0)};setTimeout(h,0)},i.prototype.sign=function(t,e,r){var n=function(a,h){if(h<a.length+22)return console.error("Message too long for RSA"),null;for(var c=h-a.length-6,l="",b=0;b<c;b+=2)l+="ff";return R("0001"+l+"00"+a,16)}((Tt[r]||"")+e(t).toString(),this.n.bitLength()/4);if(n==null)return null;var s=this.doPrivate(n);if(s==null)return null;var o=s.toString(16);return 1&o.length?"0"+o:o},i.prototype.verify=function(t,e,r){var n=R(e,16),s=this.doPublic(n);return s==null?null:function(o){for(var a in Tt)if(Tt.hasOwnProperty(a)){var h=Tt[a],c=h.length;if(o.substr(0,c)==h)return o.substr(c)}return o}(s.toString(16).replace(/^1f+00/,""))==r(t).toString()},i}(),Tt={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"},O={};O.lang={extend:function(i,t,e){if(!t||!i)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var r=function(){};if(r.prototype=t.prototype,i.prototype=new r,i.prototype.constructor=i,i.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t),e){var n;for(n in e)i.prototype[n]=e[n];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(a,h){for(n=0;n<o.length;n+=1){var c=o[n],l=h[c];typeof l=="function"&&l!=Object.prototype[c]&&(a[c]=l)}})}catch{}s(i.prototype,e)}}};var u={};u.asn1!==void 0&&u.asn1||(u.asn1={}),u.asn1.ASN1Util=new function(){this.integerToByteHex=function(i){var t=i.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(i){var t=i.toString(16);if(t.substr(0,1)!="-")t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var e=t.substr(1).length;e%2==1?e+=1:t.match(/^[0-7]/)||(e+=2);for(var r="",n=0;n<e;n++)r+="f";t=new p(r,16).xor(i).add(p.ONE).toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(i,t){return hextopem(i,t)},this.newObject=function(i){var t=u.asn1,e=t.DERBoolean,r=t.DERInteger,n=t.DERBitString,s=t.DEROctetString,o=t.DERNull,a=t.DERObjectIdentifier,h=t.DEREnumerated,c=t.DERUTF8String,l=t.DERNumericString,b=t.DERPrintableString,g=t.DERTeletexString,x=t.DERIA5String,S=t.DERUTCTime,T=t.DERGeneralizedTime,A=t.DERSequence,W=t.DERSet,_=t.DERTaggedObject,H=t.ASN1Util.newObject,ht=Object.keys(i);if(ht.length!=1)throw"key of param shall be only one.";var f=ht[0];if(":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+f+":")==-1)throw"undefined key: "+f;if(f=="bool")return new e(i[f]);if(f=="int")return new r(i[f]);if(f=="bitstr")return new n(i[f]);if(f=="octstr")return new s(i[f]);if(f=="null")return new o(i[f]);if(f=="oid")return new a(i[f]);if(f=="enum")return new h(i[f]);if(f=="utf8str")return new c(i[f]);if(f=="numstr")return new l(i[f]);if(f=="prnstr")return new b(i[f]);if(f=="telstr")return new g(i[f]);if(f=="ia5str")return new x(i[f]);if(f=="utctime")return new S(i[f]);if(f=="gentime")return new T(i[f]);if(f=="seq"){for(var ct=i[f],lt=[],nt=0;nt<ct.length;nt++){var Vt=H(ct[nt]);lt.push(Vt)}return new A({array:lt})}if(f=="set"){for(ct=i[f],lt=[],nt=0;nt<ct.length;nt++)Vt=H(ct[nt]),lt.push(Vt);return new W({array:lt})}if(f=="tag"){var Z=i[f];if(Object.prototype.toString.call(Z)==="[object Array]"&&Z.length==3){var ee=H(Z[2]);return new _({tag:Z[0],explicit:Z[1],obj:ee})}var Et={};if(Z.explicit!==void 0&&(Et.explicit=Z.explicit),Z.tag!==void 0&&(Et.tag=Z.tag),Z.obj===void 0)throw"obj shall be specified for 'tag'.";return Et.obj=H(Z.obj),new _(Et)}},this.jsonToASN1HEX=function(i){return this.newObject(i).getEncodedHex()}},u.asn1.ASN1Util.oidHexToInt=function(i){for(var t="",e=parseInt(i.substr(0,2),16),r=(t=Math.floor(e/40)+"."+e%40,""),n=2;n<i.length;n+=2){var s=("00000000"+parseInt(i.substr(n,2),16).toString(2)).slice(-8);r+=s.substr(1,7),s.substr(0,1)=="0"&&(t=t+"."+new p(r,2).toString(10),r="")}return t},u.asn1.ASN1Util.oidIntToHex=function(i){var t=function(a){var h=a.toString(16);return h.length==1&&(h="0"+h),h},e=function(a){var h="",c=new p(a,10).toString(2),l=7-c.length%7;l==7&&(l=0);for(var b="",g=0;g<l;g++)b+="0";for(c=b+c,g=0;g<c.length-1;g+=7){var x=c.substr(g,7);g!=c.length-7&&(x="1"+x),h+=t(parseInt(x,2))}return h};if(!i.match(/^[0-9.]+$/))throw"malformed oid string: "+i;var r="",n=i.split("."),s=40*parseInt(n[0])+parseInt(n[1]);r+=t(s),n.splice(0,2);for(var o=0;o<n.length;o++)r+=e(n[o]);return r},u.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(this.hV===void 0||this.hV==null)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var i=this.hV.length/2,t=i.toString(16);if(t.length%2==1&&(t="0"+t),i<128)return t;var e=t.length/2;if(e>15)throw"ASN.1 length too long to represent by 8x: n = "+i.toString(16);return(128+e).toString(16)+t},this.getEncodedHex=function(){return(this.hTLV==null||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},u.asn1.DERAbstractString=function(i){u.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"?this.setString(i):i.str!==void 0?this.setString(i.str):i.hex!==void 0&&this.setStringHex(i.hex))},O.lang.extend(u.asn1.DERAbstractString,u.asn1.ASN1Object),u.asn1.DERAbstractTime=function(i){u.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var n=this.zeroPadding,s=this.localDateToUTC(t),o=String(s.getFullYear());e=="utc"&&(o=o.substr(2,2));var a=o+n(String(s.getMonth()+1),2)+n(String(s.getDate()),2)+n(String(s.getHours()),2)+n(String(s.getMinutes()),2)+n(String(s.getSeconds()),2);if(r===!0){var h=s.getMilliseconds();if(h!=0){var c=n(String(h),3);a=a+"."+(c=c.replace(/[0]+$/,""))}}return a+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,n,s,o){var a=new Date(Date.UTC(t,e-1,r,n,s,o,0));this.setByDate(a)},this.getFreshValueHex=function(){return this.hV}},O.lang.extend(u.asn1.DERAbstractTime,u.asn1.ASN1Object),u.asn1.DERAbstractStructured=function(i){u.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,i!==void 0&&i.array!==void 0&&(this.asn1Array=i.array)},O.lang.extend(u.asn1.DERAbstractStructured,u.asn1.ASN1Object),u.asn1.DERBoolean=function(){u.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},O.lang.extend(u.asn1.DERBoolean,u.asn1.ASN1Object),u.asn1.DERInteger=function(i){u.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=u.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new p(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.bigint!==void 0?this.setByBigInteger(i.bigint):i.int!==void 0?this.setByInteger(i.int):typeof i=="number"?this.setByInteger(i):i.hex!==void 0&&this.setValueHex(i.hex))},O.lang.extend(u.asn1.DERInteger,u.asn1.ASN1Object),u.asn1.DERBitString=function(i){if(i!==void 0&&i.obj!==void 0){var t=u.asn1.ASN1Util.newObject(i.obj);i.hex="00"+t.getEncodedHex()}u.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(e){this.hTLV=null,this.isModified=!0,this.hV=e},this.setUnusedBitsAndHexValue=function(e,r){if(e<0||7<e)throw"unused bits shall be from 0 to 7: u = "+e;var n="0"+e;this.hTLV=null,this.isModified=!0,this.hV=n+r},this.setByBinaryString=function(e){var r=8-(e=e.replace(/0+$/,"")).length%8;r==8&&(r=0);for(var n=0;n<=r;n++)e+="0";var s="";for(n=0;n<e.length-1;n+=8){var o=e.substr(n,8),a=parseInt(o,2).toString(16);a.length==1&&(a="0"+a),s+=a}this.hTLV=null,this.isModified=!0,this.hV="0"+r+s},this.setByBooleanArray=function(e){for(var r="",n=0;n<e.length;n++)e[n]==1?r+="1":r+="0";this.setByBinaryString(r)},this.newFalseArray=function(e){for(var r=new Array(e),n=0;n<e;n++)r[n]=!1;return r},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"&&i.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(i):i.hex!==void 0?this.setHexValueIncludingUnusedBits(i.hex):i.bin!==void 0?this.setByBinaryString(i.bin):i.array!==void 0&&this.setByBooleanArray(i.array))},O.lang.extend(u.asn1.DERBitString,u.asn1.ASN1Object),u.asn1.DEROctetString=function(i){if(i!==void 0&&i.obj!==void 0){var t=u.asn1.ASN1Util.newObject(i.obj);i.hex=t.getEncodedHex()}u.asn1.DEROctetString.superclass.constructor.call(this,i),this.hT="04"},O.lang.extend(u.asn1.DEROctetString,u.asn1.DERAbstractString),u.asn1.DERNull=function(){u.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},O.lang.extend(u.asn1.DERNull,u.asn1.ASN1Object),u.asn1.DERObjectIdentifier=function(i){var t=function(r){var n=r.toString(16);return n.length==1&&(n="0"+n),n},e=function(r){var n="",s=new p(r,10).toString(2),o=7-s.length%7;o==7&&(o=0);for(var a="",h=0;h<o;h++)a+="0";for(s=a+s,h=0;h<s.length-1;h+=7){var c=s.substr(h,7);h!=s.length-7&&(c="1"+c),n+=t(parseInt(c,2))}return n};u.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(r){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=r},this.setValueOidString=function(r){if(!r.match(/^[0-9.]+$/))throw"malformed oid string: "+r;var n="",s=r.split("."),o=40*parseInt(s[0])+parseInt(s[1]);n+=t(o),s.splice(0,2);for(var a=0;a<s.length;a++)n+=e(s[a]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(r){var n=u.asn1.x509.OID.name2oid(r);if(n==="")throw"DERObjectIdentifier oidName undefined: "+r;this.setValueOidString(n)},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"?i.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(i):this.setValueName(i):i.oid!==void 0?this.setValueOidString(i.oid):i.hex!==void 0?this.setValueHex(i.hex):i.name!==void 0&&this.setValueName(i.name))},O.lang.extend(u.asn1.DERObjectIdentifier,u.asn1.ASN1Object),u.asn1.DEREnumerated=function(i){u.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=u.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new p(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.int!==void 0?this.setByInteger(i.int):typeof i=="number"?this.setByInteger(i):i.hex!==void 0&&this.setValueHex(i.hex))},O.lang.extend(u.asn1.DEREnumerated,u.asn1.ASN1Object),u.asn1.DERUTF8String=function(i){u.asn1.DERUTF8String.superclass.constructor.call(this,i),this.hT="0c"},O.lang.extend(u.asn1.DERUTF8String,u.asn1.DERAbstractString),u.asn1.DERNumericString=function(i){u.asn1.DERNumericString.superclass.constructor.call(this,i),this.hT="12"},O.lang.extend(u.asn1.DERNumericString,u.asn1.DERAbstractString),u.asn1.DERPrintableString=function(i){u.asn1.DERPrintableString.superclass.constructor.call(this,i),this.hT="13"},O.lang.extend(u.asn1.DERPrintableString,u.asn1.DERAbstractString),u.asn1.DERTeletexString=function(i){u.asn1.DERTeletexString.superclass.constructor.call(this,i),this.hT="14"},O.lang.extend(u.asn1.DERTeletexString,u.asn1.DERAbstractString),u.asn1.DERIA5String=function(i){u.asn1.DERIA5String.superclass.constructor.call(this,i),this.hT="16"},O.lang.extend(u.asn1.DERIA5String,u.asn1.DERAbstractString),u.asn1.DERUTCTime=function(i){u.asn1.DERUTCTime.superclass.constructor.call(this,i),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},i!==void 0&&(i.str!==void 0?this.setString(i.str):typeof i=="string"&&i.match(/^[0-9]{12}Z$/)?this.setString(i):i.hex!==void 0?this.setStringHex(i.hex):i.date!==void 0&&this.setByDate(i.date))},O.lang.extend(u.asn1.DERUTCTime,u.asn1.DERAbstractTime),u.asn1.DERGeneralizedTime=function(i){u.asn1.DERGeneralizedTime.superclass.constructor.call(this,i),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},i!==void 0&&(i.str!==void 0?this.setString(i.str):typeof i=="string"&&i.match(/^[0-9]{14}Z$/)?this.setString(i):i.hex!==void 0?this.setStringHex(i.hex):i.date!==void 0&&this.setByDate(i.date),i.millis===!0&&(this.withMillis=!0))},O.lang.extend(u.asn1.DERGeneralizedTime,u.asn1.DERAbstractTime),u.asn1.DERSequence=function(i){u.asn1.DERSequence.superclass.constructor.call(this,i),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},O.lang.extend(u.asn1.DERSequence,u.asn1.DERAbstractStructured),u.asn1.DERSet=function(i){u.asn1.DERSet.superclass.constructor.call(this,i),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return this.sortFlag==1&&t.sort(),this.hV=t.join(""),this.hV},i!==void 0&&i.sortflag!==void 0&&i.sortflag==0&&(this.sortFlag=!1)},O.lang.extend(u.asn1.DERSet,u.asn1.DERAbstractStructured),u.asn1.DERTaggedObject=function(i){u.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.tag!==void 0&&(this.hT=i.tag),i.explicit!==void 0&&(this.isExplicit=i.explicit),i.obj!==void 0&&(this.asn1Object=i.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},O.lang.extend(u.asn1.DERTaggedObject,u.asn1.ASN1Object);var St,Lt,Jt=(St=function(i,t){return St=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])},St(i,t)},function(i,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function e(){this.constructor=i}St(i,t),i.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}),Ht=function(i){function t(e){var r=i.call(this)||this;return e&&(typeof e=="string"?r.parseKey(e):(t.hasPrivateKeyProperty(e)||t.hasPublicKeyProperty(e))&&r.parsePropertiesFrom(e)),r}return Jt(t,i),t.prototype.parseKey=function(e){try{var r=0,n=0,s=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?function(S){var T;if(N===void 0){var A="0123456789ABCDEF",W=` \f
\r	 \u2028\u2029`;for(N={},T=0;T<16;++T)N[A.charAt(T)]=T;for(A=A.toLowerCase(),T=10;T<16;++T)N[A.charAt(T)]=T;for(T=0;T<W.length;++T)N[W.charAt(T)]=-1}var _=[],H=0,ht=0;for(T=0;T<S.length;++T){var f=S.charAt(T);if(f=="=")break;if((f=N[f])!=-1){if(f===void 0)throw new Error("Illegal character at offset "+T);H|=f,++ht>=2?(_[_.length]=H,H=0,ht=0):H<<=4}}if(ht)throw new Error("Hex encoding incomplete: 4 bits missing");return _}(e):q.unarmor(e),o=zt.decode(s);if(o.sub.length===3&&(o=o.sub[2].sub[0]),o.sub.length===9){r=o.sub[1].getHexStringValue(),this.n=R(r,16),n=o.sub[2].getHexStringValue(),this.e=parseInt(n,16);var a=o.sub[3].getHexStringValue();this.d=R(a,16);var h=o.sub[4].getHexStringValue();this.p=R(h,16);var c=o.sub[5].getHexStringValue();this.q=R(c,16);var l=o.sub[6].getHexStringValue();this.dmp1=R(l,16);var b=o.sub[7].getHexStringValue();this.dmq1=R(b,16);var g=o.sub[8].getHexStringValue();this.coeff=R(g,16)}else{if(o.sub.length!==2)return!1;if(o.sub[0].sub){var x=o.sub[1].sub[0];r=x.sub[0].getHexStringValue(),this.n=R(r,16),n=x.sub[1].getHexStringValue(),this.e=parseInt(n,16)}else r=o.sub[0].getHexStringValue(),this.n=R(r,16),n=o.sub[1].getHexStringValue(),this.e=parseInt(n,16)}return!0}catch{return!1}},t.prototype.getPrivateBaseKey=function(){var e={array:[new u.asn1.DERInteger({int:0}),new u.asn1.DERInteger({bigint:this.n}),new u.asn1.DERInteger({int:this.e}),new u.asn1.DERInteger({bigint:this.d}),new u.asn1.DERInteger({bigint:this.p}),new u.asn1.DERInteger({bigint:this.q}),new u.asn1.DERInteger({bigint:this.dmp1}),new u.asn1.DERInteger({bigint:this.dmq1}),new u.asn1.DERInteger({bigint:this.coeff})]};return new u.asn1.DERSequence(e).getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return P(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var e=new u.asn1.DERSequence({array:[new u.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new u.asn1.DERNull]}),r=new u.asn1.DERSequence({array:[new u.asn1.DERInteger({bigint:this.n}),new u.asn1.DERInteger({int:this.e})]}),n=new u.asn1.DERBitString({hex:"00"+r.getEncodedHex()});return new u.asn1.DERSequence({array:[e,n]}).getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return P(this.getPublicBaseKey())},t.wordwrap=function(e,r){if(!e)return e;var n="(.{1,"+(r=r||64)+`})( +|$
?)|(.{1,`+r+"})";return e.match(RegExp(n,"g")).join(`
`)},t.prototype.getPrivateKey=function(){var e=`-----BEGIN RSA PRIVATE KEY-----
`;return(e+=t.wordwrap(this.getPrivateBaseKeyB64())+`
`)+"-----END RSA PRIVATE KEY-----"},t.prototype.getPublicKey=function(){var e=`-----BEGIN PUBLIC KEY-----
`;return(e+=t.wordwrap(this.getPublicBaseKeyB64())+`
`)+"-----END PUBLIC KEY-----"},t.hasPublicKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)},t}($t),Kt=Q(155),Xt=Kt!==void 0?(Lt=Kt.env)===null||Lt===void 0?void 0:"3.3.2":void 0;const te=function(){function i(t){t===void 0&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return i.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Ht(t)},i.prototype.setPrivateKey=function(t){this.setKey(t)},i.prototype.setPublicKey=function(t){this.setKey(t)},i.prototype.decrypt=function(t){try{return this.getKey().decrypt(K(t))}catch{return!1}},i.prototype.encrypt=function(t){try{return P(this.getKey().encrypt(t))}catch{return!1}},i.prototype.sign=function(t,e,r){try{return P(this.getKey().sign(t,e,r))}catch{return!1}},i.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,K(e),r)}catch{return!1}},i.prototype.getKey=function(t){if(!this.key){if(this.key=new Ht,t&&{}.toString.call(t)==="[object Function]")return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},i.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},i.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},i.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},i.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},i.version=Xt,i}()})(),gt.default})())})(Ft);var de=Ft.exports;const Ut=re(de),me=`MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH
nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==`,ve=`MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY
7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN
PuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA
kM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow
cSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv
DQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh
YhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3
UP8iWi1Qw0Y=`;function ye(st){const J=new Ut;return J.setPublicKey(me),J.encrypt(st)}function be(st){const J=new Ut;return J.setPrivateKey(ve),J.decrypt(st)}const Te={class:"login"},Se={class:"title"},Ee={class:"login-code"},we=["src"],De={key:0},xe={key:1},Ae={__name:"login",setup(st){const J="捷运停车管理系统",pt=ne(),it=se(),Q=oe(),{proxy:gt}=ae(),m=ft({username:"admin",password:"admin123",rememberMe:!1,code:"",uuid:""}),y={username:[{required:!0,trigger:"blur",message:"请输入您的账号"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},D=ft(""),v=ft(!1),U=ft(!0),Y=ft(void 0);he(it,E=>{Y.value=E.query&&E.query.redirect},{immediate:!0});function X(){gt.$refs.loginRef.validate(E=>{E&&(v.value=!0,m.value.rememberMe?($.set("username",m.value.username,{expires:30}),$.set("password",ye(m.value.password),{expires:30}),$.set("rememberMe",m.value.rememberMe,{expires:30})):($.remove("username"),$.remove("password"),$.remove("rememberMe")),pt.login(m.value).then(()=>{const B=it.query,P=Object.keys(B).reduce((K,I)=>(I!=="redirect"&&(K[I]=B[I]),K),{});Q.push({path:Y.value||"/",query:P})}).catch(()=>{v.value=!1,U.value&&L()}))})}function L(){ge().then(E=>{U.value=E.captchaEnabled===void 0?!0:E.captchaEnabled,U.value&&(D.value="data:image/gif;base64,"+E.img,m.value.uuid=E.uuid)})}function N(){const E=$.get("username"),B=$.get("password"),P=$.get("rememberMe");m.value={username:E===void 0?m.value.username:E,password:B===void 0?m.value.password:be(B),rememberMe:P===void 0?!1:!!P}}return L(),N(),(E,B)=>{const P=ut("svg-icon"),K=ut("el-input"),I=ut("el-form-item"),q=ut("el-checkbox"),w=ut("el-button"),M=ut("el-form");return wt(),Rt("div",Te,[F(M,{ref:"loginRef",model:j(m),rules:y,class:"login-form"},{default:G(()=>[Ot("h3",Se,le(j(J)),1),F(I,{prop:"username"},{default:G(()=>[F(K,{modelValue:j(m).username,"onUpdate:modelValue":B[0]||(B[0]=V=>j(m).username=V),type:"text",size:"large","auto-complete":"off",placeholder:"账号"},{prefix:G(()=>[F(P,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),F(I,{prop:"password"},{default:G(()=>[F(K,{modelValue:j(m).password,"onUpdate:modelValue":B[1]||(B[1]=V=>j(m).password=V),type:"password",size:"large","auto-complete":"off",placeholder:"密码",onKeyup:kt(X,["enter"])},{prefix:G(()=>[F(P,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),j(U)?(wt(),ue(I,{key:0,prop:"code"},{default:G(()=>[F(K,{modelValue:j(m).code,"onUpdate:modelValue":B[2]||(B[2]=V=>j(m).code=V),size:"large","auto-complete":"off",placeholder:"验证码",style:{width:"63%"},onKeyup:kt(X,["enter"])},{prefix:G(()=>[F(P,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"]),Ot("div",Ee,[Ot("img",{src:j(D),onClick:L,class:"login-code-img"},null,8,we)])]),_:1})):ce("",!0),F(q,{modelValue:j(m).rememberMe,"onUpdate:modelValue":B[3]||(B[3]=V=>j(m).rememberMe=V),style:{margin:"0px 0px 25px 0px"}},{default:G(()=>[fe("记住密码")]),_:1},8,["modelValue"]),F(I,{style:{width:"100%"}},{default:G(()=>[F(w,{loading:j(v),size:"large",type:"primary",style:{width:"100%"},onClick:pe(X,["prevent"])},{default:G(()=>[j(v)?(wt(),Rt("span",xe,"登 录 中...")):(wt(),Rt("span",De,"登 录"))]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])}}},Ve=ie(Ae,[["__scopeId","data-v-4a16cb58"]]);export{Ve as default};
