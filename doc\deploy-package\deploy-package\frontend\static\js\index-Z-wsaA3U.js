import{O as Ce,d as we,r as b,C as ke,T as Se,x as De,e as r,Q as Z,c as E,o as p,R as T,f as e,S as Ue,h as l,m as ee,l as o,L as z,M as I,j as f,n as d,t as w,i as $,a0 as le,k as S}from"./index-D_FV2sri.js";import{l as xe,g as Ne,d as Ye,a as Ee,c as $e,e as Ae,p as Qe,b as qe,u as Re,f as Le}from"./template-Dro944Br.js";import{C as ae}from"./index-C9zsUI-h.js";const Me={class:"app-container"},He=$("br",null,null,-1),Pe={class:"dialog-footer"},ze=Ce({name:"CouponTemplate"}),Oe=Object.assign(ze,{components:{CustomPagination:ae}},{setup(Ie){const{proxy:_}=we(),{coupon_type:A,coupon_status:B}=_.useDict("coupon_type","coupon_status"),Q=b(),D=b(),F=b([]),h=b(!1),q=b(!0),R=b(!0),L=b([]),K=b(!0),O=b(!0),j=b(0),M=b(""),te=ke({form:{},queryParams:{pageNum:1,pageSize:10,couponCode:null,couponName:null,couponType:null,status:null},rules:{couponName:[{required:!0,message:"优惠券名称不能为空",trigger:"blur"}],couponType:[{required:!0,message:"优惠券类型不能为空",trigger:"change"}],discountType:[{required:!0,message:"折扣方式不能为空",trigger:"change"}],discountValue:[{required:!0,message:"优惠值不能为空",trigger:"blur"}],userLimit:[{required:!0,message:"每人限领数量不能为空",trigger:"blur"}],issueStartTime:[{required:!0,message:"发放开始时间不能为空",trigger:"change"}],issueEndTime:[{required:!0,message:"发放结束时间不能为空",trigger:"change"}],validType:[{required:!0,message:"有效期类型不能为空",trigger:"change"}]}}),{queryParams:m,form:u,rules:oe}=Se(te);function ue(n){return n.discountType===1?n.discountValue+"元":n.discountType===2?n.discountValue+"%":n.discountValue}function v(){q.value=!0,xe(m.value).then(n=>{F.value=n.rows,j.value=n.total,q.value=!1})}function ne(){h.value=!1,H()}function H(){u.value={id:null,couponCode:null,couponName:null,couponType:null,discountType:1,discountValue:null,thresholdAmount:0,maxDiscountAmount:null,applicableScope:1,warehouseIds:null,memberLevels:null,issueType:1,totalQuantity:null,userLimit:1,issueStartTime:null,issueEndTime:null,validType:1,validDays:null,validStartTime:null,validEndTime:null,status:1,description:null,usageRules:null,sortOrder:0},D.value&&D.value.resetFields()}function N(){m.value.pageNum=1,v()}function de(){Q.value&&Q.value.resetFields(),N()}function se(n){L.value=n.map(t=>t.id),K.value=n.length!==1,O.value=!n.length}function pe(){H(),h.value=!0,M.value="添加优惠券模板"}function G(n){H();const t=n.id||L.value;Ne(t).then(c=>{u.value=c.data,h.value=!0,M.value="修改优惠券模板"})}function re(){D.value&&D.value.validate(n=>{n&&(u.value.id!=null?Re(u.value).then(t=>{_.$modal.msgSuccess("修改成功"),h.value=!1,v()}):Le(u.value).then(t=>{_.$modal.msgSuccess("新增成功"),h.value=!1,v()}))})}function J(n){const t=n.id||L.value;_.$modal.confirm('是否确认删除优惠券模板编号为"'+t+'"的数据项？').then(function(){return Ye(t)}).then(()=>{v(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ie(){_.download("system/coupon/template/export",{...m.value},`template_${new Date().getTime()}.xlsx`)}function me(n){n===4&&(u.value.discountType=1,u.value.thresholdAmount=0)}function ce(n){n===1?u.value.validDays=null:(u.value.validStartTime=null,u.value.validEndTime=null)}function fe(n,t){switch(n){case"enable":qe(t.id).then(()=>{_.$modal.msgSuccess("启用成功"),v()});break;case"pause":Qe(t.id).then(()=>{_.$modal.msgSuccess("暂停成功"),v()});break;case"end":Ae(t.id).then(()=>{_.$modal.msgSuccess("结束成功"),v()});break;case"copy":$e(t.id).then(()=>{_.$modal.msgSuccess("复制成功"),v()});break;case"statistics":Ee(t.id).then(c=>{_.$modal.msgInfo(`统计信息：发放${c.data.issuedQuantity}张，使用${c.data.usedQuantity}张`)});break}}return De(()=>{v()}),(n,t)=>{const c=r("el-input"),s=r("el-form-item"),C=r("el-option"),U=r("el-select"),y=r("el-button"),W=r("el-form"),i=r("el-col"),_e=r("right-toolbar"),V=r("el-row"),g=r("el-table-column"),X=r("dict-tag"),ve=r("arrow-down"),ye=r("el-icon"),x=r("el-dropdown-item"),ge=r("el-dropdown-menu"),be=r("el-dropdown"),Ve=r("el-table"),Y=r("el-date-picker"),Te=r("el-dialog"),k=Z("hasPermi"),he=Z("loading");return p(),E("div",Me,[T(e(W,{model:o(m),ref_key:"queryRef",ref:Q,inline:!0,"label-width":"68px"},{default:l(()=>[e(s,{label:"优惠券编码",prop:"couponCode"},{default:l(()=>[e(c,{modelValue:o(m).couponCode,"onUpdate:modelValue":t[0]||(t[0]=a=>o(m).couponCode=a),placeholder:"请输入优惠券编码",clearable:"",onKeyup:ee(N,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"优惠券名称",prop:"couponName"},{default:l(()=>[e(c,{modelValue:o(m).couponName,"onUpdate:modelValue":t[1]||(t[1]=a=>o(m).couponName=a),placeholder:"请输入优惠券名称",clearable:"",onKeyup:ee(N,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"优惠券类型",prop:"couponType"},{default:l(()=>[e(U,{modelValue:o(m).couponType,"onUpdate:modelValue":t[2]||(t[2]=a=>o(m).couponType=a),placeholder:"请选择优惠券类型",clearable:"",style:{width:"180px"}},{default:l(()=>[(p(!0),E(z,null,I(o(A),a=>(p(),f(C,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"状态",prop:"status"},{default:l(()=>[e(U,{modelValue:o(m).status,"onUpdate:modelValue":t[3]||(t[3]=a=>o(m).status=a),placeholder:"请选择状态",clearable:"",style:{width:"180px"}},{default:l(()=>[(p(!0),E(z,null,I(o(B),a=>(p(),f(C,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,null,{default:l(()=>[e(y,{type:"primary",icon:"Search",onClick:N},{default:l(()=>[d("搜索")]),_:1}),e(y,{icon:"Refresh",onClick:de},{default:l(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ue,R.value]]),e(V,{gutter:10,class:"mb8"},{default:l(()=>[e(i,{span:1.5},{default:l(()=>[T((p(),f(y,{type:"primary",plain:"",icon:"Plus",onClick:pe},{default:l(()=>[d("新增")]),_:1})),[[k,["coupon:template:add"]]])]),_:1}),e(i,{span:1.5},{default:l(()=>[T((p(),f(y,{type:"success",plain:"",icon:"Edit",disabled:K.value,onClick:G},{default:l(()=>[d("修改")]),_:1},8,["disabled"])),[[k,["coupon:template:edit"]]])]),_:1}),e(i,{span:1.5},{default:l(()=>[T((p(),f(y,{type:"danger",plain:"",icon:"Delete",disabled:O.value,onClick:J},{default:l(()=>[d("删除")]),_:1},8,["disabled"])),[[k,["coupon:template:remove"]]])]),_:1}),e(i,{span:1.5},{default:l(()=>[T((p(),f(y,{type:"warning",plain:"",icon:"Download",onClick:ie},{default:l(()=>[d("导出")]),_:1})),[[k,["coupon:template:export"]]])]),_:1}),e(_e,{showSearch:R.value,"onUpdate:showSearch":t[4]||(t[4]=a=>R.value=a),onQueryTable:v},null,8,["showSearch"])]),_:1}),T((p(),f(Ve,{data:F.value,onSelectionChange:se},{default:l(()=>[e(g,{type:"selection",width:"55",align:"center"}),e(g,{label:"优惠券编码",align:"center",prop:"couponCode"}),e(g,{label:"优惠券名称",align:"center",prop:"couponName"}),e(g,{label:"优惠券类型",align:"center",prop:"couponType"},{default:l(a=>[e(X,{options:o(A),value:a.row.couponType},null,8,["options","value"])]),_:1}),e(g,{label:"优惠值",align:"center",prop:"discountValue"},{default:l(a=>[d(w(ue(a.row)),1)]),_:1}),e(g,{label:"使用门槛",align:"center",prop:"thresholdAmount"},{default:l(a=>[d(w(a.row.thresholdAmount>0?"满"+a.row.thresholdAmount+"元":"无门槛"),1)]),_:1}),e(g,{label:"发放数量",align:"center"},{default:l(a=>[d(w(a.row.issuedQuantity)+" / "+w(a.row.totalQuantity||"无限制"),1)]),_:1}),e(g,{label:"使用数量",align:"center",prop:"usedQuantity"}),e(g,{label:"状态",align:"center",prop:"status"},{default:l(a=>[e(X,{options:o(B),value:a.row.status},null,8,["options","value"])]),_:1}),e(g,{label:"发放时间",align:"center",prop:"issueStartTime",width:"180"},{default:l(a=>[$("span",null,w(o(le)(a.row.issueStartTime,"{y}-{m}-{d}")),1),He,$("span",null,"至 "+w(o(le)(a.row.issueEndTime,"{y}-{m}-{d}")),1)]),_:1}),e(g,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(a=>[T((p(),f(y,{link:"",type:"primary",icon:"Edit",onClick:P=>G(a.row)},{default:l(()=>[d("修改")]),_:2},1032,["onClick"])),[[k,["coupon:template:edit"]]]),T((p(),f(y,{link:"",type:"primary",icon:"Delete",onClick:P=>J(a.row)},{default:l(()=>[d("删除")]),_:2},1032,["onClick"])),[[k,["coupon:template:remove"]]]),e(be,{onCommand:P=>fe(P,a.row)},{dropdown:l(()=>[e(ge,null,{default:l(()=>[a.row.status!==2?(p(),f(x,{key:0,command:"enable"},{default:l(()=>[d("启用")]),_:1})):S("",!0),a.row.status===2?(p(),f(x,{key:1,command:"pause"},{default:l(()=>[d("暂停")]),_:1})):S("",!0),a.row.status!==3?(p(),f(x,{key:2,command:"end"},{default:l(()=>[d("结束")]),_:1})):S("",!0),e(x,{command:"copy"},{default:l(()=>[d("复制")]),_:1}),e(x,{command:"statistics"},{default:l(()=>[d("统计")]),_:1})]),_:2},1024)]),default:l(()=>[e(y,{link:"",type:"primary"},{default:l(()=>[d(" 更多"),e(ye,{class:"el-icon--right"},{default:l(()=>[e(ve)]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[he,q.value]]),e(ae,{total:j.value,"current-page":o(m).pageNum,"onUpdate:currentPage":t[5]||(t[5]=a=>o(m).pageNum=a),"page-size":o(m).pageSize,"onUpdate:pageSize":t[6]||(t[6]=a=>o(m).pageSize=a),onPagination:v},null,8,["total","current-page","page-size"]),e(Te,{title:M.value,modelValue:h.value,"onUpdate:modelValue":t[23]||(t[23]=a=>h.value=a),width:"800px","append-to-body":""},{footer:l(()=>[$("div",Pe,[e(y,{type:"primary",onClick:re},{default:l(()=>[d("确 定")]),_:1}),e(y,{onClick:ne},{default:l(()=>[d("取 消")]),_:1})])]),default:l(()=>[e(W,{ref_key:"templateRef",ref:D,model:o(u),rules:o(oe),"label-width":"120px"},{default:l(()=>[e(V,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"优惠券名称",prop:"couponName"},{default:l(()=>[e(c,{modelValue:o(u).couponName,"onUpdate:modelValue":t[7]||(t[7]=a=>o(u).couponName=a),placeholder:"请输入优惠券名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"优惠券类型",prop:"couponType"},{default:l(()=>[e(U,{modelValue:o(u).couponType,"onUpdate:modelValue":t[8]||(t[8]=a=>o(u).couponType=a),placeholder:"请选择优惠券类型",onChange:me},{default:l(()=>[(p(!0),E(z,null,I(o(A),a=>(p(),f(C,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"折扣方式",prop:"discountType"},{default:l(()=>[e(U,{modelValue:o(u).discountType,"onUpdate:modelValue":t[9]||(t[9]=a=>o(u).discountType=a),placeholder:"请选择折扣方式"},{default:l(()=>[e(C,{label:"固定金额",value:1}),e(C,{label:"百分比折扣",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"优惠值",prop:"discountValue"},{default:l(()=>[e(c,{modelValue:o(u).discountValue,"onUpdate:modelValue":t[10]||(t[10]=a=>o(u).discountValue=a),placeholder:"请输入优惠值",type:"number"},{append:l(()=>[d(w(o(u).discountType===1?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"使用门槛",prop:"thresholdAmount"},{default:l(()=>[e(c,{modelValue:o(u).thresholdAmount,"onUpdate:modelValue":t[11]||(t[11]=a=>o(u).thresholdAmount=a),placeholder:"请输入使用门槛金额",type:"number"},{append:l(()=>[d("元")]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(u).discountType===2?(p(),f(i,{key:0,span:12},{default:l(()=>[e(s,{label:"最高优惠金额",prop:"maxDiscountAmount"},{default:l(()=>[e(c,{modelValue:o(u).maxDiscountAmount,"onUpdate:modelValue":t[12]||(t[12]=a=>o(u).maxDiscountAmount=a),placeholder:"请输入最高优惠金额",type:"number"},{append:l(()=>[d("元")]),_:1},8,["modelValue"])]),_:1})]),_:1})):S("",!0)]),_:1}),e(V,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"发行总量",prop:"totalQuantity"},{default:l(()=>[e(c,{modelValue:o(u).totalQuantity,"onUpdate:modelValue":t[13]||(t[13]=a=>o(u).totalQuantity=a),placeholder:"请输入发行总量，留空表示无限制",type:"number"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"每人限领数量",prop:"userLimit"},{default:l(()=>[e(c,{modelValue:o(u).userLimit,"onUpdate:modelValue":t[14]||(t[14]=a=>o(u).userLimit=a),placeholder:"请输入每人限领数量",type:"number"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"发放开始时间",prop:"issueStartTime"},{default:l(()=>[e(Y,{modelValue:o(u).issueStartTime,"onUpdate:modelValue":t[15]||(t[15]=a=>o(u).issueStartTime=a),type:"datetime",placeholder:"选择发放开始时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"发放结束时间",prop:"issueEndTime"},{default:l(()=>[e(Y,{modelValue:o(u).issueEndTime,"onUpdate:modelValue":t[16]||(t[16]=a=>o(u).issueEndTime=a),type:"datetime",placeholder:"选择发放结束时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"有效期类型",prop:"validType"},{default:l(()=>[e(U,{modelValue:o(u).validType,"onUpdate:modelValue":t[17]||(t[17]=a=>o(u).validType=a),placeholder:"请选择有效期类型",onChange:ce},{default:l(()=>[e(C,{label:"固定时间段",value:1}),e(C,{label:"领取后N天有效",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(u).validType===2?(p(),f(i,{key:0,span:12},{default:l(()=>[e(s,{label:"有效天数",prop:"validDays"},{default:l(()=>[e(c,{modelValue:o(u).validDays,"onUpdate:modelValue":t[18]||(t[18]=a=>o(u).validDays=a),placeholder:"请输入有效天数",type:"number"},{append:l(()=>[d("天")]),_:1},8,["modelValue"])]),_:1})]),_:1})):S("",!0)]),_:1}),o(u).validType===1?(p(),f(V,{key:0},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"有效期开始时间",prop:"validStartTime"},{default:l(()=>[e(Y,{modelValue:o(u).validStartTime,"onUpdate:modelValue":t[19]||(t[19]=a=>o(u).validStartTime=a),type:"datetime",placeholder:"选择有效期开始时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"有效期结束时间",prop:"validEndTime"},{default:l(()=>[e(Y,{modelValue:o(u).validEndTime,"onUpdate:modelValue":t[20]||(t[20]=a=>o(u).validEndTime=a),type:"datetime",placeholder:"选择有效期结束时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):S("",!0),e(s,{label:"优惠券描述",prop:"description"},{default:l(()=>[e(c,{modelValue:o(u).description,"onUpdate:modelValue":t[21]||(t[21]=a=>o(u).description=a),type:"textarea",placeholder:"请输入优惠券描述",rows:3},null,8,["modelValue"])]),_:1}),e(s,{label:"使用规则说明",prop:"usageRules"},{default:l(()=>[e(c,{modelValue:o(u).usageRules,"onUpdate:modelValue":t[22]||(t[22]=a=>o(u).usageRules=a),type:"textarea",placeholder:"请输入使用规则说明",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Oe as default};
