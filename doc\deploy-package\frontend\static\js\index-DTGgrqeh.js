import{v as R,_ as Se,O as Me,d as Ue,r as c,C as qe,T as $e,y as Oe,e as p,Q as te,c as q,o as u,R as k,k as C,f as e,S as We,l as o,h as a,L as B,M as E,j as m,m as oe,n as d,t as b,P as re,i as ne}from"./index-Cox2ohSR.js";import{o as Re}from"./operator-DsAnS6bq.js";import{o as ue,c as A}from"./warehouse-D8FfdMFO.js";import{C as se}from"./index-Cy2yB1cz.js";function Te(h){return R({url:"/system/platform/warehouseManager/list",method:"get",params:h})}function De(h){return R({url:"/system/platform/warehouseManager/"+h,method:"get"})}function Qe(h){return R({url:"/system/platform/warehouseManager",method:"post",data:h})}function ze(h){return R({url:"/system/platform/warehouseManager",method:"put",data:h})}function Be(h){return R({url:"/system/platform/warehouseManager/"+h,method:"delete"})}const Ee={class:"app-container"},Fe={key:0,class:"query-condition-tags"},Le={class:"dialog-footer"},Ke=Me({name:"WarehouseManager"}),je=Object.assign(Ke,{components:{CustomPagination:se}},{setup(h){const{proxy:y}=Ue(),G=c([]),N=c([]),$=c([]),x=c([]),P=c(!1),T=c(!0),D=c(!0),F=c([]),H=c(!0),J=c(!0),X=c(0),L=c(""),de=qe({form:{},queryParams:{pageNum:1,pageSize:10,operatorId:null,warehouseId:null,managerName:null,managerPhone:null,isPrimary:null,status:null},rules:{operatorId:[{required:!0,message:"所属运营商不能为空",trigger:"change"}],warehouseId:[{required:!0,message:"负责场库不能为空",trigger:"change"}],managerName:[{required:!0,message:"管理人员姓名不能为空",trigger:"blur"}],managerPhone:[{required:!0,message:"管理人员电话不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:r,form:s,rules:ie}=$e(de),Y=Oe(()=>!!(r.value.operatorId||r.value.warehouseId||r.value.managerName||r.value.managerPhone||r.value.isPrimary!==null&&r.value.isPrimary!==void 0||r.value.status!==null&&r.value.status!==void 0));function me(n){const t=N.value.find(f=>f.id==n);return t?t.companyName:"未知运营商"}function pe(n){const t=$.value.find(f=>f.id==n);return t?t.warehouseName:"未知场库"}function fe(n){return n===1?"是":"否"}function ce(n){return n===1?"正常":"停用"}function S(n){r.value[n]=null,n==="operatorId"&&(r.value.complexId=null,O()),M()}function ge(){r.value.operatorId=null,r.value.complexId=null,r.value.managerName=null,r.value.managerPhone=null,r.value.isPrimary=null,r.value.status=null,O(),M()}function I(){T.value=!0,Te(r.value).then(n=>{G.value=n.rows,X.value=n.total,T.value=!1}).catch(n=>{console.error("获取场库管理人员列表失败:",n),T.value=!1})}function K(){return Re().then(n=>(N.value=n.data,n)).catch(n=>{throw console.error("获取运营商选项失败:",n),n})}function O(){ue().then(n=>{$.value=n.data})}function he(n){r.value.warehouseId=null,n?A(n).then(t=>{$.value=t.data}):O()}function ve(n){s.value.warehouseId=null,n?A(n).then(t=>{x.value=t.data}):x.value=[]}function _e(){P.value=!1,j()}function j(){s.value={id:null,operatorId:null,warehouseId:null,managerName:null,managerPhone:null,isPrimary:0,status:1,remark:null},x.value=[],y.resetForm("managerRef")}function M(){r.value.pageNum=1,I()}function ye(){y.resetForm("queryRef"),$.value=[],O(),M()}function we(n){F.value=n.map(t=>t.id),H.value=n.length!=1,J.value=!n.length}function be(){j(),N.value.length===0&&K(),P.value=!0,L.value="添加场库管理人员信息"}function Z(n){j();const t=n.id||F.value,f=N.value.length===0?K():Promise.resolve();Promise.resolve(f).then(()=>De(t)).then(v=>(s.value=v.data,s.value.operatorId?A(s.value.operatorId).then(i=>{x.value=i.data}):ue().then(i=>{x.value=i.data}))).then(()=>{P.value=!0,L.value="修改场库管理人员信息"}).catch(v=>{console.error("修改操作失败:",v),y.$modal.msgError("获取数据失败，请重试")})}function Pe(){y.$refs.managerRef.validate(n=>{n&&(s.value.id!=null?ze(s.value).then(t=>{y.$modal.msgSuccess("修改成功"),P.value=!1,I()}):Qe(s.value).then(t=>{y.$modal.msgSuccess("新增成功"),P.value=!1,I()}))})}function ee(n){const t=n.id||F.value;y.$modal.confirm('是否确认删除场库管理人员信息编号为"'+t+'"的数据项？').then(function(){return Be(t)}).then(()=>{I(),y.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Ve(){y.download("system/platform/warehouseManager/export",{...r.value},`warehouseManager_${new Date().getTime()}.xlsx`)}return I(),K(),O(),(n,t)=>{const f=p("el-option"),v=p("el-select"),i=p("el-form-item"),W=p("el-input"),g=p("el-button"),ae=p("el-form"),V=p("el-tag"),_=p("el-col"),ke=p("right-toolbar"),Q=p("el-row"),w=p("el-table-column"),Ce=p("el-table"),z=p("el-radio"),le=p("el-radio-group"),Ie=p("el-dialog"),U=te("hasPermi"),Ne=te("loading");return u(),q("div",Ee,[k(e(ae,{model:o(r),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[e(i,{label:"运营商",prop:"operatorId"},{default:a(()=>[e(v,{modelValue:o(r).operatorId,"onUpdate:modelValue":t[0]||(t[0]=l=>o(r).operatorId=l),placeholder:"请选择运营商",clearable:"",onChange:he,style:{width:"280px"},"popper-class":"operator-select-dropdown"},{default:a(()=>[(u(!0),q(B,null,E(o(N),l=>(u(),m(f,{key:l.id,label:l.companyName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"场库",prop:"warehouseId"},{default:a(()=>[e(v,{modelValue:o(r).warehouseId,"onUpdate:modelValue":t[1]||(t[1]=l=>o(r).warehouseId=l),placeholder:"请选择场库",clearable:"",style:{width:"200px"},"popper-class":"warehouse-select-dropdown"},{default:a(()=>[(u(!0),q(B,null,E(o($),l=>(u(),m(f,{key:l.id,label:l.warehouseName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"管理人员",prop:"managerName"},{default:a(()=>[e(W,{modelValue:o(r).managerName,"onUpdate:modelValue":t[2]||(t[2]=l=>o(r).managerName=l),placeholder:"请输入管理人员姓名",clearable:"",onKeyup:oe(M,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"联系电话",prop:"managerPhone"},{default:a(()=>[e(W,{modelValue:o(r).managerPhone,"onUpdate:modelValue":t[3]||(t[3]=l=>o(r).managerPhone=l),placeholder:"请输入管理人员电话",clearable:"",onKeyup:oe(M,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"主要负责人",prop:"isPrimary"},{default:a(()=>[e(v,{modelValue:o(r).isPrimary,"onUpdate:modelValue":t[4]||(t[4]=l=>o(r).isPrimary=l),placeholder:"是否主要负责人",clearable:"",style:{width:"150px"}},{default:a(()=>[e(f,{label:"是",value:1}),e(f,{label:"否",value:0})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"状态",prop:"status"},{default:a(()=>[e(v,{modelValue:o(r).status,"onUpdate:modelValue":t[5]||(t[5]=l=>o(r).status=l),placeholder:"状态",clearable:"",style:{width:"120px"}},{default:a(()=>[e(f,{label:"正常",value:1}),e(f,{label:"停用",value:0})]),_:1},8,["modelValue"])]),_:1}),e(i,null,{default:a(()=>[e(g,{type:"primary",icon:"Search",onClick:M},{default:a(()=>[d("搜索")]),_:1}),e(g,{icon:"Refresh",onClick:ye},{default:a(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[We,o(D)]]),o(Y)?(u(),q("div",Fe,[o(r).operatorId?(u(),m(V,{key:0,closable:"",onClose:t[6]||(t[6]=l=>S("operatorId")),class:"query-tag"},{default:a(()=>[d(" 所属运营商: "+b(me(o(r).operatorId)),1)]),_:1})):C("",!0),o(r).complexId?(u(),m(V,{key:1,closable:"",onClose:t[7]||(t[7]=l=>S("complexId")),class:"query-tag"},{default:a(()=>[d(" 负责场库: "+b(pe(o(r).complexId)),1)]),_:1})):C("",!0),o(r).managerName?(u(),m(V,{key:2,closable:"",onClose:t[8]||(t[8]=l=>S("managerName")),class:"query-tag"},{default:a(()=>[d(" 管理人员: "+b(o(r).managerName),1)]),_:1})):C("",!0),o(r).managerPhone?(u(),m(V,{key:3,closable:"",onClose:t[9]||(t[9]=l=>S("managerPhone")),class:"query-tag"},{default:a(()=>[d(" 联系电话: "+b(o(r).managerPhone),1)]),_:1})):C("",!0),o(r).isPrimary!==null&&o(r).isPrimary!==void 0?(u(),m(V,{key:4,closable:"",onClose:t[10]||(t[10]=l=>S("isPrimary")),class:"query-tag"},{default:a(()=>[d(" 主要负责人: "+b(fe(o(r).isPrimary)),1)]),_:1})):C("",!0),o(r).status!==null&&o(r).status!==void 0?(u(),m(V,{key:5,closable:"",onClose:t[11]||(t[11]=l=>S("status")),class:"query-tag"},{default:a(()=>[d(" 状态: "+b(ce(o(r).status)),1)]),_:1})):C("",!0),o(Y)?(u(),m(g,{key:6,type:"text",icon:"Refresh",onClick:ge,class:"clear-all-btn"},{default:a(()=>[d(" 清空 ")]),_:1})):C("",!0)])):C("",!0),e(Q,{gutter:10,class:"mb8"},{default:a(()=>[e(_,{span:1.5},{default:a(()=>[k((u(),m(g,{type:"primary",plain:"",icon:"Plus",onClick:be},{default:a(()=>[d("新增")]),_:1})),[[U,["platform:warehouseManager:add"]]])]),_:1}),e(_,{span:1.5},{default:a(()=>[k((u(),m(g,{type:"success",plain:"",icon:"Edit",disabled:o(H),onClick:Z},{default:a(()=>[d("修改")]),_:1},8,["disabled"])),[[U,["platform:warehouseManager:edit"]]])]),_:1}),e(_,{span:1.5},{default:a(()=>[k((u(),m(g,{type:"danger",plain:"",icon:"Delete",disabled:o(J),onClick:ee},{default:a(()=>[d("删除")]),_:1},8,["disabled"])),[[U,["platform:warehouseManager:remove"]]])]),_:1}),e(_,{span:1.5},{default:a(()=>[k((u(),m(g,{type:"warning",plain:"",icon:"Download",onClick:Ve},{default:a(()=>[d("导出")]),_:1})),[[U,["platform:warehouseManager:export"]]])]),_:1}),e(ke,{showSearch:o(D),"onUpdate:showSearch":t[12]||(t[12]=l=>re(D)?D.value=l:null),onQueryTable:I},null,8,["showSearch"])]),_:1}),k((u(),m(Ce,{data:o(G),onSelectionChange:we},{default:a(()=>[e(w,{type:"selection",width:"55",align:"center"}),e(w,{label:"所属运营商",align:"center",prop:"operatorName","show-overflow-tooltip":!0}),e(w,{label:"负责场库",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),e(w,{label:"管理人员",align:"center",prop:"managerName"}),e(w,{label:"联系电话",align:"center",prop:"managerPhone"}),e(w,{label:"主要负责人",align:"center",prop:"isPrimary",width:"140"},{default:a(l=>[e(V,{type:l.row.isPrimary===1?"success":"info"},{default:a(()=>[d(b(l.row.isPrimary===1?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),e(w,{label:"状态",align:"center",prop:"status",width:"100"},{default:a(l=>[e(V,{type:l.row.status===1?"success":"danger"},{default:a(()=>[d(b(l.row.status===1?"正常":"停用"),1)]),_:2},1032,["type"])]),_:1}),e(w,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(l=>[ne("span",null,b(n.parseTime(l.row.createTime)),1)]),_:1}),e(w,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(l=>[k((u(),m(g,{link:"",type:"primary",icon:"Edit",onClick:xe=>Z(l.row)},{default:a(()=>[d("修改")]),_:2},1032,["onClick"])),[[U,["platform:warehouseManager:edit"]]]),k((u(),m(g,{link:"",type:"primary",icon:"Delete",onClick:xe=>ee(l.row)},{default:a(()=>[d("删除")]),_:2},1032,["onClick"])),[[U,["platform:warehouseManager:remove"]]])]),_:1})]),_:1},8,["data"])),[[Ne,o(T)]]),e(se,{total:o(X),"current-page":o(r).pageNum,"onUpdate:currentPage":t[13]||(t[13]=l=>o(r).pageNum=l),"page-size":o(r).pageSize,"onUpdate:pageSize":t[14]||(t[14]=l=>o(r).pageSize=l),onPagination:I},null,8,["total","current-page","page-size"]),e(Ie,{title:o(L),modelValue:o(P),"onUpdate:modelValue":t[22]||(t[22]=l=>re(P)?P.value=l:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[ne("div",Le,[e(g,{type:"primary",onClick:Pe},{default:a(()=>[d("确 定")]),_:1}),e(g,{onClick:_e},{default:a(()=>[d("取 消")]),_:1})])]),default:a(()=>[e(ae,{ref:"managerRef",model:o(s),rules:o(ie),"label-width":"120px"},{default:a(()=>[e(Q,null,{default:a(()=>[e(_,{span:12},{default:a(()=>[e(i,{label:"所属运营商",prop:"operatorId"},{default:a(()=>[e(v,{modelValue:o(s).operatorId,"onUpdate:modelValue":t[15]||(t[15]=l=>o(s).operatorId=l),placeholder:"请选择运营商",onChange:ve,style:{width:"100%"},"popper-class":"operator-select-dropdown"},{default:a(()=>[(u(!0),q(B,null,E(o(N),l=>(u(),m(f,{key:l.id,label:l.companyName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:a(()=>[e(i,{label:"负责场库",prop:"warehouseId"},{default:a(()=>[e(v,{modelValue:o(s).warehouseId,"onUpdate:modelValue":t[16]||(t[16]=l=>o(s).warehouseId=l),placeholder:"请选择场库",style:{width:"100%"},"popper-class":"warehouse-select-dropdown"},{default:a(()=>[(u(!0),q(B,null,E(o(x),l=>(u(),m(f,{key:l.id,label:l.warehouseName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(Q,null,{default:a(()=>[e(_,{span:12},{default:a(()=>[e(i,{label:"管理人员姓名",prop:"managerName"},{default:a(()=>[e(W,{modelValue:o(s).managerName,"onUpdate:modelValue":t[17]||(t[17]=l=>o(s).managerName=l),placeholder:"请输入管理人员姓名"},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:a(()=>[e(i,{label:"管理人员电话",prop:"managerPhone"},{default:a(()=>[e(W,{modelValue:o(s).managerPhone,"onUpdate:modelValue":t[18]||(t[18]=l=>o(s).managerPhone=l),placeholder:"请输入管理人员电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(Q,null,{default:a(()=>[e(_,{span:12},{default:a(()=>[e(i,{label:"是否主要负责人",prop:"isPrimary"},{default:a(()=>[e(le,{modelValue:o(s).isPrimary,"onUpdate:modelValue":t[19]||(t[19]=l=>o(s).isPrimary=l)},{default:a(()=>[e(z,{value:1},{default:a(()=>[d("是")]),_:1}),e(z,{value:0},{default:a(()=>[d("否")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:a(()=>[e(i,{label:"状态",prop:"status"},{default:a(()=>[e(le,{modelValue:o(s).status,"onUpdate:modelValue":t[20]||(t[20]=l=>o(s).status=l)},{default:a(()=>[e(z,{value:1},{default:a(()=>[d("正常")]),_:1}),e(z,{value:0},{default:a(()=>[d("停用")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{label:"备注",prop:"remark"},{default:a(()=>[e(W,{modelValue:o(s).remark,"onUpdate:modelValue":t[21]||(t[21]=l=>o(s).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Xe=Se(je,[["__scopeId","data-v-6ed3fa56"]]);export{Xe as default};
