import{v as P,O as ce,d as ge,r as c,C as ye,T as he,x as _e,e as p,Q as W,c as B,o as f,R as b,f as l,S as ve,l as t,h as n,L as j,M as A,j as h,m as G,n as y,P as H,k as be,i as J,t as Ce}from"./index-D_FV2sri.js";import{g as we}from"./member-Ddkf0SYw.js";import{C as Pe}from"./index-C9zsUI-h.js";function Ve(d){return P({url:"/system/platform/unionPayConfig/list",method:"get",params:d})}function ke(d){return P({url:"/system/platform/unionPayConfig/"+d,method:"get"})}function Ue(d){return P({url:"/system/platform/unionPayConfig",method:"post",data:d})}function xe(d){return P({url:"/system/platform/unionPayConfig",method:"put",data:d})}function Te(d){return P({url:"/system/platform/unionPayConfig/"+d,method:"delete"})}function Se(d,m){return P({url:"/system/platform/unionPayConfig/checkMidUnique",method:"get",params:{mid:d,id:m}})}function Ie(d,m){return P({url:"/system/platform/unionPayConfig/checkTidUnique",method:"get",params:{tid:d,id:m}})}const qe={class:"app-container"},Ne={class:"dialog-footer"},De=ce({name:"UnionPayConfig"}),Be=Object.assign(De,{setup(d){const{proxy:m}=ge(),{pay_method:I}=m.useDict("pay_method"),E=c([]),X=c([]),q=c([]),_=c(!1),N=c(!0),x=c(!0),D=c([]),L=c(!0),F=c(!0),M=c(0),R=c(""),Y=ye({form:{},queryParams:{pageNum:1,pageSize:10,warehouseId:null,payType:null,mid:null,tid:null},rules:{warehouseId:[{required:!0,message:"场库不能为空",trigger:"change"}],payType:[{required:!0,message:"支付类型不能为空",trigger:"change"}],mid:[{required:!0,message:"商户号不能为空",trigger:"blur"},{min:1,max:32,message:"商户号长度必须介于 1 和 32 之间",trigger:"blur"},{validator:(o,e,s)=>{e?Se(e,i.id).then(r=>{r.data?s():s(new Error("商户号已存在"))}):s()},trigger:"blur"}],tid:[{required:!0,message:"终端号不能为空",trigger:"blur"},{min:1,max:32,message:"终端号长度必须介于 1 和 32 之间",trigger:"blur"},{validator:(o,e,s)=>{e?Ie(e,i.id).then(r=>{r.data?s():s(new Error("终端号已存在"))}):s()},trigger:"blur"}]}}),{queryParams:u,form:i,rules:Z}=he(Y);function C(){N.value=!0,Ve(u.value).then(o=>{E.value=o.rows,M.value=o.total,N.value=!1})}function ee(){_.value=!1,$()}function $(){i.value={id:null,warehouseId:null,payType:null,mid:null,tid:null,remark:null},m.resetForm("unionPayConfigRef")}function T(){u.value.pageNum=1,C()}function le(){m.resetForm("queryRef"),T()}function ae(o){D.value=o.map(e=>e.id),L.value=o.length!=1,F.value=!o.length}function te(){$(),O(),_.value=!0,R.value="添加银联配置"}function Q(o){$(),O();const e=o.id||D.value;ke(e).then(s=>{i.value=s.data,_.value=!0,R.value="修改银联配置"})}function ne(){m.$refs.unionPayConfigRef.validate(o=>{o&&(i.value.id!=null?xe(i.value).then(e=>{m.$modal.msgSuccess("修改成功"),_.value=!1,C()}):Ue(i.value).then(e=>{m.$modal.msgSuccess("新增成功"),_.value=!1,C()}))})}function z(o){const e=o.id||D.value;m.$modal.confirm('是否确认删除银联配置编号为"'+e+'"的数据项？').then(function(){return Te(e)}).then(()=>{C(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(){m.download("system/platform/unionPayConfig/export",{...u.value},`unionPayConfig_${new Date().getTime()}.xlsx`)}function O(){we().then(o=>{X.value=o.data,q.value=re(o.data)})}function re(o){if(!o||o.length===0)return[];const e=o.filter(r=>r.parentId==="0"),s=o.filter(r=>r.parentId!=="0");return e.map(r=>{const k=s.filter(w=>w.parentId===r.id).map(w=>({value:w.id,label:w.warehouseName,isLeaf:!0}));return{value:r.id,label:r.warehouseName,children:k.length>0?k:void 0}})}return _e(()=>{O(),C()}),(o,e)=>{const s=p("el-cascader"),r=p("el-form-item"),k=p("el-option"),w=p("el-select"),U=p("el-input"),g=p("el-button"),K=p("el-form"),S=p("el-col"),ie=p("right-toolbar"),ue=p("el-row"),v=p("el-table-column"),de=p("dict-tag"),se=p("el-table"),pe=p("el-dialog"),V=W("hasPermi"),me=W("loading");return f(),B("div",qe,[b(l(K,{model:t(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[l(r,{label:"场库/停车场",prop:"warehouseId"},{default:n(()=>[l(s,{modelValue:t(u).warehouseId,"onUpdate:modelValue":e[0]||(e[0]=a=>t(u).warehouseId=a),options:t(q),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),l(r,{label:"支付类型",prop:"payType"},{default:n(()=>[l(w,{modelValue:t(u).payType,"onUpdate:modelValue":e[1]||(e[1]=a=>t(u).payType=a),placeholder:"请选择支付类型",clearable:"",style:{width:"200px"}},{default:n(()=>[(f(!0),B(j,null,A(t(I),a=>(f(),h(k,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"商户号",prop:"mid"},{default:n(()=>[l(U,{modelValue:t(u).mid,"onUpdate:modelValue":e[2]||(e[2]=a=>t(u).mid=a),placeholder:"请输入商户号",clearable:"",style:{width:"200px"},onKeyup:G(T,["enter"])},null,8,["modelValue"])]),_:1}),l(r,{label:"终端号",prop:"tid"},{default:n(()=>[l(U,{modelValue:t(u).tid,"onUpdate:modelValue":e[3]||(e[3]=a=>t(u).tid=a),placeholder:"请输入终端号",clearable:"",style:{width:"200px"},onKeyup:G(T,["enter"])},null,8,["modelValue"])]),_:1}),l(r,null,{default:n(()=>[l(g,{type:"primary",icon:"Search",onClick:T},{default:n(()=>[y("搜索")]),_:1}),l(g,{icon:"Refresh",onClick:le},{default:n(()=>[y("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,t(x)]]),l(ue,{gutter:10,class:"mb8"},{default:n(()=>[l(S,{span:1.5},{default:n(()=>[b((f(),h(g,{type:"primary",plain:"",icon:"Plus",onClick:te},{default:n(()=>[y("新增")]),_:1})),[[V,["platform:unionPayConfig:add"]]])]),_:1}),l(S,{span:1.5},{default:n(()=>[b((f(),h(g,{type:"success",plain:"",icon:"Edit",disabled:t(L),onClick:Q},{default:n(()=>[y("修改")]),_:1},8,["disabled"])),[[V,["platform:unionPayConfig:edit"]]])]),_:1}),l(S,{span:1.5},{default:n(()=>[b((f(),h(g,{type:"danger",plain:"",icon:"Delete",disabled:t(F),onClick:z},{default:n(()=>[y("删除")]),_:1},8,["disabled"])),[[V,["platform:unionPayConfig:remove"]]])]),_:1}),l(S,{span:1.5},{default:n(()=>[b((f(),h(g,{type:"warning",plain:"",icon:"Download",onClick:oe},{default:n(()=>[y("导出")]),_:1})),[[V,["platform:unionPayConfig:export"]]])]),_:1}),l(ie,{showSearch:t(x),"onUpdate:showSearch":e[4]||(e[4]=a=>H(x)?x.value=a:null),onQueryTable:C},null,8,["showSearch"])]),_:1}),b((f(),h(se,{data:t(E),onSelectionChange:ae},{default:n(()=>[l(v,{type:"selection",width:"55",align:"center"}),be("",!0),l(v,{label:"场库名称",align:"center",prop:"warehouseName"}),l(v,{label:"支付类型",align:"center",prop:"payType"},{default:n(a=>[l(de,{options:t(I),value:a.row.payType},null,8,["options","value"])]),_:1}),l(v,{label:"商户号",align:"center",prop:"mid"}),l(v,{label:"终端号",align:"center",prop:"tid"}),l(v,{label:"备注",align:"center",prop:"remark"}),l(v,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:n(a=>[J("span",null,Ce(o.parseTime(a.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(v,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(a=>[b((f(),h(g,{link:"",type:"primary",icon:"Edit",onClick:fe=>Q(a.row)},{default:n(()=>[y("修改")]),_:2},1032,["onClick"])),[[V,["platform:unionPayConfig:edit"]]]),b((f(),h(g,{link:"",type:"primary",icon:"Delete",onClick:fe=>z(a.row)},{default:n(()=>[y("删除")]),_:2},1032,["onClick"])),[[V,["platform:unionPayConfig:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,t(N)]]),l(Pe,{total:t(M),page:t(u).pageNum,"onUpdate:page":e[5]||(e[5]=a=>t(u).pageNum=a),limit:t(u).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>t(u).pageSize=a),onPagination:C},null,8,["total","page","limit"]),l(pe,{title:t(R),modelValue:t(_),"onUpdate:modelValue":e[12]||(e[12]=a=>H(_)?_.value=a:null),width:"500px","append-to-body":""},{footer:n(()=>[J("div",Ne,[l(g,{type:"primary",onClick:ne},{default:n(()=>[y("确 定")]),_:1}),l(g,{onClick:ee},{default:n(()=>[y("取 消")]),_:1})])]),default:n(()=>[l(K,{ref:"unionPayConfigRef",model:t(i),rules:t(Z),"label-width":"80px"},{default:n(()=>[l(r,{label:"场库/停车场",prop:"warehouseId"},{default:n(()=>[l(s,{modelValue:t(i).warehouseId,"onUpdate:modelValue":e[7]||(e[7]=a=>t(i).warehouseId=a),options:t(q),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"100%"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),l(r,{label:"支付类型",prop:"payType"},{default:n(()=>[l(w,{modelValue:t(i).payType,"onUpdate:modelValue":e[8]||(e[8]=a=>t(i).payType=a),placeholder:"请选择支付类型",style:{width:"100%"}},{default:n(()=>[(f(!0),B(j,null,A(t(I),a=>(f(),h(k,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"商户号",prop:"mid"},{default:n(()=>[l(U,{modelValue:t(i).mid,"onUpdate:modelValue":e[9]||(e[9]=a=>t(i).mid=a),placeholder:"请输入商户号"},null,8,["modelValue"])]),_:1}),l(r,{label:"终端号",prop:"tid"},{default:n(()=>[l(U,{modelValue:t(i).tid,"onUpdate:modelValue":e[10]||(e[10]=a=>t(i).tid=a),placeholder:"请输入终端号"},null,8,["modelValue"])]),_:1}),l(r,{label:"备注",prop:"remark"},{default:n(()=>[l(U,{modelValue:t(i).remark,"onUpdate:modelValue":e[11]||(e[11]=a=>t(i).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Be as default};
