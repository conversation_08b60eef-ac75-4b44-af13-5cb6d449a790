# GitHub
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_github

# Gitee
Host gitee.com
    HostName gitee.com
    User git
    IdentityFile ~/.ssh/id_ed25519

# Tencent Cloud Server
Host tencent-cloud
    HostName **************
    User ubuntu
    Port 22
    IdentityFile ~/.ssh/augment.pem
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 10

Host ____xpipe
    HostName *************
    User root
    Port 1224
    ControlMaster no
    StrictHostKeyChecking no
    IgnoreUnknown RemoteCommand
    RemoteCommand none
    NoHostAuthenticationForLocalhost yes
    IdentitiesOnly yes

# AWS EC2 Ubuntu Server
Host AWS002
    HostName **************
    User ubuntu
    Port 22
    IdentityFile ~/.ssh/MAC.pem
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 10
