import{_ as S,r as f,e as o,c as p,o as _,i as e,f as s,n as d,h as t,L as C,M as I,j as k,t as c,l as q,a4 as L,a3 as U,A as j,B as D}from"./index-D_FV2sri.js";const n=v=>(j("data-v-6903ae4e"),v=v(),D(),v),E={class:"app-container"},F={class:"scan-voucher-container"},Q={class:"page-header"},G={class:"page-title"},H=n(()=>e("p",{class:"page-description"},"这是扫码购券页面 - 管理二维码购券功能和统计",-1)),J={class:"scan-stats"},K={class:"stat-content"},O={class:"stat-icon total"},W=n(()=>e("div",{class:"stat-info"},[e("div",{class:"stat-number"},"3,456"),e("div",{class:"stat-label"},"总扫码次数")],-1)),X={class:"stat-content"},Y={class:"stat-icon success"},Z=n(()=>e("div",{class:"stat-info"},[e("div",{class:"stat-number"},"2,789"),e("div",{class:"stat-label"},"成功购券")],-1)),$={class:"stat-content"},ee={class:"stat-icon rate"},se=n(()=>e("div",{class:"stat-info"},[e("div",{class:"stat-number"},"80.7%"),e("div",{class:"stat-label"},"转化率")],-1)),te={class:"stat-content"},ae={class:"stat-icon today"},oe=n(()=>e("div",{class:"stat-info"},[e("div",{class:"stat-number"},"¥12,580"),e("div",{class:"stat-label"},"今日收入")],-1)),ne={class:"card-header"},ce=n(()=>e("span",null,"二维码管理",-1)),le={class:"qrcode-grid"},de={class:"qrcode-header"},ie={class:"qrcode-name"},_e={class:"qrcode-content"},re={class:"qrcode-image"},ue={class:"qr-placeholder"},he=n(()=>e("div",{class:"qr-text"},"二维码",-1)),pe={class:"qrcode-info"},ve={class:"info-row"},me=n(()=>e("span",{class:"label"},"扫码次数:",-1)),fe={class:"value"},we={class:"info-row"},ye=n(()=>e("span",{class:"label"},"购券次数:",-1)),be={class:"value"},ge={class:"info-row"},Te=n(()=>e("span",{class:"label"},"转化率:",-1)),xe={class:"value"},Ne={class:"info-row"},ze=n(()=>e("span",{class:"label"},"创建时间:",-1)),Ce={class:"value"},Ie={class:"qrcode-actions"},ke={class:"card-header"},qe=n(()=>e("span",null,"扫码记录",-1)),Pe={class:"header-actions"},Ve={style:{"font-size":"12px",color:"#909399"}},Re={key:0},Me={key:1},Be={style:{"font-size":"12px"}},Ae={__name:"index",setup(v){const w=f(""),y=f(""),b=f([{id:1,name:"停车场入口码",status:"启用",scanCount:1256,purchaseCount:1023,conversionRate:81.4,createTime:"2024-01-15"},{id:2,name:"停车场出口码",status:"启用",scanCount:987,purchaseCount:756,conversionRate:76.6,createTime:"2024-01-16"},{id:3,name:"商场推广码",status:"启用",scanCount:2134,purchaseCount:1789,conversionRate:83.8,createTime:"2024-01-17"},{id:4,name:"活动专用码",status:"停用",scanCount:456,purchaseCount:321,conversionRate:70.4,createTime:"2024-01-10"}]),P=f([{qrcodeName:"停车场入口码",userName:"张三",userPhone:"138****8001",scanTime:"2024-01-20 14:30:15",action:"购买成功",voucherType:"停车券",amount:15,payMethod:"微信支付",deviceInfo:"iPhone 12 Pro"},{qrcodeName:"停车场出口码",userName:"李四",userPhone:"139****8002",scanTime:"2024-01-20 15:45:22",action:"仅浏览",voucherType:"-",amount:null,payMethod:"-",deviceInfo:"Android 小米11"},{qrcodeName:"商场推广码",userName:"王五",userPhone:"136****8003",scanTime:"2024-01-20 16:20:08",action:"购买成功",voucherType:"洗车券",amount:25,payMethod:"支付宝",deviceInfo:"iPhone 13"},{qrcodeName:"停车场入口码",userName:"赵六",userPhone:"137****8004",scanTime:"2024-01-20 17:10:33",action:"支付失败",voucherType:"停车券",amount:null,payMethod:"微信支付",deviceInfo:"Android 华为P40"}]),V=g=>{switch(g){case"购买成功":return"success";case"仅浏览":return"info";case"支付失败":return"danger";case"支付中":return"warning";default:return"info"}};return(g,m)=>{const r=o("svg-icon"),i=o("el-card"),h=o("el-col"),T=o("el-row"),x=o("el-icon"),u=o("el-button"),N=o("el-tag"),z=o("el-option"),R=o("el-select"),M=o("el-date-picker"),l=o("el-table-column"),B=o("el-table");return _(),p("div",E,[e("div",F,[e("div",Q,[e("h1",G,[s(r,{"icon-class":"scan-voucher",class:"title-icon"}),d(" 扫码购券管理 ")]),H]),e("div",J,[s(T,{gutter:20},{default:t(()=>[s(h,{span:6},{default:t(()=>[s(i,{class:"stat-card",shadow:"hover"},{default:t(()=>[e("div",K,[e("div",O,[s(r,{"icon-class":"scan-voucher"})]),W])]),_:1})]),_:1}),s(h,{span:6},{default:t(()=>[s(i,{class:"stat-card",shadow:"hover"},{default:t(()=>[e("div",X,[e("div",Y,[s(r,{"icon-class":"star"})]),Z])]),_:1})]),_:1}),s(h,{span:6},{default:t(()=>[s(i,{class:"stat-card",shadow:"hover"},{default:t(()=>[e("div",$,[e("div",ee,[s(r,{"icon-class":"chart"})]),se])]),_:1})]),_:1}),s(h,{span:6},{default:t(()=>[s(i,{class:"stat-card",shadow:"hover"},{default:t(()=>[e("div",te,[e("div",ae,[s(r,{"icon-class":"money"})]),oe])]),_:1})]),_:1})]),_:1})]),s(i,{class:"qrcode-management-card",shadow:"never"},{header:t(()=>[e("div",ne,[ce,s(u,{type:"primary",size:"small"},{default:t(()=>[s(x,null,{default:t(()=>[s(q(L))]),_:1}),d(" 生成新码 ")]),_:1})])]),default:t(()=>[e("div",le,[s(T,{gutter:20},{default:t(()=>[(_(!0),p(C,null,I(b.value,(a,A)=>(_(),k(h,{span:8,key:A},{default:t(()=>[s(i,{class:"qrcode-card",shadow:"hover"},{default:t(()=>[e("div",de,[e("div",ie,c(a.name),1),s(N,{type:a.status==="启用"?"success":"info",size:"small"},{default:t(()=>[d(c(a.status),1)]),_:2},1032,["type"])]),e("div",_e,[e("div",re,[e("div",ue,[s(r,{"icon-class":"scan-voucher"}),he])]),e("div",pe,[e("div",ve,[me,e("span",fe,c(a.scanCount),1)]),e("div",we,[ye,e("span",be,c(a.purchaseCount),1)]),e("div",ge,[Te,e("span",xe,c(a.conversionRate)+"%",1)]),e("div",Ne,[ze,e("span",Ce,c(a.createTime),1)])])]),e("div",Ie,[s(u,{type:"primary",size:"small"},{default:t(()=>[d("下载")]),_:1}),s(u,{type:"warning",size:"small"},{default:t(()=>[d("编辑")]),_:1}),s(u,{type:"info",size:"small"},{default:t(()=>[d("统计")]),_:1})])]),_:2},1024)]),_:2},1024))),128))]),_:1})])]),_:1}),s(i,{class:"scan-records-card",shadow:"never"},{header:t(()=>[e("div",ke,[qe,e("div",Pe,[s(R,{modelValue:w.value,"onUpdate:modelValue":m[0]||(m[0]=a=>w.value=a),placeholder:"选择二维码",size:"small",style:{width:"150px","margin-right":"10px"}},{default:t(()=>[s(z,{label:"全部",value:""}),(_(!0),p(C,null,I(b.value,a=>(_(),k(z,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),s(M,{modelValue:y.value,"onUpdate:modelValue":m[1]||(m[1]=a=>y.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small",style:{"margin-right":"10px"}},null,8,["modelValue"]),s(u,{type:"success",size:"small"},{default:t(()=>[s(x,null,{default:t(()=>[s(q(U))]),_:1}),d(" 导出 ")]),_:1})])])]),default:t(()=>[s(B,{data:P.value,style:{width:"100%"}},{default:t(()=>[s(l,{prop:"qrcodeName",label:"二维码",width:"150"}),s(l,{prop:"userInfo",label:"用户信息",width:"200"},{default:t(a=>[e("div",null,c(a.row.userName),1),e("div",Ve,c(a.row.userPhone),1)]),_:1}),s(l,{prop:"scanTime",label:"扫码时间",width:"180"}),s(l,{prop:"action",label:"操作结果",width:"120"},{default:t(a=>[s(N,{type:V(a.row.action),size:"small"},{default:t(()=>[d(c(a.row.action),1)]),_:2},1032,["type"])]),_:1}),s(l,{prop:"voucherType",label:"购买券类",width:"120"}),s(l,{prop:"amount",label:"金额",width:"100"},{default:t(a=>[a.row.amount?(_(),p("span",Re,"¥"+c(a.row.amount),1)):(_(),p("span",Me,"-"))]),_:1}),s(l,{prop:"payMethod",label:"支付方式",width:"120"}),s(l,{prop:"deviceInfo",label:"设备信息",width:"150"},{default:t(a=>[e("div",Be,c(a.row.deviceInfo),1)]),_:1}),s(l,{label:"操作",width:"100"},{default:t(a=>[s(u,{type:"primary",size:"small",link:""},{default:t(()=>[d("详情")]),_:1})]),_:1})]),_:1},8,["data"])]),_:1})])])}}},Le=S(Ae,[["__scopeId","data-v-6903ae4e"]]);export{Le as default};
