#!/bin/bash

# 脚本语法检查工具
# 作者: DevOps Team
# 版本: 1.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")
            echo -e "${GREEN}[$timestamp] [INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[$timestamp] [WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[$timestamp] [ERROR]${NC} $message" >&2
            ;;
        "DEBUG")
            echo -e "${BLUE}[$timestamp] [DEBUG]${NC} $message"
            ;;
    esac
}

# 检查单个脚本语法
check_script_syntax() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    log "DEBUG" "检查脚本语法: $script_name"
    
    # 使用bash -n检查语法
    if bash -n "$script_file" 2>/dev/null; then
        log "INFO" "✓ $script_name - 语法正确"
        return 0
    else
        log "ERROR" "✗ $script_name - 语法错误"
        
        # 显示详细错误信息
        echo "错误详情:"
        bash -n "$script_file" 2>&1 | sed 's/^/  /'
        echo ""
        
        return 1
    fi
}

# 检查脚本权限
check_script_permissions() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    if [ -x "$script_file" ]; then
        log "DEBUG" "✓ $script_name - 权限正确"
        return 0
    else
        log "WARN" "⚠ $script_name - 缺少执行权限"
        
        # 自动修复权限
        chmod +x "$script_file"
        log "INFO" "已修复执行权限: $script_name"
        
        return 0
    fi
}

# 检查脚本编码
check_script_encoding() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    # 检查是否包含BOM
    if [ "$(head -c 3 "$script_file" | od -t x1 -N 3 | head -1 | awk '{print $2$3$4}')" = "efbbbf" ]; then
        log "ERROR" "✗ $script_name - 包含BOM字符"
        return 1
    fi
    
    # 检查行结束符
    if file "$script_file" | grep -q "CRLF"; then
        log "WARN" "⚠ $script_name - 使用Windows行结束符(CRLF)"
        
        # 自动修复行结束符
        sed -i 's/\r$//' "$script_file"
        log "INFO" "已修复行结束符: $script_name"
    fi
    
    log "DEBUG" "✓ $script_name - 编码正确"
    return 0
}

# 检查脚本依赖
check_script_dependencies() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    log "DEBUG" "检查脚本依赖: $script_name"
    
    # 提取脚本中使用的命令
    local commands=$(grep -oE '\b(docker|docker-compose|mvn|npm|git|curl|nc|mysql|redis-cli)\b' "$script_file" 2>/dev/null | sort -u)
    
    local missing_commands=()
    
    for cmd in $commands; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [ ${#missing_commands[@]} -gt 0 ]; then
        log "WARN" "⚠ $script_name - 缺少依赖命令: ${missing_commands[*]}"
        return 1
    else
        log "DEBUG" "✓ $script_name - 依赖检查通过"
        return 0
    fi
}

# 检查脚本中的路径
check_script_paths() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    log "DEBUG" "检查脚本路径: $script_name"
    
    # 检查硬编码路径
    local hardcoded_paths=$(grep -oE '"/[^"]*"' "$script_file" 2>/dev/null | grep -v -E '"/dev/|"/proc/|"/sys/|"/tmp/"' || true)
    
    if [ -n "$hardcoded_paths" ]; then
        log "WARN" "⚠ $script_name - 发现硬编码路径:"
        echo "$hardcoded_paths" | sed 's/^/    /'
    fi
    
    # 检查相对路径使用
    local relative_paths=$(grep -oE '\.\./[^[:space:]]*' "$script_file" 2>/dev/null || true)
    
    if [ -n "$relative_paths" ]; then
        log "INFO" "ℹ $script_name - 使用相对路径:"
        echo "$relative_paths" | sed 's/^/    /'
    fi
    
    return 0
}

# 检查脚本安全性
check_script_security() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    log "DEBUG" "检查脚本安全性: $script_name"
    
    # 检查危险命令
    local dangerous_patterns=(
        "rm -rf /"
        "rm -rf \$"
        "chmod 777"
        "eval.*\$"
        "exec.*\$"
    )
    
    local security_issues=()
    
    for pattern in "${dangerous_patterns[@]}"; do
        if grep -qE "$pattern" "$script_file" 2>/dev/null; then
            security_issues+=("$pattern")
        fi
    done
    
    if [ ${#security_issues[@]} -gt 0 ]; then
        log "ERROR" "✗ $script_name - 发现安全风险:"
        for issue in "${security_issues[@]}"; do
            echo "    - $issue"
        done
        return 1
    fi
    
    # 检查密码硬编码
    if grep -qE '(password|passwd|pwd).*=.*["\"][^"\"]*["\"]' "$script_file" 2>/dev/null; then
        log "WARN" "⚠ $script_name - 可能包含硬编码密码"
    fi
    
    log "DEBUG" "✓ $script_name - 安全检查通过"
    return 0
}

# 生成检查报告
generate_report() {
    local total_scripts="$1"
    local passed_scripts="$2"
    local failed_scripts="$3"
    
    echo ""
    echo "=== 脚本检查报告 ==="
    echo "检查时间: $(date)"
    echo "总脚本数: $total_scripts"
    echo "通过检查: $passed_scripts"
    echo "检查失败: $failed_scripts"
    echo ""
    
    if [ "$failed_scripts" -eq 0 ]; then
        log "INFO" "🎉 所有脚本检查通过！"
        return 0
    else
        log "ERROR" "❌ 有 $failed_scripts 个脚本检查失败"
        return 1
    fi
}

# 主检查函数
main() {
    log "INFO" "开始脚本语法检查..."
    log "INFO" "检查目录: $SCRIPTS_ROOT"
    
    # 查找所有shell脚本
    local script_files=($(find "$SCRIPTS_ROOT" -name "*.sh" -type f))
    
    if [ ${#script_files[@]} -eq 0 ]; then
        log "WARN" "没有找到shell脚本文件"
        return 0
    fi
    
    log "INFO" "找到 ${#script_files[@]} 个脚本文件"
    echo ""
    
    local total_scripts=${#script_files[@]}
    local passed_scripts=0
    local failed_scripts=0
    local failed_files=()
    
    # 检查每个脚本
    for script_file in "${script_files[@]}"; do
        local script_name=$(basename "$script_file")
        local script_passed=true
        
        echo "检查脚本: $script_name"
        echo "----------------------------------------"
        
        # 语法检查
        if ! check_script_syntax "$script_file"; then
            script_passed=false
        fi
        
        # 权限检查
        if ! check_script_permissions "$script_file"; then
            script_passed=false
        fi
        
        # 编码检查
        if ! check_script_encoding "$script_file"; then
            script_passed=false
        fi
        
        # 依赖检查
        if ! check_script_dependencies "$script_file"; then
            # 依赖检查失败不算致命错误
            true
        fi
        
        # 路径检查
        check_script_paths "$script_file"
        
        # 安全检查
        if ! check_script_security "$script_file"; then
            script_passed=false
        fi
        
        if [ "$script_passed" = true ]; then
            ((passed_scripts++))
        else
            ((failed_scripts++))
            failed_files+=("$script_name")
        fi
        
        echo ""
    done
    
    # 生成报告
    generate_report "$total_scripts" "$passed_scripts" "$failed_scripts"
    
    # 如果有失败的脚本，列出它们
    if [ ${#failed_files[@]} -gt 0 ]; then
        echo "失败的脚本:"
        for file in "${failed_files[@]}"; do
            echo "  - $file"
        done
        echo ""
    fi
    
    # 返回结果
    if [ "$failed_scripts" -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
