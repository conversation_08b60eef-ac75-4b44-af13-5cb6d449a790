### 停车系统接口开发文档

本文档详细说明了停车系统的标准云接口和数据推送接口，旨在帮助开发者快速集成。

## 一、 标准云接口

本部分定义了第三方平台调用云端服务时的所有接口规范。

### 1.1 通用签名规则

所有标准云接口（除优惠券接口外）均遵循此签名规则。

- **签名方法**：将需要发送的「JSON对象」中所有「字段名」按「字典序」排序，然后使用 `key=value&` 的方式拼接成字符串。最后，在字符串末尾拼接上 `key=密钥` 形成最终的待签名字符串。
- **签名值**：对待签名字符串进行32位大写的MD5加密，得到最终的签名值。

**注意事项**:

1. 当字段的`值`是对象、空字符串或`null`时，该字段**不参与**签名。
2. `密钥` (Key) 需要联系技术人员分配，否则签名验证将不通过。
3. 接口请求签名失败时，会返回用于签名的`data`字段（不含最后拼接的密钥），方便开发者对比排查。
4. 请确保您使用的`MD5`加密方法与标准在线工具结果一致。

**签名示例**:

- 假设 `Key` 值为: `20180718152957184`

- **原始请求参数 (JSON)**:

  ```
  {
      "parkingID": "20180718152957184",
      "timestamp": "1694163437000",
      "plate": "鄂A12317",
      "vehicleType": "临时车",
      "beginTime": "2023-02-02",
      "endTime": "2023-03-02 23:59:59",
      "userName": "",
      "chargeTypeName": "",
      "channelPermission": 15,
      "status": 1,
      "sign": "D0A8BFF8F190FEE95EF74D3682CCD98D"
  }
  ```

- **拼接后的待签名字符串**:

  ```
  beginTime=2023-02-02&channelPermission=15&endTime=2023-03-02 23:59:59&parkingID=20180718152957184&plate=鄂A12317&status=1&timestamp=1694163437000&vehicleType=临时车&key=20180718152957184
  ```

- **计算出的32位大写MD5签名值**:

  ```
  D0A8BFF8F190FEE95EF74D3682CCD98D
  ```

### 1.2 车辆管理接口

#### **新增/修改车辆信息**

```
POST /transmit/vehicle/save
```

**请求参数**

|

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 |

| vehicleType | string | M | 车辆类型，取值: ["临时车", "月租车", "免费车", "储值车", "访客车"] |

| beginTime | string | M | 启用时间，默认当天0点，格式 YYYY-MM-dd |

| endTime | string | M | 结束时间，时分秒请传 23:59:59，格式 YYYY-MM-dd HH:mm:ss |

| userName | string | O | 用户名称，不传则取车牌号 |

| userSpace | int | O | 用户车位数 |

| chargeTypeName | string | O | 收费标准名称，不传则取车场默认标准 |

| channelPermission | int | O | 通道权限，不传则拥有所有通道权限 |

| status | int | O | 状态，0禁用 1正常，不传默认1 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | string | C | 签名前字符串（不含密钥），code=0 时必返 |

#### **删除车辆信息**

```
POST /transmit/vehicle/delete
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 |

| userSpace | int | O | 用户车位数 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | string | C | 签名前字符串（不含密钥），code=0 时必返 |

#### **查询车辆信息**

```
POST /transmit/vehicle/query
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | object/string | C | 成功时返回车辆信息对象，失败时返回签名前字符串 |

| data.userName | string | C | 用户名称 |

| data.userTel | string | C | 用户电话 |

| data.userLotCount | string | C | 用户车位数量 |

| data.storeBalance | string | C | 用户储值余额 |

| data.storeUpdateTime | string | C | 储值余额更新时间 |

| data.plate | string | C | 车牌号 |

| data.vehicleType | string | C | 车辆类型 |

| data.beginTime | string | C | 开始时间 |

| data.endTime | string | C | 到期时间 |

| data.permission | string | C | 通道权限 |

| data.packageName | string | C | 绑定套餐名称 |

| data.packageAmount | string | C | 套餐金额 |

| data.packageUnit | string | C | 套餐单位 0月 1年 2日 |

| data.packageValue | string | C | 套餐值，配合单位计算时长 |

### 1.3 黑名单管理接口

#### **新增黑名单**

```
POST /transmit/blacklist/save
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 |

| endTime | string | M | 失效时间，格式 YYYY-MM-dd HH:mm:ss |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | string | C | 签名前字符串（不含密钥），code=0 时必返 |

#### **删除黑名单**

```
POST /transmit/blacklist/delete
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | object/string | C | 成功时返回黑名单数据，失败时返回签名前字符串 |

| data.plate | string | C | 成功时必传，车牌号 |

| data.endTime | string | C | 成功时必传，失效时间 |

**返回示例 (成功)**

```
{
    "code": 1,
    "data": {
        "plate":"鄂A11111",
        "endTime":"2022-10-10 23:59:59"
    }
}
```

### 1.4 场内管理接口

#### **无牌车入场**

`POST /transmit/noplate/entry` **说明**：此接口用于通知指定通道的客户端处理无牌车入场。接收消息的客户端必须在线且其IP与通道关联的终端IP一致。

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 (无牌车可为 "无牌车" 或其他约定标识) |

| channelIndex | int | O | 通道编号 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 (如 "客户端不在线") |

| data | string | C | 签名前字符串（不含密钥），code=0 时必返 |

#### **查询车辆在场信息及费用**

```
POST /transmit/price/queryv2
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | object/string | C | 成功时返回费用对象，失败时返回签名前字符串 |

| data.plate | string | M | 车牌号 |

| data.inTime | string | C | 入场时间，在场必返，格式yyyy-MM-dd HH:mm:ss |

| data.outTime | string | C | 出场时间，在场必返，格式yyyy-MM-dd HH:mm:ss |

| data.isOnPark | int | C | 在场状态，0不在场 1在场 |

| data.totalAmount | int | C | 停车总金额，单位分 |

| data.paidAmount | int | C | 已支付金额，单位分 |

| data.discountAmount | int | C | 优惠券抵扣金额，单位分 |

| data.needPayAmount | int | C | 本次停车应付金额，单位分 |

**返回示例 (成功)**

```
{
    "code": 1,
    "data": {
        "plate": "鄂J94637",
        "inTime": "2023-03-02 14:23:41",
        "outTime": "2023-03-02 15:23:41",
        "isOnPark": 1,
        "totalAmount": 5000,
        "paidAmount": 0,
        "discountAmount": 0,
        "needPayAmount": 5000
    }
}
```

#### **查询出口待支付车辆费用**

```
POST /transmit/outpay/queryv2
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| channelIndex | int | M | 查费通道编号 |

| sign | string | M | 签名 |

**返回参数**

(返回参数及示例同 `/transmit/price/queryv2`)

#### **缴费通知**

```
POST /transmit/pay/notice
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| plate | string | M | 车牌号 |

| amount | int | M | 支付金额，单位分 |

| payType | int | O | 支付类型，1支付宝 2微信，不传默认 "移动支付" |

| payTime | string | O | 支付时间，格式yyyy-MM-dd HH:mm:ss，不传默认当前时间 |

| tradeNo | string | O | 订单号，一次支付唯一 |

| isNoPlate | int | O | 无牌车标识，0非无牌车 1无牌车，不传默认0 |

| outChannelIndex | int | O | 出口通道编号，无牌车支付时，软件会根据此编号开闸 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | string | C | 签名前字符串（不含密钥），code=0 时必返 |

#### **查询余位信息**

```
POST /transmit/lot/query
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| parkingID | string | M | 车场编号 |

| timestamp | string | M | 毫秒时间戳 |

| parkName | string | O | 软件内停车场名称，不传则查询所有车场总车位 |

| sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| code | int | M | 状态码，1成功 0失败 |

| msg | string | C | 错误信息，code=0 时必返 |

| data | object/string | C | 成功时返回车位信息对象，失败时返回签名前字符串 |

| data.totalLot | int | C | 总车位 |

| data.tempLot | int | C | 临时车总车位 |

| data.leftLot | int | C | 剩余车位 |

| data.leftTempLot | int | C | 临时车剩余车位 |

**返回示例 (成功)**

```
{
    "code": 1,
    "data": {
        "totalLot": 994,
        "tempLot": 1000,
        "leftLot": 994,
        "leftTempLot": 994
    }
}
```

### 1.5 优惠券接口 (独立)

**注意：优惠券接口使用独立的接口地址和签名规则。**

- **接口地址**:
  - **正式环境**: `http://qr.it-wy.cn:82/CouponReport/CouponRelease`
  - **测试环境**: `http://wxy.ittiger.club:9002/CouponReport/CouponRelease`
- **特别签名规则**:
  1. 拼接方式为 `keyvalue`，**中间无需 `&` 连接符**。
  2. 所有参与签名的字段按序拼接后，**直接在末尾拼接密钥**。
  3. **示例**:
     - 待签名参数: `CouponName=测试`, `CouponType=1`, `Discount=1`, `ParkID=20231226105133354`, `PlateNumber=沪B12345`
     - 密钥: `20231226105133354`
     - **拼接后待签名字符串**: `CouponName=测试CouponType=1Discount=1ParkID=20231226105133354PlateNumber=沪B1234520231226105133354`

#### **发放优惠券**

**请求参数**

| 参数 | 类型 | 必填 | 说明 |

| ParkID | string | M | 车场编号 |

| PlateNumber | string | M | 车牌号 |

| CouponType | int | M | 优惠券类型: 1时长, 2金额, 3次券 |

| Discount | int | M | 优惠值。时长券单位：分钟；金额券单位：元 |

| CouponName | string | M | 券名称 |

| Sign | string | M | 签名 |

**返回参数**

| 参数 | 类型 | 必填 | 说明 |

| Code | int | M | 状态码，1成功 0失败 |

| ErrMsg | string | C | 错误信息，code=0 时必返 |

**返回示例**

```
{ 
    "Code": 1, 
    "ErrMsg": "success" 
}
```

## 二、 推送接口

本部分定义了车场系统在特定事件发生时，向第三方平台主动推送数据的格式。第三方平台需提供接收推送的URL。

### 2.1 车辆入场推送

- **推送时机**: 车辆入场完成时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `ProjectName` | string | O | 车场名称 | | `Plate` | string | **M** | 车牌号码 | | `PlateType` | int | **M** | 车牌类型 (详见附录) | | `VehicleType` | string | **M** | 车辆类型 | | `UserName` | string | **M** | 用户名称 | | `ChannelName` | string | **M** | 入场通道名称 | | `ChannelNo` | int | **M** | 入场通道编号 | | `ChargeTypeName` | string| **M** | 收费标准名称 | | `InTime` | string | **M** | 入场时间, `yyyy-MM-dd HH:mm:ss` | | `TempLot` | int | **M** | 剩余临时车车位数 | | `TotalLot` | int | **M** | 剩余总车位数 | | `InParkNum` | int | **M** | 在场车辆数量 | | `Mobile` | string | O | 用户手机号 | | `IDCard` | string | O | 用户身份证 | | `OrderID` | string | **M** | 订单编号，一次停车唯一 | | `OperatorName`| string | **M** | 操作员名称 | | `InPicUrl` | string | O | 入场图片base64字符串 | | `SmallInPicUrl` | string | O | 入场小图base64字符串 | | `Panorama` | string | O | 全景图base64字符串 |

- **推送示例**:

  ```
  {
      "ParkID": "202008141120165861",
      "ProjectName": "",
      "Plate": "赣A11111",
      "PlateType": 0,
      "VehicleType": "储值车",
      "UserName": "张三",
      "ChannelName": "北门入口",
      "ChannelNo": 1,
      "ChargeTypeName": "标准收费标准",
      "InTime": "2023-09-08 13:58:03",
      "TempLot": 998,
      "TotalLot": 998,
      "InParkNum": 1,
      "Mobile": "13800000000",
      "IDCard": "",
      "OrderID": "5ea065f6-792c-4244-a446-c93866bea891",
      "OperatorName": "admin"
  }
  ```

### 2.2 车辆出场推送

- **推送时机**: 车辆出场完成时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `ProjectName` | string | O | 车场名称 | | `Plate` | string | **M** | 车牌号码 | | `PlateType` | int | **M** | 车牌类型 (详见附录) | | `VehicleType` | string | **M** | 车辆类型 | | `UserName` | string | **M** | 用户名称 | | `InChannelName` | string | **M** | 入场通道名称 | | `InChannelIndex`| int | **M** | 入场通道编号 | | `ChannelName` | string | **M** | 出场通道名称 | | `ChannelNo` | int | **M** | 出场通道编号 | | `OrderID` | string | **M** | 订单编号，一次停车唯一 | | `PayType` | string | **M** | 支付类型 | | `PayMoney` | decimal | **M** | 收费金额，单位分 | | `InTime` | string | **M** | 入场时间, `yyyy-MM-dd HH:mm:ss` | | `OutTime` | string | **M** | 出场时间, `yyyy-MM-dd HH:mm:ss` | | `TempLot` | int | **M** | 剩余临时车车位数 | | `TotalLot` | int | **M** | 剩余总车位数 | | `InParkNum` | int | **M** | 在场车辆数量 | | `Mobile` | string | O | 用户手机号 | | `IDCard` | string | O | 用户身份证 | | `OperatorName`| string | **M** | 操作员名称 | | `OutPicUrl` | string | O | 出场图片base64字符串 | | `SmallOutPicUrl`| string | O | 出场小图base64字符串 | | `Panorama` | string | O | 全景图base64字符串 |

- **推送示例**:

  ```
  {
      "ParkID": "123456789",
      "ProjectName": "测试车场",
      "Plate": "赣A11111",
      "PlateType": 0,
      "VehicleType": "储值车",
      "UserName": "张三",
      "InChannelName": "北门入口",
      "InChannelNo": 1,
      "ChannelName": "北门出口",
      "ChannelNo": 2,
      "OrderID": "1ae35275-aac4-4a88-970f-338cc36e535b",
      "PayType": "储值扣费",
      "PayMoney": "100",
      "InTime": "2023-09-08 14:09:47",
      "OutTime": "2023-09-08 14:10:00",
      "ChargeTypeName": "标准收费标准",
      "TempLot": 997,
      "TotalLot": 997,
      "InParkNum": 2,
      "Mobile": "13800000000",
      "IDCard": "",
      "OperatorName": "admin"
  }
  ```

### 2.3 车辆信息变更推送

- **推送时机**: 车辆新增或车辆信息改动完成时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `ProjectName` | string | O | 车场名称 | | `UserID` | string | **M** | 用户唯一id | | `UserName` | string | **M** | 用户名称 | | `Plate` | string | **M** | 车牌号码 | | `oldPlate` | string | C | 如果修改车牌必传，修改前车牌 | | `Mobile` | string | O | 用户手机号 | | `VehicleType` | string | **M** | 车辆类型 | | `LotNum` | int | **M** | 用户车位数量 | | `StartTime` | string | **M** | 车辆启用时间, `yyyy-MM-dd HH:mm:ss` | | `EndTime` | string | **M** | 车辆到期时间, `yyyy-MM-dd HH:mm:ss` | | `PackageName` | string | O | 车辆绑定月租套餐名称 | | `PackageAmount`| decimal | O | 套餐收费 | | `PackageUnit` | int | O | 套餐单位 `0`月`1`年`2`日 | | `Package` | int | O | 套餐值 | | `PlateColor` | int | **M** | 车辆颜色, `0`未知`1`蓝`2`黄`3`白`4`黑`5`绿 | | `ChannelPermission`| int | **M** | 车辆通道权限 |

- **推送示例**:

  ```
  {
      "ParkID": "123456789",
      "ProjectName": "测试车场",
      "UserID": "69bbd4f6-f9b1-4fc4-b6c3-c147b7b775a6",
      "UserName": "张三",
      "Plate": "赣A22222",
      "oldPlate": "赣A11111",
      "Mobile": "13970862710",
      "VehicleType": "储值车",
      "LotNum": 1,
      "StartTime": "2023-07-21T00:00:00",
      "EndTime": "2045-03-05T23:59:59",
      "PackageName": null,
      "PackageAmount": 0,
      "PackageUnit": 0,
      "Package": 0,
      "PlateColor": 4,
      "ChannelPermission": 3
  }
  ```

### 2.4 在场车牌修改推送

- **推送时机**: 在场车辆的车牌号被修改时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `ProjectName` | string | O | 车场名称 | | `OldPlate` | string | **M** | 修改前车牌 | | `Plate` | string | **M** | 修改后车牌 | | `OrderID` | string | **M** | 订单编号，一次停车唯一 | | `OperaType` | int | **M** | 修改类型，`0`修改车牌 `1`直接出场 |

- **推送示例**:

  ```
  {
      "ParkID": "202008141120165861",
      "ProjectName": "测试车场",
      "OldPlate": "赣A11111",
      "Plate": "赣A11111",
      "OrderID": "5ea065f6-792c-4244-a446-c93866bea891",
      ...
      "OperaType": "0"
  }
  ```

### 2.5 车辆删除推送

- **推送时机**: 车辆删除完成时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `ProjectName` | string | O | 车场名称 | | `Plate` | string | **M** | 被删除的车牌号码 |

- **推送示例**:

  ```
  {
      "ParkID": "123456789",
      "ProjectName": "测试车场",
      "Plate": "赣A22222"
  }
  ```

### 2.6 车位变动推送

- **推送时机**: 停车场车位（总车位、剩余车位等）发生变动时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `ParkName` | string | O | 软件内车场名称，用于区分不同区域的车位 | | `TempLot` | int | **M** | 剩余临时车车位数 | | `TotalLot` | int | **M** | 剩余总车位数 | | `InParkNum`| int | **M** | 在场车辆数量 | | `ParkLot` | int | **M** | 总车位数 |

- **推送示例**:

  ```
  {
      "ParkID": "202008141120165861",
      "ParkName": "",
      "TempLot": 998,
      "TotalLot": 998,
      "InParkNum": 2,
      "ParkLot": 999
  }
  ```

### 2.7 其他业务推送

#### **储值车扣费推送**

- **推送时机**: 储值车扣费完成时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `Plate` | string | **M** | 车牌号码 | | `ChannelName` | string | **M** | 出场通道名称 | | `Balance` | decimal | **M** | 用户余额，单位元 | | `ConsumeAmount` | decimal | **M** | 本次消费金额，单位元 |

- **示例**:

  ```
  {
      "ParkID": "123456789",
      "Plate": "赣A11111",
      "ChannelName": "北门出口",
      "Balance": 53.00,
      "ConsumeAmount": 1.0
  }
  ```

#### **手动开闸推送**

- **推送时机**: 通道被手动开闸时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `OperatorName` | string | **M** | 操作员名称 | | `OpenTime` | string | **M** | 操作时间, `yyyy-MM-dd HH:mm:ss` | | `Image` | string | O | 开闸时图片base64字符串 | | `Reason` | string | **M** | 开闸原因 |

- **示例**:

  ```
  {
      "ParkID": "123456789",
      "OperatorName": "admin",
      "OpenTime": "2023-09-08 14:10:27",
      "Image": "...",
      "Reason": "员工车"
  }
  ```

#### **操作员变动推送**

- **推送时机**: 操作员增加、修改或删除完成时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `IsDelete` | bool | **M** | 是否删除, `true`是, `false`否 | | `JobNumber` | int | **M** | 操作员唯一工号 | | `Name` | string | **M** | 操作员名称 | | `Password` | string | C | 加密后的密码，有密码时必传 |

- **示例**:

  ```
  {
      "ParkID": "202008141120165861",
      "IsDelete": false,
      "JobNumber": "2",
      "Name": "队长",
      "Password": ""
  }
  ```

#### **操作员登录/退出推送**

- **推送时机**: 操作员登录或退出系统时。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `JobNumber` | int | **M** | 操作员唯一工号 | | `Name` | string | **M** | 操作员名称 | | `IsLogin` | bool | **M** | `true`登录, `false`退出 |

- **示例**:

  ```
  {
      "ParkID": "202008141120165861",
      "JobNumber": "1",
      "Name": "admin",
      "IsLogin": true
  }
  ```

### 2.8 交互式推送 (需返回响应)

#### **车辆进出前确认**

- **推送时机**: 车辆到达道闸口，系统识别后，开闸前推送。
- **目的**: 第三方平台根据业务逻辑判断是否允许车辆通行。
- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `Plate` | string | **M** | 车牌号码 | | `ChannelName` | string | **M** | 通道名称 | | `ChannelNo` | int | **M** | 通道编号 | | `IsIn` | bool | **M** | `true`入场, `false`出场 | | `IoTime` | string | **M** | 进出时间, `yyyy-MM-dd HH:mm:ss`| | `InPicUrl` | string | O | 进出图片base64字符串 |
- **第三方平台返回参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `Code` | int | **M** | `1`成功 `0`失败 | | `EnableOpen`| bool | C | `Code=1`时生效, `true`开闸 `false`不开闸 | | `VehicleType`| string | C | `Code=1`时必填, 用于LED显示 | | `DispMsg` | string | C | `Code=1`时必填, 用于LED和语音播报 |

#### **车辆出场前支付确认**

- **推送时机**: 车辆到达出口，计算出费用后，支付前推送。

- **目的**: 第三方平台判断该车辆是否已通过其他渠道完成支付。

- **推送参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `ParkID` | string | **M** | 车场编号 | | `Plate` | string | **M** | 车牌号码 | | `PlateColor`| int | **M** | 车牌颜色 `0`未知`1`蓝`2`黄`3`白`4`黑`5`绿 | | `OrderID` | string | **M** | 订单编号，一次停车唯一 | | `InTime` | string | **M** | 入场时间, `yyyy-MM-dd HH:mm:ss` | | `Amount` | int | **M** | 应收金额，单位分 |

- **第三方平台返回参数**: | 参数 | 类型 | 必填 | 说明 | | :--- | :--- | :--- | :--- | | `Status` | bool | **M** | `true`表示已支付，系统直接开闸；`false`表示未支付，走正常收费流程 |

- **返回示例**:

  ```
  {
      "Status": true
  }
  ```

## 三、 附录

### 附录A: 车牌类型 (PlateType) 说明

| 值 | 说明 |

| 0 | 未知车牌 |

| 1 | 蓝牌小汽车 |

| 2 | 黑牌小汽车 |

| 3 | 单排黄牌 |

| 4 | 双排黄牌 |

| 5 | 警车车牌 |

| 6 | 武警车牌 |

| 7 | 个性化车牌 |

| 8 | 单排军车牌 |

| 9 | 双排军车牌 |

| 10 | 使馆车牌 |

| 11 | 香港进出中国大陆车牌 |

| 12 | 农用车牌 |

| 13 | 教练车牌 |

| 14 | 澳门进出中国大陆车牌 |

| 19 | 新能源 |

| 20 | 大型新能源 |

| 21 | 应急车牌 |

| ### ... | 其他类型 |