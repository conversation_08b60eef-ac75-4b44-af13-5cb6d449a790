<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxUserCarMapper">
    <sql id="selectWxUserCarVo">
        select id,
               plate_no,
               car_brand,
               car_type,
               is_default,
               user_id,
               energy_type,
               delete_flag
        from wx_user_car
    </sql>

    <select id="selectWxUserCarList" resultType="com.lgjy.wx.domain.WxUserCar">
        <include refid="selectWxUserCarVo"/>
        where delete_flag = false
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        order by
        is_default desc,
        create_time asc
    </select>

    <insert id="insertWxUserCar">
        insert into wx_user_car(id,plate_no, car_brand, car_type, is_default, user_id, energy_type)
        values(#{id},#{plateNo}, #{carBrand}, #{carType}, #{isDefault}, #{userId}, #{energyType})
    </insert>

    <update id="updateWxUserCar">
        update wx_user_car
        <set>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="carBrand != null">car_brand = #{carBrand},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="energyType != null">energy_type = #{energyType}</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteWxUserCar">
        update wx_user_car
        set delete_flag = true
        where id = #{id}
    </delete>

    <select id="selectWxUserCarById" resultType="com.lgjy.wx.domain.WxUserCar">
        <include refid="selectWxUserCarVo"/>
        where id = #{id} and delete_flag = false
    </select>

    <select id="selectWxUserDefaultCar" resultType="com.lgjy.wx.domain.WxUserCar">
        <include refid="selectWxUserCarVo"/>
        where user_id = #{userId} and delete_flag = false and is_default = true
    </select>

    <select id="selectWxUserCarByPlateNo" resultType="com.lgjy.wx.domain.WxUserCar">
        <include refid="selectWxUserCarVo"/>
        where plate_no = #{plateNo} and delete_flag = false
    </select>
</mapper>