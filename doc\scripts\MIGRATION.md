# Scripts 目录迁移说明

## 📁 迁移概述

为了更好地管理CI/CD脚本并确保版本控制的一致性，我们已将`scripts`目录从工作空间根目录迁移到后端仓库中。

## 🔄 迁移详情

### 迁移前结构
```
F:\parking\  (工作空间根目录)
├── scripts\          ❌ 问题：不在版本控制中
├── park-api\         (后端仓库)
├── park-ui\          (前端仓库)
└── ...
```

### 迁移后结构
```
F:\parking\  (工作空间根目录)
├── park-api\         (后端仓库)
│   ├── src\
│   ├── pom.xml
│   ├── scripts\      ✅ 现在在版本控制中
│   │   ├── build\
│   │   ├── deploy\
│   │   ├── test\
│   │   ├── utils\
│   │   └── jenkins\
│   └── ...
├── park-ui\          (前端仓库)
└── ...
```

## ✅ 迁移优势

### 1. 版本控制
- ✅ scripts现在跟随后端代码进行版本控制
- ✅ 不同分支可以有不同版本的部署脚本
- ✅ 可以追踪scripts的修改历史

### 2. 团队协作
- ✅ 团队成员克隆后端仓库时自动包含scripts
- ✅ 脚本修改可以通过Pull Request进行代码审查
- ✅ 确保所有环境使用相同版本的脚本

### 3. Jenkins集成
- ✅ Jenkins可以直接从后端仓库拉取scripts
- ✅ 符合Jenkins流水线的设计预期
- ✅ 脚本路径引用更加清晰

### 4. 部署一致性
- ✅ 开发、测试、生产环境使用相同的脚本版本
- ✅ 避免因脚本不一致导致的部署问题
- ✅ 支持脚本的回滚和版本管理

## 🔧 Jenkins配置更新

### Pipeline配置
Jenkins Pipeline任务配置保持不变：
```
Repository URL: https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-api.git
Branch: park
Script Path: scripts/jenkins/Jenkinsfile  ✅ 路径正确
```

### 脚本路径
所有脚本路径引用已经正确配置：
- Jenkinsfile: `park-api/scripts/jenkins/Jenkinsfile`
- 构建脚本: `park-api/scripts/build/`
- 部署脚本: `park-api/scripts/deploy/`
- 测试脚本: `park-api/scripts/test/`

## 📋 验证清单

### ✅ 已完成的迁移步骤
- [x] 将scripts从根目录移动到park-api/scripts/
- [x] 添加scripts到Git版本控制
- [x] 提交scripts到后端仓库
- [x] 更新相关文档和配置
- [x] 验证目录结构正确

### 🔍 需要验证的项目
- [ ] Jenkins可以正确拉取scripts
- [ ] 所有脚本路径引用正确
- [ ] 脚本执行权限正常
- [ ] 团队成员可以正常访问scripts

## 🚀 下一步操作

### 1. 推送到远程仓库
```bash
cd F:\parking\park-api
git push origin park
```

### 2. 验证Jenkins配置
- 确认Jenkins Pipeline任务配置正确
- 执行一次测试构建验证scripts拉取正常

### 3. 团队同步
- 通知团队成员scripts位置变更
- 更新相关文档和Wiki

## 📝 注意事项

### 对现有工作流的影响
1. **开发者**: 需要从park-api仓库中访问scripts
2. **运维人员**: 脚本修改需要通过Git提交
3. **Jenkins**: 配置无需更改，会自动从正确位置拉取

### 最佳实践
1. **脚本修改**: 通过Git分支和Pull Request进行
2. **版本管理**: 使用Git标签标记重要的脚本版本
3. **测试**: 在测试环境验证脚本修改后再合并到主分支

## 🔗 相关文档

- [Jenkins配置指南](./jenkins/SETUP.md)
- [部署检查清单](./jenkins/CHECKLIST.md)
- [使用说明](./jenkins/README.md)
- [故障排除指南](./jenkins/TROUBLESHOOTING.md)

## 📞 支持

如有任何问题，请联系：
- DevOps团队
- 项目负责人
- 技术支持
