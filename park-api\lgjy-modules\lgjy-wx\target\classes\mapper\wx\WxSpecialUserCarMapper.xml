<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxSpecialUserCarMapper">
    
    <sql id="selectSpecialUserCarVo">
        select id, special_user_id, warehouse_id, phone_number, plate_no, delete_flag,
               create_by, create_time, update_by, update_time 
        from mini_special_user_car
    </sql>

    <select id="selectCarsBySpecialUserId" resultType="com.lgjy.wx.domain.WxSpecialUserCar">
        <include refid="selectSpecialUserCarVo"/>
        where special_user_id = #{specialUserId} and delete_flag = 0
    </select>

    <select id="selectCarsByPhoneNumber" resultType="com.lgjy.wx.domain.WxSpecialUserCar">
        <include refid="selectSpecialUserCarVo"/>
        WHERE
        RIGHT(phone_number, 10) = RIGHT(#{phoneNumber}, 10)
        AND delete_flag = 0
        ORDER BY
        CAST(CONCAT('1', RIGHT(phone_number, 10)) AS UNSIGNED) ASC
    </select>

    <select id="selectCar" resultType="com.lgjy.wx.domain.WxSpecialUserCar">
        <include refid="selectSpecialUserCarVo"/>
        where special_user_id = #{userId}
        and plate_no = #{plateNo}
        and phone_number = #{phoneNumber}
        and delete_flag = 0
    </select>

    <insert id="insertWxSpecialUserCar">
        insert into mini_special_user_car
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="specialUserId != null">
                special_user_id,
            </if>
            <if test="warehouseId != null">
                warehouse_id,
            </if>
            <if test="phoneNumber != null">
                phone_number,
            </if>
            <if test="plateNo != null">
                plate_no,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="specialUserId != null">
                #{specialUserId},
            </if>
            <if test="warehouseId != null">
                #{warehouseId},
            </if>
            <if test="phoneNumber != null">
                #{phoneNumber},
            </if>
            <if test="plateNo != null">
                #{plateNo},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag},
            </if>
        </trim>
    </insert>
</mapper>
