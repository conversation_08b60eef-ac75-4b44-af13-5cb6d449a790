#!/bin/bash

# API健康检查测试脚本
# 用于检查各个API端点的健康状态

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 默认参数
TEST_ENV="test"
TIMEOUT=10
RETRY_COUNT=3

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            TEST_ENV="${1#*=}"
            shift
            ;;
        --timeout=*)
            TIMEOUT="${1#*=}"
            shift
            ;;
        --retry=*)
            RETRY_COUNT="${1#*=}"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV        测试环境 (test|staging|prod) 默认: test"
            echo "  --timeout=SEC    超时时间（秒） 默认: 10"
            echo "  --retry=COUNT    重试次数 默认: 3"
            echo "  -h, --help       显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 根据环境设置API URL
# 注意: 所有API请求都通过nginx转发，遵循双层转发架构
case $TEST_ENV in
    "test")
        BASE_DOMAIN="test-parknew.lgfw24hours.com:3443"
        API_BASE_URL="https://${BASE_DOMAIN}/prod-api"
        ACTUATOR_URL="https://${BASE_DOMAIN}/prod-api/actuator"
        FRONTEND_URL="https://${BASE_DOMAIN}/"
        JENKINS_URL="https://${BASE_DOMAIN}/jenkins"
        ;;
    "staging")
        BASE_DOMAIN="test-parknew.lgfw24hours.com:3443"
        API_BASE_URL="https://${BASE_DOMAIN}/prod-api"
        ACTUATOR_URL="https://${BASE_DOMAIN}/prod-api/actuator"
        FRONTEND_URL="https://${BASE_DOMAIN}/"
        JENKINS_URL="https://${BASE_DOMAIN}/jenkins"
        ;;
    "prod")
        BASE_DOMAIN="test-parknew.lgfw24hours.com:3443"
        API_BASE_URL="https://${BASE_DOMAIN}/prod-api"
        ACTUATOR_URL="https://${BASE_DOMAIN}/prod-api/actuator"
        FRONTEND_URL="https://${BASE_DOMAIN}/"
        JENKINS_URL="https://${BASE_DOMAIN}/jenkins"
        ;;
    *)
        log "ERROR" "未知环境: $TEST_ENV"
        exit 1
        ;;
esac

log "INFO" "开始API健康检查 - 环境: $TEST_ENV"
log "INFO" "API基础URL: $API_BASE_URL"
log "INFO" "Actuator URL: $ACTUATOR_URL"
log "INFO" "前端URL: $FRONTEND_URL"
log "INFO" "Jenkins URL: $JENKINS_URL"

# 测试计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 带重试的HTTP请求函数
http_request_with_retry() {
    local url="$1"
    local method="${2:-GET}"
    local data="${3:-}"
    local expected_status="${4:-200}"
    
    for i in $(seq 1 $RETRY_COUNT); do
        local response
        local status_code
        
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
                -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$url" 2>/dev/null || echo -e "\n000")
        else
            response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
                -X "$method" \
                "$url" 2>/dev/null || echo -e "\n000")
        fi
        
        status_code=$(echo "$response" | tail -n1)
        
        if [ "$status_code" = "$expected_status" ]; then
            return 0
        fi
        
        if [ $i -lt $RETRY_COUNT ]; then
            log "WARN" "请求失败 (状态码: $status_code)，重试 $i/$RETRY_COUNT"
            sleep 2
        fi
    done
    
    log "ERROR" "请求失败，状态码: $status_code"
    return 1
}

# 检查函数
check_endpoint() {
    local name="$1"
    local url="$2"
    local method="${3:-GET}"
    local data="${4:-}"
    local expected_status="${5:-200}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    log "INFO" "检查: $name"
    
    if http_request_with_retry "$url" "$method" "$data" "$expected_status"; then
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        log "INFO" "✓ $name - 正常"
        return 0
    else
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        log "ERROR" "✗ $name - 异常"
        return 1
    fi
}

# 执行健康检查
log "INFO" "检查Spring Boot Actuator端点..."
check_endpoint "应用健康状态" "$ACTUATOR_URL/health"
check_endpoint "应用信息" "$ACTUATOR_URL/info"
check_endpoint "应用指标" "$ACTUATOR_URL/metrics"

log "INFO" "检查数据库和缓存连接..."
check_endpoint "数据库连接" "$ACTUATOR_URL/health/db"
check_endpoint "Redis连接" "$ACTUATOR_URL/health/redis"

log "INFO" "检查核心API端点..."
check_endpoint "API状态" "$API_BASE_URL/status"
check_endpoint "系统信息" "$API_BASE_URL/system/info"

log "INFO" "检查业务API端点..."
check_endpoint "停车场列表" "$API_BASE_URL/parking/list"
check_endpoint "用户信息" "$API_BASE_URL/user/info"
check_endpoint "订单统计" "$API_BASE_URL/order/stats"

log "INFO" "检查认证相关端点..."
check_endpoint "登录接口" "$API_BASE_URL/auth/login" "POST" '{"username":"healthcheck","password":"healthcheck"}' "200"

# 输出检查结果
log "INFO" "API健康检查完成"
log "INFO" "检查结果: 总计 $TOTAL_CHECKS, 正常 $PASSED_CHECKS, 异常 $FAILED_CHECKS"

if [ $FAILED_CHECKS -gt 0 ]; then
    log "ERROR" "API健康检查失败，有 $FAILED_CHECKS 个端点异常"
    exit 1
else
    log "INFO" "所有API端点健康检查通过"
    exit 0
fi
