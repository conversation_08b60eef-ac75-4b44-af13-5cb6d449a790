import{v as U,_ as Ce,O as Pe,d as xe,r as w,C as Ie,T as Le,y as Se,x as Te,e as c,Q as te,c as q,o as r,R as V,k as x,f as e,S as Ue,l,h as a,m as B,L as ne,M as oe,j as m,n as u,t as i,P as se,i as f,A as $e,B as qe}from"./index-D_FV2sri.js";import{C as de}from"./index-C9zsUI-h.js";function Me(h){return U({url:"/system/merchant/info/list",method:"get",params:h})}function ue(h){return U({url:"/system/merchant/info/"+h,method:"get"})}function De(h){return U({url:"/system/merchant/info",method:"post",data:h})}function Re(h){return U({url:"/system/merchant/info",method:"put",data:h})}function ze(h){return U({url:"/system/merchant/info/"+h,method:"delete"})}function Be(){return U({url:"/system/merchant/info/warehouseOptions",method:"get"})}const E=h=>($e("data-v-64b9a798"),h=h(),qe(),h),Qe={class:"app-container"},Oe={key:0,class:"query-condition-tags"},Ke={key:0,class:"merchant-detail-view"},je={class:"card-header"},Ee=E(()=>f("span",{class:"header-title"},"基本信息",-1)),Fe={class:"license-text"},Ye={class:"company-text"},Ae=E(()=>f("div",{class:"card-header"},[f("span",{class:"header-title"},"负责人信息")],-1)),He={class:"contact-name"},We={class:"contact-phone"},Xe={class:"contact-card"},Ge=E(()=>f("div",{class:"card-header"},[f("span",{class:"header-title"},"系统信息")],-1)),Je={class:"dialog-footer"},Ze=Pe({name:"MerchantInfo"}),ea=Object.assign(Ze,{components:{CustomPagination:de}},{setup(h){const{proxy:k}=xe(),F=w([]),M=w([]),N=w(!1),Q=w(!0),D=w(!0),O=w([]),Y=w(!0),A=w(!0),H=w(0),R=w(""),T=w(!1),re=Ie({form:{},queryParams:{pageNum:1,pageSize:10,merchantName:null,warehouseId:null,businessLicense:null,headName:null,headPhone:null},rules:{merchantName:[{required:!0,message:"商户名称不能为空",trigger:"blur"}],warehouseId:[{required:!0,message:"所属场库不能为空",trigger:"change"}],businessLicense:[{required:!0,message:"营业执照不能为空",trigger:"blur"}],headPhone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],headCard:[{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号码",trigger:"blur"}]}}),{queryParams:o,form:s,rules:ie}=Le(re);function I(){Q.value=!0,Me(o.value).then(d=>{F.value=d.rows,H.value=d.total,Q.value=!1})}function C(){o.value.pageNum=1,I()}function me(){k.resetForm("queryRef"),C()}function ce(d){O.value=d.map(t=>t.id),Y.value=d.length!=1,A.value=!d.length}function pe(){z(),T.value=!1,G(),N.value=!0,R.value="添加商户信息"}function W(d){z(),T.value=!1;const t=d.id||O.value;ue(t).then(p=>{s.value=p.data,N.value=!0,R.value="修改商户信息"})}function he(d){z(),T.value=!0;const t=d.id;ue(t).then(p=>{s.value=p.data,N.value=!0,R.value="查看商户信息"})}function fe(){k.$refs.merchantRef.validate(d=>{d&&(s.value.id!=null?Re(s.value).then(t=>{k.$modal.msgSuccess("修改成功"),N.value=!1,I()}):De(s.value).then(t=>{k.$modal.msgSuccess("新增成功"),N.value=!1,I()}))})}function X(d){const t=d.id||O.value;k.$modal.confirm('是否确认删除商户信息编号为"'+t+'"的数据项？').then(function(){return ze(t)}).then(()=>{I(),k.$modal.msgSuccess("删除成功")}).catch(()=>{})}function _e(){k.download("system/merchant/info/export",{...o.value},`merchant_info_${new Date().getTime()}.xlsx`)}function ve(){N.value=!1,z()}function z(){s.value={id:null,merchantName:null,warehouseId:null,businessLicense:null,headName:null,headCard:null,headPhone:null,moveTime:null,remark:null},k.resetForm("merchantRef")}function G(){Be().then(d=>{M.value=d.data})}function be(d){const t=M.value.find(p=>p.id===d);return t?t.warehouseName:"未知场库"}function $(d){o.value[d]=null,C()}function ye(){o.value.merchantName=null,o.value.warehouseId=null,o.value.businessLicense=null,o.value.headName=null,o.value.headPhone=null,C()}const J=Se(()=>o.value.merchantName||o.value.warehouseId||o.value.businessLicense||o.value.headName||o.value.headPhone);return Te(()=>{I(),G()}),(d,t)=>{const p=c("el-input"),_=c("el-form-item"),Z=c("el-option"),ee=c("el-select"),b=c("el-button"),ae=c("el-form"),P=c("el-tag"),y=c("el-col"),ge=c("right-toolbar"),L=c("el-row"),g=c("el-table-column"),we=c("el-table"),v=c("el-descriptions-item"),K=c("el-descriptions"),j=c("el-card"),Ne=c("el-date-picker"),Ve=c("el-dialog"),S=te("hasPermi"),ke=te("loading");return r(),q("div",Qe,[V(e(ae,{model:l(o),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[e(_,{label:"商户名称",prop:"merchantName"},{default:a(()=>[e(p,{modelValue:l(o).merchantName,"onUpdate:modelValue":t[0]||(t[0]=n=>l(o).merchantName=n),placeholder:"请输入商户名称",clearable:"",style:{width:"200px"},onKeyup:B(C,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"所属场库",prop:"warehouseId"},{default:a(()=>[e(ee,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[1]||(t[1]=n=>l(o).warehouseId=n),placeholder:"请选择场库",clearable:"",style:{width:"200px"}},{default:a(()=>[(r(!0),q(ne,null,oe(l(M),n=>(r(),m(Z,{key:n.id,label:n.warehouseName,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"营业执照",prop:"businessLicense"},{default:a(()=>[e(p,{modelValue:l(o).businessLicense,"onUpdate:modelValue":t[2]||(t[2]=n=>l(o).businessLicense=n),placeholder:"请输入营业执照",clearable:"",style:{width:"200px"},onKeyup:B(C,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"负责人姓名",prop:"headName"},{default:a(()=>[e(p,{modelValue:l(o).headName,"onUpdate:modelValue":t[3]||(t[3]=n=>l(o).headName=n),placeholder:"请输入负责人姓名",clearable:"",style:{width:"200px"},onKeyup:B(C,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"负责人电话",prop:"headPhone"},{default:a(()=>[e(p,{modelValue:l(o).headPhone,"onUpdate:modelValue":t[4]||(t[4]=n=>l(o).headPhone=n),placeholder:"请输入负责人电话",clearable:"",style:{width:"200px"},onKeyup:B(C,["enter"])},null,8,["modelValue"])]),_:1}),e(_,null,{default:a(()=>[e(b,{type:"primary",icon:"Search",onClick:C},{default:a(()=>[u("搜索")]),_:1}),e(b,{icon:"Refresh",onClick:me},{default:a(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ue,l(D)]]),l(J)?(r(),q("div",Oe,[l(o).merchantName?(r(),m(P,{key:0,closable:"",onClose:t[5]||(t[5]=n=>$("merchantName")),class:"query-tag"},{default:a(()=>[u(" 商户名称: "+i(l(o).merchantName),1)]),_:1})):x("",!0),l(o).warehouseId?(r(),m(P,{key:1,closable:"",onClose:t[6]||(t[6]=n=>$("warehouseId")),class:"query-tag"},{default:a(()=>[u(" 所属场库: "+i(be(l(o).warehouseId)),1)]),_:1})):x("",!0),l(o).businessLicense?(r(),m(P,{key:2,closable:"",onClose:t[7]||(t[7]=n=>$("businessLicense")),class:"query-tag"},{default:a(()=>[u(" 营业执照: "+i(l(o).businessLicense),1)]),_:1})):x("",!0),l(o).headName?(r(),m(P,{key:3,closable:"",onClose:t[8]||(t[8]=n=>$("headName")),class:"query-tag"},{default:a(()=>[u(" 负责人姓名: "+i(l(o).headName),1)]),_:1})):x("",!0),l(o).headPhone?(r(),m(P,{key:4,closable:"",onClose:t[9]||(t[9]=n=>$("headPhone")),class:"query-tag"},{default:a(()=>[u(" 负责人电话: "+i(l(o).headPhone),1)]),_:1})):x("",!0),l(J)?(r(),m(b,{key:5,type:"text",icon:"Refresh",onClick:ye,class:"clear-all-btn"},{default:a(()=>[u(" 清空 ")]),_:1})):x("",!0)])):x("",!0),e(L,{gutter:10,class:"mb8"},{default:a(()=>[e(y,{span:1.5},{default:a(()=>[V((r(),m(b,{type:"primary",plain:"",icon:"Plus",onClick:pe},{default:a(()=>[u("新增")]),_:1})),[[S,["merchant:info:add"]]])]),_:1}),e(y,{span:1.5},{default:a(()=>[V((r(),m(b,{type:"success",plain:"",icon:"Edit",disabled:l(Y),onClick:W},{default:a(()=>[u("修改")]),_:1},8,["disabled"])),[[S,["merchant:info:edit"]]])]),_:1}),e(y,{span:1.5},{default:a(()=>[V((r(),m(b,{type:"danger",plain:"",icon:"Delete",disabled:l(A),onClick:X},{default:a(()=>[u("删除")]),_:1},8,["disabled"])),[[S,["merchant:info:remove"]]])]),_:1}),e(y,{span:1.5},{default:a(()=>[V((r(),m(b,{type:"warning",plain:"",icon:"Download",onClick:_e},{default:a(()=>[u("导出")]),_:1})),[[S,["merchant:info:export"]]])]),_:1}),e(ge,{showSearch:l(D),"onUpdate:showSearch":t[10]||(t[10]=n=>se(D)?D.value=n:null),onQueryTable:I},null,8,["showSearch"])]),_:1}),V((r(),m(we,{data:l(F),onSelectionChange:ce},{default:a(()=>[e(g,{type:"selection",width:"55",align:"center"}),e(g,{label:"商户名称",align:"center",prop:"merchantName","show-overflow-tooltip":!0}),e(g,{label:"所属场库",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),e(g,{label:"项目名称",align:"center",prop:"projectName","show-overflow-tooltip":!0}),e(g,{label:"运营商",align:"center",prop:"companyName","show-overflow-tooltip":!0}),e(g,{label:"营业执照",align:"center",prop:"businessLicense","show-overflow-tooltip":!0}),e(g,{label:"负责人姓名",align:"center",prop:"headName"}),e(g,{label:"负责人电话",align:"center",prop:"headPhone"}),e(g,{label:"迁移时间",align:"center",prop:"moveTime",width:"180"},{default:a(n=>[f("span",null,i(d.parseTime(n.row.moveTime)),1)]),_:1}),e(g,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(n=>[f("span",null,i(d.parseTime(n.row.createTime)),1)]),_:1}),e(g,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(n=>[V((r(),m(b,{link:"",type:"primary",icon:"View",onClick:le=>he(n.row)},{default:a(()=>[u("查看")]),_:2},1032,["onClick"])),[[S,["merchant:info:query"]]]),V((r(),m(b,{link:"",type:"primary",icon:"Edit",onClick:le=>W(n.row)},{default:a(()=>[u("修改")]),_:2},1032,["onClick"])),[[S,["merchant:info:edit"]]]),V((r(),m(b,{link:"",type:"primary",icon:"Delete",onClick:le=>X(n.row)},{default:a(()=>[u("删除")]),_:2},1032,["onClick"])),[[S,["merchant:info:remove"]]])]),_:1})]),_:1},8,["data"])),[[ke,l(Q)]]),e(de,{total:l(H),"current-page":l(o).pageNum,"onUpdate:currentPage":t[11]||(t[11]=n=>l(o).pageNum=n),"page-size":l(o).pageSize,"onUpdate:pageSize":t[12]||(t[12]=n=>l(o).pageSize=n),onPagination:I},null,8,["total","current-page","page-size"]),e(Ve,{title:l(R),modelValue:l(N),"onUpdate:modelValue":t[20]||(t[20]=n=>se(N)?N.value=n:null),width:"800px","append-to-body":""},{footer:a(()=>[f("div",Je,[l(T)?x("",!0):(r(),m(b,{key:0,type:"primary",onClick:fe},{default:a(()=>[u("确 定")]),_:1})),e(b,{onClick:ve},{default:a(()=>[u(i(l(T)?"关 闭":"取 消"),1)]),_:1})])]),default:a(()=>[l(T)?(r(),q("div",Ke,[e(j,{class:"detail-card",shadow:"never"},{header:a(()=>[f("div",je,[Ee,e(P,{type:"primary",size:"small"},{default:a(()=>[u("商户编号："+i(l(s).id),1)]),_:1})])]),default:a(()=>[e(K,{column:2,border:""},{default:a(()=>[e(v,{label:"商户名称"},{default:a(()=>[e(P,{type:"primary",effect:"plain"},{default:a(()=>[u(i(l(s).merchantName),1)]),_:1})]),_:1}),e(v,{label:"营业执照"},{default:a(()=>[f("span",Fe,i(l(s).businessLicense),1)]),_:1}),e(v,{label:"所属场库"},{default:a(()=>[e(P,{type:"success"},{default:a(()=>[u(i(l(s).warehouseName||"-"),1)]),_:1})]),_:1}),e(v,{label:"项目名称"},{default:a(()=>[u(i(l(s).projectName||"-"),1)]),_:1}),e(v,{label:"运营商"},{default:a(()=>[f("span",Ye,i(l(s).companyName||"-"),1)]),_:1}),e(v,{label:"迁移时间"},{default:a(()=>[u(i(d.parseTime(l(s).moveTime,"{y}-{m}-{d} {h}:{i}:{s}")||"-"),1)]),_:1})]),_:1})]),_:1}),e(j,{class:"detail-card",shadow:"never"},{header:a(()=>[Ae]),default:a(()=>[e(K,{column:2,border:""},{default:a(()=>[e(v,{label:"负责人姓名"},{default:a(()=>[f("span",He,i(l(s).headName||"-"),1)]),_:1}),e(v,{label:"联系电话"},{default:a(()=>[f("span",We,i(l(s).headPhone||"-"),1)]),_:1}),e(v,{label:"身份证号",span:2},{default:a(()=>[f("span",Xe,i(l(s).headCard||"-"),1)]),_:1})]),_:1})]),_:1}),e(j,{class:"detail-card",shadow:"never"},{header:a(()=>[Ge]),default:a(()=>[e(K,{column:2,border:""},{default:a(()=>[e(v,{label:"创建时间"},{default:a(()=>[u(i(d.parseTime(l(s).createTime,"{y}-{m}-{d} {h}:{i}:{s}")||"-"),1)]),_:1}),e(v,{label:"更新时间"},{default:a(()=>[u(i(d.parseTime(l(s).updateTime,"{y}-{m}-{d} {h}:{i}:{s}")||"-"),1)]),_:1}),e(v,{label:"创建者"},{default:a(()=>[u(i(l(s).createdBy||"-"),1)]),_:1}),e(v,{label:"更新者"},{default:a(()=>[u(i(l(s).updatedBy||"-"),1)]),_:1})]),_:1})]),_:1})])):(r(),m(ae,{key:1,ref:"merchantRef",model:l(s),rules:l(ie),"label-width":"100px"},{default:a(()=>[e(L,null,{default:a(()=>[e(y,{span:24},{default:a(()=>[e(_,{label:"商户名称",prop:"merchantName"},{default:a(()=>[e(p,{modelValue:l(s).merchantName,"onUpdate:modelValue":t[13]||(t[13]=n=>l(s).merchantName=n),placeholder:"请输入商户名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(L,null,{default:a(()=>[e(y,{span:24},{default:a(()=>[e(_,{label:"所属场库",prop:"warehouseId"},{default:a(()=>[e(ee,{modelValue:l(s).warehouseId,"onUpdate:modelValue":t[14]||(t[14]=n=>l(s).warehouseId=n),placeholder:"请选择场库",style:{width:"100%"}},{default:a(()=>[(r(!0),q(ne,null,oe(l(M),n=>(r(),m(Z,{key:n.id,label:n.warehouseName,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(L,null,{default:a(()=>[e(y,{span:24},{default:a(()=>[e(_,{label:"营业执照",prop:"businessLicense"},{default:a(()=>[e(p,{modelValue:l(s).businessLicense,"onUpdate:modelValue":t[15]||(t[15]=n=>l(s).businessLicense=n),placeholder:"请输入营业执照"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(L,null,{default:a(()=>[e(y,{span:12},{default:a(()=>[e(_,{label:"负责人姓名",prop:"headName"},{default:a(()=>[e(p,{modelValue:l(s).headName,"onUpdate:modelValue":t[16]||(t[16]=n=>l(s).headName=n),placeholder:"请输入负责人姓名"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:a(()=>[e(_,{label:"负责人电话",prop:"headPhone"},{default:a(()=>[e(p,{modelValue:l(s).headPhone,"onUpdate:modelValue":t[17]||(t[17]=n=>l(s).headPhone=n),placeholder:"请输入负责人电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(L,null,{default:a(()=>[e(y,{span:24},{default:a(()=>[e(_,{label:"负责人身份证",prop:"headCard"},{default:a(()=>[e(p,{modelValue:l(s).headCard,"onUpdate:modelValue":t[18]||(t[18]=n=>l(s).headCard=n),placeholder:"请输入负责人身份证"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(L,null,{default:a(()=>[e(y,{span:24},{default:a(()=>[e(_,{label:"迁移时间",prop:"moveTime"},{default:a(()=>[e(Ne,{clearable:"",modelValue:l(s).moveTime,"onUpdate:modelValue":t[19]||(t[19]=n=>l(s).moveTime=n),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择迁移时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]))]),_:1},8,["title","modelValue"])])}}}),ta=Ce(ea,[["__scopeId","data-v-64b9a798"]]);export{ta as default};
