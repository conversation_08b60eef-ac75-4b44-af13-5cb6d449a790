#!/bin/bash

# 冒烟测试脚本
# 用于验证部署后的基本功能

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 默认参数
TEST_ENV="test"
VERBOSE="false"
TIMEOUT=30

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            TEST_ENV="${1#*=}"
            shift
            ;;
        --verbose)
            VERBOSE="true"
            shift
            ;;
        --timeout=*)
            TIMEOUT="${1#*=}"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV        测试环境 (test|staging|prod) 默认: test"
            echo "  --verbose        详细输出"
            echo "  --timeout=SEC    超时时间（秒） 默认: 30"
            echo "  -h, --help       显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 根据环境设置测试URL
# 注意: 所有流量都通过nginx转发，不直接访问容器端口
case $TEST_ENV in
    "test")
        BASE_DOMAIN="test-parknew.lgfw24hours.com:3443"
        FRONTEND_URL="https://${BASE_DOMAIN}/"
        API_URL="https://${BASE_DOMAIN}/prod-api"  # 修正API路径
        JENKINS_URL="https://${BASE_DOMAIN}/jenkins"
        ;;
    "staging")
        BASE_DOMAIN="test-parknew.lgfw24hours.com:3443"
        FRONTEND_URL="https://${BASE_DOMAIN}/"
        API_URL="https://${BASE_DOMAIN}/prod-api"
        JENKINS_URL="https://${BASE_DOMAIN}/jenkins"
        ;;
    "prod")
        BASE_DOMAIN="test-parknew.lgfw24hours.com:3443"
        FRONTEND_URL="https://${BASE_DOMAIN}/"
        API_URL="https://${BASE_DOMAIN}/prod-api"
        JENKINS_URL="https://${BASE_DOMAIN}/jenkins"
        ;;
    *)
        log "ERROR" "未知环境: $TEST_ENV"
        exit 1
        ;;
esac

log "INFO" "开始冒烟测试 - 环境: $TEST_ENV"
log "INFO" "前端URL: $FRONTEND_URL"
log "INFO" "API URL: $API_URL"
log "INFO" "Jenkins URL: $JENKINS_URL"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "执行测试: $test_name"
    fi
    
    if eval "$test_command"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        if [ "$VERBOSE" = "true" ]; then
            log "INFO" "✓ $test_name - 通过"
        fi
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log "ERROR" "✗ $test_name - 失败"
        return 1
    fi
}

# 网络连通性测试
test_network_connectivity() {
    # 测试外部域名解析
    nslookup test-parknew.lgfw24hours.com > /dev/null
}

# 系统nginx配置测试
test_nginx_config() {
    # 检查nginx配置是否正确
    nginx -t > /dev/null 2>&1
}

# nginx服务状态测试
test_nginx_service() {
    # 检查nginx服务是否运行
    systemctl is-active nginx > /dev/null 2>&1
}

# 前端访问测试
test_frontend_access() {
    curl -f -s --max-time $TIMEOUT "$FRONTEND_URL" > /dev/null
}

# Jenkins访问测试
test_jenkins_access() {
    curl -f -s --max-time $TIMEOUT "$JENKINS_URL" > /dev/null
}

# API健康检查测试（通过nginx转发）
test_api_health() {
    curl -f -s --max-time $TIMEOUT "$API_URL/actuator/health" > /dev/null
}

# API状态测试
test_api_status() {
    curl -f -s --max-time $TIMEOUT "$API_URL/v1/status" > /dev/null
}

# 数据库连接测试（通过nginx转发）
test_database_connection() {
    curl -f -s --max-time $TIMEOUT "$API_URL/actuator/health/db" | grep -q "UP"
}

# Redis连接测试（通过nginx转发）
test_redis_connection() {
    curl -f -s --max-time $TIMEOUT "$API_URL/actuator/health/redis" | grep -q "UP"
}

# 登录接口测试
test_login_api() {
    local response=$(curl -s --max-time $TIMEOUT -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"test","password":"test"}' \
        "$API_URL/v1/auth/login")

    echo "$response" | grep -q "token\|success"
}

# 停车场列表接口测试
test_parking_list_api() {
    curl -f -s --max-time $TIMEOUT "$API_URL/v1/parking/list" > /dev/null
}

# Docker容器状态测试
test_docker_containers() {
    # 检查关键容器是否运行
    docker ps --filter "name=jenkins-park" --filter "status=running" | grep -q "jenkins-park" &&
    docker ps --filter "name=park-nginx" --filter "status=running" | grep -q "park-nginx"
}

# 执行所有测试
log "INFO" "执行网络基础设施测试..."
run_test "网络连通性" "test_network_connectivity"
run_test "Nginx配置" "test_nginx_config"
run_test "Nginx服务状态" "test_nginx_service"
run_test "Docker容器状态" "test_docker_containers"

log "INFO" "执行应用访问测试..."
run_test "前端访问" "test_frontend_access"
run_test "Jenkins访问" "test_jenkins_access"
run_test "API健康检查" "test_api_health"
run_test "API状态检查" "test_api_status"

log "INFO" "执行数据库连接测试..."
run_test "数据库连接" "test_database_connection"
run_test "Redis连接" "test_redis_connection"

log "INFO" "执行业务接口测试..."
run_test "登录接口" "test_login_api"
run_test "停车场列表接口" "test_parking_list_api"

# 输出测试结果
log "INFO" "冒烟测试完成"
log "INFO" "测试结果: 总计 $TOTAL_TESTS, 通过 $PASSED_TESTS, 失败 $FAILED_TESTS"

if [ $FAILED_TESTS -gt 0 ]; then
    log "ERROR" "冒烟测试失败，有 $FAILED_TESTS 个测试未通过"
    exit 1
else
    log "INFO" "所有冒烟测试通过"
    exit 0
fi
