#!/bin/bash

# 停车管理系统健康检查脚本
# 作者: DevOps Team
# 版本: 1.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 解析命令行参数
CHECK_ENV="test"
CHECK_SERVICE=""
TIMEOUT="30"
VERBOSE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            CHECK_ENV="${1#*=}"
            shift
            ;;
        --service=*)
            CHECK_SERVICE="${1#*=}"
            shift
            ;;
        --timeout=*)
            TIMEOUT="${1#*=}"
            shift
            ;;
        --verbose)
            VERBOSE="true"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV            检查环境 (test|prod) 默认: test"
            echo "  --service=SERVICE    检查指定服务"
            echo "  --timeout=SECONDS    超时时间 默认: 30"
            echo "  --verbose            详细输出"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查容器状态
check_container_status() {
    local service="$1"
    local container_name="park-${service,,}"
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "检查容器状态: $container_name"
    fi
    
    # 检查容器是否存在
    if ! docker ps -a --format "{{.Names}}" | grep -q "^$container_name$"; then
        log "ERROR" "容器不存在: $container_name"
        return 1
    fi
    
    # 检查容器是否运行
    if ! docker ps --format "{{.Names}}" | grep -q "^$container_name$"; then
        log "ERROR" "容器未运行: $container_name"
        return 1
    fi
    
    # 检查容器健康状态
    local container_status=$(docker inspect --format='{{.State.Status}}' "$container_name" 2>/dev/null || echo "unknown")
    
    if [ "$container_status" != "running" ]; then
        log "ERROR" "容器状态异常: $container_name ($container_status)"
        return 1
    fi
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "容器状态正常: $container_name"
    fi
    
    return 0
}

# 检查HTTP健康端点
check_http_health() {
    local service="$1"
    local port="${SERVICE_PORTS[$service]}"
    local health_path="${SERVICE_HEALTH_PATHS[$service]}"
    
    if [ -z "$port" ] || [ -z "$health_path" ]; then
        if [ "$VERBOSE" = "true" ]; then
            log "INFO" "服务 $service 没有HTTP健康检查配置，跳过"
        fi
        return 0
    fi
    
    local health_url="http://localhost:$port$health_path"
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "检查HTTP健康端点: $health_url"
    fi
    
    # 使用curl检查健康端点
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time "$TIMEOUT" "$health_url" 2>/dev/null || echo "000")
    
    if [ "$response_code" = "200" ]; then
        if [ "$VERBOSE" = "true" ]; then
            log "INFO" "HTTP健康检查通过: $service ($response_code)"
        fi
        return 0
    else
        log "ERROR" "HTTP健康检查失败: $service ($response_code)"
        return 1
    fi
}

# 检查端口连通性
check_port_connectivity() {
    local service="$1"
    local port="${SERVICE_PORTS[$service]}"
    
    if [ -z "$port" ]; then
        if [ "$VERBOSE" = "true" ]; then
            log "INFO" "服务 $service 没有端口配置，跳过端口检查"
        fi
        return 0
    fi
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "检查端口连通性: localhost:$port"
    fi
    
    # 使用nc或telnet检查端口
    if command -v nc &> /dev/null; then
        if nc -z localhost "$port" 2>/dev/null; then
            if [ "$VERBOSE" = "true" ]; then
                log "INFO" "端口连通性检查通过: $service ($port)"
            fi
            return 0
        else
            log "ERROR" "端口连通性检查失败: $service ($port)"
            return 1
        fi
    else
        # 使用telnet作为备选
        if timeout 5 bash -c "echo >/dev/tcp/localhost/$port" 2>/dev/null; then
            if [ "$VERBOSE" = "true" ]; then
                log "INFO" "端口连通性检查通过: $service ($port)"
            fi
            return 0
        else
            log "ERROR" "端口连通性检查失败: $service ($port)"
            return 1
        fi
    fi
}

# 检查服务依赖
check_service_dependencies() {
    local service="$1"
    local dependencies="${SERVICE_DEPENDENCIES[$service]}"
    
    if [ -z "$dependencies" ]; then
        if [ "$VERBOSE" = "true" ]; then
            log "INFO" "服务 $service 没有依赖，跳过依赖检查"
        fi
        return 0
    fi
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "检查服务依赖: $service -> $dependencies"
    fi
    
    IFS=',' read -ra DEPS <<< "$dependencies"
    
    for dep in "${DEPS[@]}"; do
        dep=$(echo "$dep" | xargs)  # 去除空格
        
        if ! check_single_service "$dep" "false"; then
            log "ERROR" "依赖服务检查失败: $service -> $dep"
            return 1
        fi
    done
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "服务依赖检查通过: $service"
    fi
    
    return 0
}

# 检查单个服务
check_single_service() {
    local service="$1"
    local check_deps="${2:-true}"
    
    log "INFO" "检查服务: $service"
    
    # 检查容器状态
    if ! check_container_status "$service"; then
        return 1
    fi
    
    # 检查端口连通性
    if ! check_port_connectivity "$service"; then
        return 1
    fi
    
    # 检查HTTP健康端点
    if ! check_http_health "$service"; then
        return 1
    fi
    
    # 检查服务依赖
    if [ "$check_deps" = "true" ]; then
        if ! check_service_dependencies "$service"; then
            return 1
        fi
    fi
    
    log "INFO" "服务健康检查通过: $service"
    return 0
}

# 检查数据库连接
check_database_connection() {
    log "INFO" "检查数据库连接..."
    
    local mysql_container="park-mysql"
    
    if ! docker ps --format "{{.Names}}" | grep -q "^$mysql_container$"; then
        log "ERROR" "MySQL容器未运行"
        return 1
    fi
    
    # 检查数据库连接
    if docker exec "$mysql_container" mysql -u "$TEST_MYSQL_USER" -p"$TEST_MYSQL_PASSWORD" -e "SELECT 1" "$TEST_MYSQL_DATABASE" > /dev/null 2>&1; then
        log "INFO" "数据库连接检查通过"
        return 0
    else
        log "ERROR" "数据库连接检查失败"
        return 1
    fi
}

# 检查Redis连接
check_redis_connection() {
    log "INFO" "检查Redis连接..."
    
    local redis_container="park-redis"
    
    if ! docker ps --format "{{.Names}}" | grep -q "^$redis_container$"; then
        log "ERROR" "Redis容器未运行"
        return 1
    fi
    
    # 检查Redis连接
    if docker exec "$redis_container" redis-cli -a "$TEST_REDIS_PASSWORD" ping | grep -q "PONG"; then
        log "INFO" "Redis连接检查通过"
        return 0
    else
        log "ERROR" "Redis连接检查失败"
        return 1
    fi
}

# 检查Nacos连接
check_nacos_connection() {
    log "INFO" "检查Nacos连接..."
    
    local nacos_url="http://localhost:8848/nacos/v1/ns/operator/metrics"
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 "$nacos_url" 2>/dev/null || echo "000")
    
    if [ "$response_code" = "200" ]; then
        log "INFO" "Nacos连接检查通过"
        return 0
    else
        log "ERROR" "Nacos连接检查失败 ($response_code)"
        return 1
    fi
}

# 生成健康检查报告
generate_health_report() {
    local report_file="$LOGS_DIR/health-check-$(date +%Y%m%d-%H%M%S).json"
    
    log "INFO" "生成健康检查报告: $report_file"
    
    cat > "$report_file" << EOF
{
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "environment": "$CHECK_ENV",
    "overall_status": "healthy",
    "services": {
EOF

    local services=($(get_services_by_group "all"))
    local service_count=${#services[@]}
    local current_index=0
    
    for service in "${services[@]}"; do
        ((current_index++))
        
        local status="healthy"
        if ! check_single_service "$service" "false" > /dev/null 2>&1; then
            status="unhealthy"
        fi
        
        echo "        \"$service\": \"$status\"" >> "$report_file"
        
        if [ $current_index -lt $service_count ]; then
            echo "," >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
    }
}
EOF

    log "INFO" "健康检查报告已生成"
}

# 主检查函数
main() {
    log "INFO" "开始健康检查..."
    log "INFO" "检查环境: $CHECK_ENV"
    
    if [ -n "$CHECK_SERVICE" ]; then
        # 检查指定服务
        log "INFO" "检查指定服务: $CHECK_SERVICE"
        
        local service_upper=$(echo "$CHECK_SERVICE" | tr '[:lower:]' '[:upper:]')
        
        if ! check_single_service "$service_upper"; then
            log "ERROR" "服务健康检查失败: $CHECK_SERVICE"
            return 1
        fi
        
        log "INFO" "服务健康检查通过: $CHECK_SERVICE"
    else
        # 检查所有服务
        log "INFO" "检查所有服务"
        
        # 检查基础设施
        check_database_connection
        check_redis_connection
        check_nacos_connection
        
        # 检查所有服务
        local services=($(get_services_by_group "all"))
        local failed_services=()
        
        for service in "${services[@]}"; do
            if ! check_single_service "$service"; then
                failed_services+=("$service")
            fi
        done
        
        # 生成报告
        generate_health_report
        
        # 检查结果
        if [ ${#failed_services[@]} -eq 0 ]; then
            log "INFO" "所有服务健康检查通过"
            return 0
        else
            log "ERROR" "以下服务健康检查失败: ${failed_services[*]}"
            return 1
        fi
    fi
}

# 执行主函数
main "$@"
