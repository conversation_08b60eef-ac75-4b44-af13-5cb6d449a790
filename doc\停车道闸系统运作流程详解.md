# 停车道闸系统运作流程详解

## 概述

本文档基于司卓接口文档，详细梳理停车道闸系统的完整运作流程，包括车辆进场、出场、支付、异常处理等各个环节的业务逻辑，以及各个接口之间的调用关系和数据流转。

## 系统架构

停车道闸系统采用云端+本地的架构模式：

- **云端服务**：提供标准云接口，供第三方平台调用
- **本地设备**：车场内的道闸设备、车牌识别系统等
- **推送机制**：本地设备向第三方平台主动推送事件数据

## 核心业务流程

### 1. 车辆入场流程

#### 1.1 正常入场流程

1. **车辆到达入口道闸**
   - 触发车牌识别系统
   - 拍摄车辆照片（入场图片、全景图等）

2. **车牌识别与验证**
   - 识别车牌号码和车牌类型
   - 查询本地车辆数据库
   - 检查黑名单状态

3. **权限验证**
   - 检查车辆类型（临时车、月租车、免费车、储值车、访客车）
   - 验证通道权限（channelPermission）
   - 检查有效期（beginTime、endTime）

4. **交互式确认（可选）**
   - 如果启用交互式推送，系统会推送"车辆进出前确认"
   - 第三方平台返回是否允许通行的决策
   - 包含开闸指令和LED显示信息

5. **开闸入场**
   - 系统根据验证结果决定是否开闸
   - LED显示屏显示相关信息
   - 语音播报欢迎信息

6. **入场数据推送**
   - 推送车辆入场数据到第三方平台
   - 包含车辆信息、入场时间、通道信息等
   - 更新车位统计信息

#### 1.2 无牌车入场流程

1. **无牌车检测**
   - 车牌识别系统无法识别有效车牌
   - 或人工判断为无牌车

2. **无牌车入场接口调用**
   - 调用 `/transmit/noplate/entry` 接口
   - 通知指定通道的客户端处理
   - 需要客户端在线且IP匹配

3. **人工处理**
   - 现场操作员确认车辆信息
   - 手动开闸或拒绝入场
   - 记录无牌车信息

### 2. 车辆出场流程

#### 2.1 正常出场流程

1. **车辆到达出口道闸**
   - 触发车牌识别系统
   - 拍摄出场照片

2. **在场记录查询**
   - 根据车牌号查询入场记录
   - 验证车辆是否在场（isOnPark）
   - 获取入场时间等信息

3. **费用计算**
   - 根据停车时长和收费标准计算费用
   - 考虑已支付金额和优惠券抵扣
   - 计算实际应付金额

4. **支付状态确认**
   - 交互式推送：推送"车辆出场前支付确认"
   - 第三方平台返回支付状态
   - 或直接检查本地支付记录

5. **支付流程（如需要）**
   - 调用费用查询接口 `/transmit/price/queryv2`
   - 第三方平台完成支付操作
   - 调用缴费通知接口 `/transmit/pay/notice`

6. **开闸出场**
   - 验证支付完成后开闸
   - 推送车辆出场数据
   - 更新车位统计

#### 2.2 特殊出场情况

1. **储值车出场**
   - 自动从储值余额扣费
   - 推送储值车扣费数据
   - 更新用户余额

2. **免费车出场**
   - 直接开闸，无需支付
   - 记录出场信息

3. **异常出场处理**
   - 车牌识别失败的处理
   - 找不到入场记录的处理
   - 手动开闸的记录

### 3. 车辆管理流程

#### 3.1 车辆信息管理

1. **新增/修改车辆**
   - 接口：`/transmit/vehicle/save`
   - 支持批量操作
   - 自动推送车辆信息变更

2. **删除车辆**
   - 接口：`/transmit/vehicle/delete`
   - 推送车辆删除通知
   - 清理相关数据

3. **查询车辆**
   - 接口：`/transmit/vehicle/query`
   - 返回详细车辆信息
   - 包含储值余额、套餐信息等

#### 3.2 黑名单管理

1. **新增黑名单**
   - 接口：`/transmit/blacklist/save`
   - 设置失效时间
   - 立即生效

2. **删除黑名单**
   - 接口：`/transmit/blacklist/delete`
   - 恢复车辆正常通行权限

### 4. 优惠券管理流程

1. **优惠券发放**
   - 独立的接口地址和签名规则
   - 支持时长券、金额券、次券
   - 实时生效

2. **优惠券使用**
   - 在费用计算时自动抵扣
   - 优先使用即将过期的优惠券

## 接口调用关系图

### 标准云接口（第三方→云端）

```
车辆管理:
├── /transmit/vehicle/save      (新增/修改车辆)
├── /transmit/vehicle/delete    (删除车辆)
└── /transmit/vehicle/query     (查询车辆)

黑名单管理:
├── /transmit/blacklist/save    (新增黑名单)
└── /transmit/blacklist/delete  (删除黑名单)

场内管理:
├── /transmit/noplate/entry     (无牌车入场)
├── /transmit/price/queryv2     (查询费用)
├── /transmit/outpay/queryv2    (查询出口费用)
├── /transmit/pay/notice        (缴费通知)
└── /transmit/lot/query         (查询余位)

优惠券管理:
└── /CouponReport/CouponRelease (发放优惠券)
```

### 推送接口（云端→第三方）

```
事件推送:
├── 车辆入场推送
├── 车辆出场推送
├── 车辆信息变更推送
├── 在场车牌修改推送
├── 车辆删除推送
├── 车位变动推送
├── 储值车扣费推送
├── 手动开闸推送
├── 操作员变动推送
└── 操作员登录/退出推送

交互式推送:
├── 车辆进出前确认
└── 车辆出场前支付确认
```

## 数据流转说明

### 1. 签名验证机制

所有标准云接口（除优惠券外）都需要进行签名验证：

1. **签名生成**：
   - 将JSON参数按字典序排序
   - 使用 `key=value&` 格式拼接
   - 末尾拼接密钥
   - 计算32位大写MD5值

2. **签名验证**：
   - 服务端按相同规则生成签名
   - 对比客户端传入的签名值
   - 验证失败返回错误信息

### 2. 关键数据字段

- **parkingID**: 车场编号，全局唯一标识
- **timestamp**: 毫秒时间戳，防重放攻击
- **plate**: 车牌号，核心业务标识
- **OrderID**: 订单编号，一次停车唯一
- **channelIndex/channelNo**: 通道编号，区分不同出入口

### 3. 状态管理

- **车辆状态**: 在场/离场状态管理
- **支付状态**: 已支付/未支付/部分支付
- **车位状态**: 总车位/剩余车位/在场车辆数
- **设备状态**: 在线/离线状态监控

## 异常处理机制

### 1. 网络异常

- 推送失败重试机制
- 离线数据缓存
- 网络恢复后数据同步

### 2. 业务异常

- 车牌识别失败处理
- 重复入场/出场处理
- 费用计算异常处理
- 支付异常处理

### 3. 设备异常

- 道闸故障应急处理
- 摄像头故障备用方案
- 网络中断本地处理

## 监控与日志

### 1. 关键指标监控

- 接口调用成功率
- 推送成功率
- 车辆通行效率
- 支付成功率

### 2. 日志记录

- 所有接口调用日志
- 车辆进出场日志
- 异常处理日志
- 系统运行状态日志

## 总结

停车道闸系统是一个复杂的业务系统，涉及多个子系统的协调配合。通过标准化的接口设计和完善的推送机制，实现了车场设备与第三方平台的有效集成。系统具备良好的扩展性和容错性，能够满足不同规模停车场的业务需求。
