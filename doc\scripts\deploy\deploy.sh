#!/bin/bash

# 停车管理系统部署脚本
# 针对云效仓库和特定服务器环境定制
# 服务器: *************:1224
# 作者: DevOps Team
# 版本: 2.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 服务器特定配置
SERVER_HOST="*************"
SSH_PORT="1224"

# 网络架构配置
EXTERNAL_PORT="3443"
FIREWALL_INTERNAL_PORT="433"
JENKINS_SYSTEM_PORT="9001"
FRONTEND_SYSTEM_PORT="9433"
BACKEND_CONTAINER_PORT="8080"

# 访问地址配置
BASE_DOMAIN="test-parknew.lgfw24hours.com"
EXTERNAL_URL="https://${BASE_DOMAIN}:${EXTERNAL_PORT}"
JENKINS_URL="${EXTERNAL_URL}/jenkins"
FRONTEND_URL="${EXTERNAL_URL}/"
API_URL="${EXTERNAL_URL}/api"

# Docker容器名称
JENKINS_CONTAINER="jenkins-park"
NGINX_CONTAINER="park-nginx"
BACKEND_CONTAINER="park-backend"

# 解析命令行参数
DEPLOY_ENV="test"
DEPLOY_SERVICES="all"
SKIP_BACKUP="false"
FORCE_DEPLOY="false"
ROLLING_UPDATE="true"

while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            DEPLOY_ENV="${1#*=}"
            shift
            ;;
        --services=*)
            DEPLOY_SERVICES="${1#*=}"
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP="true"
            shift
            ;;
        --force)
            FORCE_DEPLOY="true"
            shift
            ;;
        --no-rolling)
            ROLLING_UPDATE="false"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV            部署环境 (test|prod) 默认: test"
            echo "  --services=SERVICES  部署服务 (all|backend|frontend|service_name) 默认: all"
            echo "  --skip-backup        跳过备份"
            echo "  --force              强制部署"
            echo "  --no-rolling         不使用滚动更新"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 验证部署环境
validate_environment() {
    log "INFO" "验证部署环境: $DEPLOY_ENV"
    
    case "$DEPLOY_ENV" in
        "test"|"prod")
            ;;
        *)
            log "ERROR" "不支持的部署环境: $DEPLOY_ENV"
            return 1
            ;;
    esac
    
    # 检查Docker Compose文件
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        log "ERROR" "Docker Compose文件不存在: $DOCKER_COMPOSE_FILE"
        return 1
    fi
    
    # 检查Docker服务
    if ! docker info > /dev/null 2>&1; then
        log "ERROR" "Docker服务未运行"
        return 1
    fi
    
    log "INFO" "环境验证通过"
}

# 创建备份
create_backup() {
    if [ "$SKIP_BACKUP" = "true" ]; then
        log "INFO" "跳过备份"
        return 0
    fi
    
    log "INFO" "创建部署前备份..."
    
    if ! "$SCRIPTS_ROOT/deploy/backup.sh" --env="$DEPLOY_ENV"; then
        log "ERROR" "备份创建失败"
        return 1
    fi
    
    log "INFO" "备份创建完成"
}

# 停止服务
stop_services() {
    local services=("$@")
    
    log "INFO" "停止服务: ${services[*]}"
    
    cd "$DEPLOY_DIR"
    
    for service in "${services[@]}"; do
        local container_name="park-${service,,}"
        
        if docker ps --format "{{.Names}}" | grep -q "^$container_name$"; then
            log "INFO" "停止容器: $container_name"
            docker-compose stop "$container_name" || true
        else
            log "DEBUG" "容器未运行: $container_name"
        fi
    done
}

# 启动服务
start_services() {
    local services=("$@")
    
    log "INFO" "启动服务: ${services[*]}"
    
    cd "$DEPLOY_DIR"
    
    # 按启动顺序排序
    local sorted_services=($(sort_services_by_order "${services[@]}"))
    
    for service in "${sorted_services[@]}"; do
        local container_name="park-${service,,}"
        
        log "INFO" "启动容器: $container_name"
        
        if ! docker-compose up -d "$container_name"; then
            log "ERROR" "启动容器失败: $container_name"
            return 1
        fi
        
        # 等待服务启动
        if ! wait_for_service "$service" 60 5; then
            log "ERROR" "服务启动超时: $service"
            return 1
        fi
        
        log "INFO" "服务启动成功: $service"
    done
}

# 滚动更新服务
rolling_update_service() {
    local service="$1"
    local container_name="park-${service,,}"
    
    log "INFO" "滚动更新服务: $service"
    
    cd "$DEPLOY_DIR"
    
    # 拉取最新镜像
    log "INFO" "拉取最新镜像: $service"
    if ! docker-compose pull "$container_name"; then
        log "ERROR" "拉取镜像失败: $service"
        return 1
    fi
    
    # 创建新容器
    log "INFO" "创建新容器: $container_name"
    if ! docker-compose up -d --no-deps "$container_name"; then
        log "ERROR" "创建容器失败: $container_name"
        return 1
    fi
    
    # 等待服务启动
    if ! wait_for_service "$service" 60 5; then
        log "ERROR" "服务启动超时: $service"
        return 1
    fi
    
    # 健康检查
    if ! "$SCRIPTS_ROOT/deploy/health-check.sh" --service="$service"; then
        log "ERROR" "健康检查失败: $service"
        return 1
    fi
    
    log "INFO" "滚动更新完成: $service"
    return 0
}

# 部署基础设施服务
deploy_infrastructure() {
    log "INFO" "部署基础设施服务..."
    
    local infra_services=($(get_services_by_group "infrastructure"))
    
    # 检查基础设施服务状态
    local services_to_start=()
    
    for service in "${infra_services[@]}"; do
        if ! is_service_running "$service"; then
            services_to_start+=("$service")
        else
            log "INFO" "基础设施服务已运行: $service"
        fi
    done
    
    if [ ${#services_to_start[@]} -gt 0 ]; then
        start_services "${services_to_start[@]}"
    fi
    
    # 等待基础设施服务就绪
    log "INFO" "等待基础设施服务就绪..."
    sleep 30
    
    # 验证基础设施服务
    for service in "${infra_services[@]}"; do
        if ! "$SCRIPTS_ROOT/deploy/health-check.sh" --service="$service"; then
            log "ERROR" "基础设施服务健康检查失败: $service"
            return 1
        fi
    done
    
    log "INFO" "基础设施服务部署完成"
}

# 部署后端服务
deploy_backend() {
    log "INFO" "部署后端服务..."
    
    local backend_services=($(get_services_by_group "backend"))
    
    if [ "$ROLLING_UPDATE" = "true" ]; then
        # 滚动更新
        for service in "${backend_services[@]}"; do
            if ! rolling_update_service "$service"; then
                log "ERROR" "后端服务滚动更新失败: $service"
                return 1
            fi
            
            # 服务间等待时间
            sleep 10
        done
    else
        # 批量重启
        stop_services "${backend_services[@]}"
        sleep 10
        start_services "${backend_services[@]}"
    fi
    
    log "INFO" "后端服务部署完成"
}

# 部署前端服务
deploy_frontend() {
    log "INFO" "部署前端服务..."
    
    local frontend_services=($(get_services_by_group "frontend"))
    
    if [ "$ROLLING_UPDATE" = "true" ]; then
        for service in "${frontend_services[@]}"; do
            if ! rolling_update_service "$service"; then
                log "ERROR" "前端服务滚动更新失败: $service"
                return 1
            fi
        done
    else
        stop_services "${frontend_services[@]}"
        sleep 5
        start_services "${frontend_services[@]}"
    fi
    
    log "INFO" "前端服务部署完成"
}

# 部署后验证
post_deploy_validation() {
    log "INFO" "执行部署后验证..."
    
    # 等待所有服务稳定
    sleep 30
    
    # 健康检查
    if ! "$SCRIPTS_ROOT/deploy/health-check.sh" --env="$DEPLOY_ENV"; then
        log "ERROR" "部署后健康检查失败"
        return 1
    fi
    
    # 功能测试（可选）
    # run_smoke_tests
    
    log "INFO" "部署后验证通过"
}

# 主部署函数
main() {
    log "INFO" "开始部署..."
    log "INFO" "部署环境: $DEPLOY_ENV"
    log "INFO" "部署服务: $DEPLOY_SERVICES"
    log "INFO" "滚动更新: $ROLLING_UPDATE"
    
    # 验证环境
    if ! validate_environment; then
        log "ERROR" "环境验证失败"
        return 1
    fi
    
    # 创建备份
    if ! create_backup; then
        log "ERROR" "备份创建失败"
        return 1
    fi
    
    # 根据部署服务类型执行部署
    case "$DEPLOY_SERVICES" in
        "all")
            deploy_infrastructure
            deploy_backend
            deploy_frontend
            ;;
        "infrastructure")
            deploy_infrastructure
            ;;
        "backend")
            deploy_infrastructure
            deploy_backend
            ;;
        "frontend")
            deploy_frontend
            ;;
        *)
            # 部署指定服务
            if ! rolling_update_service "$DEPLOY_SERVICES"; then
                log "ERROR" "服务部署失败: $DEPLOY_SERVICES"
                return 1
            fi
            ;;
    esac
    
    # 部署后验证
    if ! post_deploy_validation; then
        log "ERROR" "部署后验证失败"
        return 1
    fi
    
    log "INFO" "部署成功完成！"
    return 0
}

# 执行主函数
main "$@"
