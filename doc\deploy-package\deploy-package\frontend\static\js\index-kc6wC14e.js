import{v as x,O as fe,d as _e,r as _,C as be,T as ge,x as ye,e as d,Q as J,c as ve,o as v,R as w,f as e,S as he,l as a,h as l,m as E,n as r,j as k,P as K,t as f,i as Q,k as Ve}from"./index-D_FV2sri.js";import{C as W}from"./index-C9zsUI-h.js";function we(b){return x({url:"/system/special/user/list",method:"get",params:b})}function X(b){return x({url:"/system/special/user/"+b,method:"get"})}function ke(b){return x({url:"/system/special/user",method:"post",data:b})}function Ne(b){return x({url:"/system/special/user",method:"put",data:b})}function Te(b){return x({url:"/system/special/user/"+b,method:"delete"})}const Ue={class:"app-container"},Ce={class:"dialog-footer"},Se={class:"dialog-footer"},xe=fe({name:"SpecialUser"}),De=Object.assign(xe,{components:{CustomPagination:W}},{setup(b){const{proxy:h}=_e(),z=_([]),N=_(!1),C=_(!1),I=_(!0),P=_(!0),$=_([]),F=_(!0),M=_(!0),O=_(0),q=_(""),m=_({}),Y=be({form:{},queryParams:{pageNum:1,pageSize:10,nickName:null,phoneNumber:null,plateNo:null,userType:null},rules:{phoneNumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],userType:[{required:!0,message:"用户类型不能为空",trigger:"blur"}]}}),{queryParams:s,form:i,rules:Z}=ge(Y);function ee(u){return{VIP客户:"warning",集团客户:"warning"}[u]||"info"}function T(){I.value=!0,we(s.value).then(u=>{z.value=u.rows,O.value=u.total,I.value=!1})}function B(){i.value={id:null,nickName:null,phoneNumber:null,plateNo:null,userType:null,remark:null},h.resetForm("specialUserRef")}function S(){s.value.pageNum=1,T()}function le(){h.resetForm("queryRef"),S()}function ae(u){$.value=u.map(t=>t.id),F.value=u.length!=1,M.value=!u.length}function te(u){const t=u.id||$.value;X(t).then(p=>{m.value=p.data||{},C.value=!0}).catch(p=>{console.error("获取特殊会员详情失败:",p),h.$modal.msgError("获取特殊会员详情失败")})}function oe(){B(),N.value=!0,q.value="添加特殊会员"}function j(u){B();const t=u.id||$.value;X(t).then(p=>{i.value=p.data||{},N.value=!0,q.value="修改特殊会员"})}function ne(){h.$refs.specialUserRef.validate(u=>{u&&(i.value.id!=null?Ne(i.value).then(t=>{h.$modal.msgSuccess("修改成功"),N.value=!1,T()}):ke(i.value).then(t=>{h.$modal.msgSuccess("新增成功"),N.value=!1,T()}))})}function re(){N.value=!1,B()}function L(u){const t=u.id||$.value;h.$modal.confirm('是否确认删除特殊会员编号为"'+t+'"的数据项？').then(function(){return Te(t)}).then(()=>{T(),h.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ue(){h.download("special/user/export",{...s.value},`特殊会员_${new Date().getTime()}.xlsx`)}return ye(()=>{T()}),(u,t)=>{const p=d("el-input"),g=d("el-form-item"),D=d("el-option"),A=d("el-select"),c=d("el-button"),G=d("el-form"),R=d("el-col"),se=d("right-toolbar"),ie=d("el-row"),V=d("el-table-column"),de=d("el-tag"),pe=d("el-table"),H=d("el-dialog"),y=d("el-descriptions-item"),me=d("el-descriptions"),U=J("hasPermi"),ce=J("loading");return v(),ve("div",Ue,[w(e(G,{model:a(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(g,{label:"姓名",prop:"nickName"},{default:l(()=>[e(p,{modelValue:a(s).nickName,"onUpdate:modelValue":t[0]||(t[0]=o=>a(s).nickName=o),placeholder:"请输入姓名",clearable:"",style:{width:"200px"},onKeyup:E(S,["enter"])},null,8,["modelValue"])]),_:1}),e(g,{label:"手机号码",prop:"phoneNumber"},{default:l(()=>[e(p,{modelValue:a(s).phoneNumber,"onUpdate:modelValue":t[1]||(t[1]=o=>a(s).phoneNumber=o),placeholder:"请输入手机号码",clearable:"",style:{width:"200px"},onKeyup:E(S,["enter"])},null,8,["modelValue"])]),_:1}),e(g,{label:"车牌号",prop:"plateNo"},{default:l(()=>[e(p,{modelValue:a(s).plateNo,"onUpdate:modelValue":t[2]||(t[2]=o=>a(s).plateNo=o),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:E(S,["enter"])},null,8,["modelValue"])]),_:1}),e(g,{label:"用户类型",prop:"userType"},{default:l(()=>[e(A,{modelValue:a(s).userType,"onUpdate:modelValue":t[3]||(t[3]=o=>a(s).userType=o),placeholder:"请选择用户类型",clearable:"",style:{width:"200px"}},{default:l(()=>[e(D,{label:"VIP客户",value:"VIP客户"}),e(D,{label:"集团客户",value:"集团客户"})]),_:1},8,["modelValue"])]),_:1}),e(g,null,{default:l(()=>[e(c,{type:"primary",icon:"Search",onClick:S},{default:l(()=>[r("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:le},{default:l(()=>[r("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[he,a(P)]]),e(ie,{gutter:10,class:"mb8"},{default:l(()=>[e(R,{span:1.5},{default:l(()=>[w((v(),k(c,{type:"primary",plain:"",icon:"Plus",onClick:oe},{default:l(()=>[r("新增")]),_:1})),[[U,["special:user:add"]]])]),_:1}),e(R,{span:1.5},{default:l(()=>[w((v(),k(c,{type:"success",plain:"",icon:"Edit",disabled:a(F),onClick:j},{default:l(()=>[r("修改")]),_:1},8,["disabled"])),[[U,["special:user:edit"]]])]),_:1}),e(R,{span:1.5},{default:l(()=>[w((v(),k(c,{type:"danger",plain:"",icon:"Delete",disabled:a(M),onClick:L},{default:l(()=>[r("删除")]),_:1},8,["disabled"])),[[U,["special:user:remove"]]])]),_:1}),e(R,{span:1.5},{default:l(()=>[w((v(),k(c,{type:"warning",plain:"",icon:"Download",onClick:ue},{default:l(()=>[r("导出")]),_:1})),[[U,["special:user:export"]]])]),_:1}),e(se,{showSearch:a(P),"onUpdate:showSearch":t[4]||(t[4]=o=>K(P)?P.value=o:null),onQueryTable:T},null,8,["showSearch"])]),_:1}),w((v(),k(pe,{data:a(z),onSelectionChange:ae},{default:l(()=>[e(V,{type:"selection",width:"55",align:"center"}),e(V,{label:"ID",align:"center",prop:"id",width:"80"}),e(V,{label:"姓名",align:"center",prop:"nickName"}),e(V,{label:"手机号码",align:"center",prop:"phoneNumber",width:"120"}),e(V,{label:"车牌号",align:"center",prop:"plateNo",width:"120"}),e(V,{label:"用户类型",align:"center",prop:"userType",width:"120"},{default:l(o=>[e(de,{type:ee(o.row.userType)},{default:l(()=>[r(f(o.row.userType),1)]),_:2},1032,["type"])]),_:1}),e(V,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(o=>[Q("span",null,f(u.parseTime(o.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(V,{label:"备注",align:"center",prop:"remark"}),e(V,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[w((v(),k(c,{link:"",type:"primary",icon:"View",onClick:n=>te(o.row)},{default:l(()=>[r("查看")]),_:2},1032,["onClick"])),[[U,["special:user:query"]]]),w((v(),k(c,{link:"",type:"primary",icon:"Edit",onClick:n=>j(o.row)},{default:l(()=>[r("修改")]),_:2},1032,["onClick"])),[[U,["special:user:edit"]]]),w((v(),k(c,{link:"",type:"primary",icon:"Delete",onClick:n=>L(o.row)},{default:l(()=>[r("删除")]),_:2},1032,["onClick"])),[[U,["special:user:remove"]]])]),_:1})]),_:1},8,["data"])),[[ce,a(I)]]),e(W,{total:a(O),page:a(s).pageNum,"onUpdate:page":t[5]||(t[5]=o=>a(s).pageNum=o),limit:a(s).pageSize,"onUpdate:limit":t[6]||(t[6]=o=>a(s).pageSize=o),onPagination:T},null,8,["total","page","limit"]),e(H,{title:a(q),modelValue:a(N),"onUpdate:modelValue":t[12]||(t[12]=o=>K(N)?N.value=o:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[Q("div",Ce,[e(c,{type:"primary",onClick:ne},{default:l(()=>[r("确 定")]),_:1}),e(c,{onClick:re},{default:l(()=>[r("取 消")]),_:1})])]),default:l(()=>[e(G,{ref:"specialUserRef",model:a(i),rules:a(Z),"label-width":"100px"},{default:l(()=>[e(g,{label:"姓名",prop:"nickName"},{default:l(()=>[e(p,{modelValue:a(i).nickName,"onUpdate:modelValue":t[7]||(t[7]=o=>a(i).nickName=o),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),e(g,{label:"手机号码",prop:"phoneNumber"},{default:l(()=>[e(p,{modelValue:a(i).phoneNumber,"onUpdate:modelValue":t[8]||(t[8]=o=>a(i).phoneNumber=o),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),e(g,{label:"车牌号",prop:"plateNo"},{default:l(()=>[e(p,{modelValue:a(i).plateNo,"onUpdate:modelValue":t[9]||(t[9]=o=>a(i).plateNo=o),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1}),e(g,{label:"用户类型",prop:"userType"},{default:l(()=>[e(A,{modelValue:a(i).userType,"onUpdate:modelValue":t[10]||(t[10]=o=>a(i).userType=o),placeholder:"请选择用户类型",style:{width:"100%"}},{default:l(()=>[e(D,{label:"VIP客户",value:"VIP客户"}),e(D,{label:"集团客户",value:"集团客户"})]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"备注",prop:"remark"},{default:l(()=>[e(p,{modelValue:a(i).remark,"onUpdate:modelValue":t[11]||(t[11]=o=>a(i).remark=o),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(H,{title:"特殊会员详情",modelValue:a(C),"onUpdate:modelValue":t[14]||(t[14]=o=>K(C)?C.value=o:null),width:"600px","append-to-body":""},{footer:l(()=>[Q("div",Se,[e(c,{onClick:t[13]||(t[13]=o=>C.value=!1)},{default:l(()=>[r("关 闭")]),_:1})])]),default:l(()=>[e(me,{column:2,border:""},{default:l(()=>{var o;return[e(y,{label:"ID"},{default:l(()=>{var n;return[r(f(((n=a(m))==null?void 0:n.id)||"-"),1)]}),_:1}),e(y,{label:"姓名"},{default:l(()=>{var n;return[r(f(((n=a(m))==null?void 0:n.nickName)||"-"),1)]}),_:1}),e(y,{label:"手机号码"},{default:l(()=>{var n;return[r(f(((n=a(m))==null?void 0:n.phoneNumber)||"-"),1)]}),_:1}),e(y,{label:"车牌号"},{default:l(()=>{var n;return[r(f(((n=a(m))==null?void 0:n.plateNo)||"-"),1)]}),_:1}),e(y,{label:"用户类型",span:2},{default:l(()=>{var n;return[r(f(((n=a(m))==null?void 0:n.userType)||"-"),1)]}),_:1}),e(y,{label:"创建者"},{default:l(()=>{var n;return[r(f(((n=a(m))==null?void 0:n.createByName)||"-"),1)]}),_:1}),e(y,{label:"创建时间"},{default:l(()=>{var n;return[r(f(u.parseTime((n=a(m))==null?void 0:n.createTime)),1)]}),_:1}),e(y,{label:"更新者"},{default:l(()=>{var n;return[r(f(((n=a(m))==null?void 0:n.updateByName)||"-"),1)]}),_:1}),e(y,{label:"更新时间"},{default:l(()=>{var n;return[r(f(u.parseTime((n=a(m))==null?void 0:n.updateTime)),1)]}),_:1}),(o=a(m))!=null&&o.remark?(v(),k(y,{key:0,label:"备注",span:2},{default:l(()=>[r(f(a(m).remark),1)]),_:1})):Ve("",!0)]}),_:1})]),_:1},8,["modelValue"])])}}});export{De as default};
