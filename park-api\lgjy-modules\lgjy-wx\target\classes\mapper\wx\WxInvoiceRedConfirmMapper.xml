<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxInvoiceRedConfirmMapper">
    <sql id="selectSicvInvoiceRedConfirmVo">
        select id,warehouse_id,
               user_id,invoice_id,
               invoice_type,
               red_confirmed_uuid,
               apply_reason,
               reverse_state,
               buyer_name,
               buyer_tax_code,
               red_confirm_no,
               confirm_state,
               invoice_code,
               total_price,
               total_tax,
               issue_date,
               seller_name,
               seller_tax_code,
               entry_identity,
               entry_date,
               confirm_date,
               reverse_invoice_code,
               reverse_date,
               reverse_total_price,
               reverse_total_tax,
               create_time,
               update_time,
               remark
        from mini_invoice_red_confirm
    </sql>

    <select id="selectRedConfirmByInvoiceId" resultType="com.lgjy.wx.domain.WxInvoiceRedConfirm">
        <include refid="selectSicvInvoiceRedConfirmVo"/>
        where invoice_id = #{invoiceId}
    </select>

    <insert id="insertSicvInvoiceRedConfirm">
        insert into sicv_invoice_red_confirm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="redConfirmedUuid != null and redConfirmedUuid != ''">red_confirmed_uuid,</if>
            <if test="applyReason != null">apply_reason,</if>
            <if test="reverseState != null">reverse_state,</if>
            <if test="buyerName != null">buyer_name,</if>
            <if test="buyerTaxCode != null">buyer_tax_code,</if>
            <if test="redConfirmNo != null">red_confirm_no,</if>
            <if test="confirmState != null">confirm_state,</if>
            <if test="invoiceCode != null">invoice_code,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="totalTax != null">total_tax,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="sellerName != null">seller_name,</if>
            <if test="sellerTaxCode != null">seller_tax_code,</if>
            <if test="entryIdentity != null">entry_identity,</if>
            <if test="entryDate != null">entry_date,</if>
            <if test="confirmDate != null">confirm_date,</if>
            <if test="reverseInvoiceCode != null">reverse_invoice_code,</if>
            <if test="reverseDate != null">reverse_date,</if>
            <if test="reverseTotalPrice != null">reverse_total_price,</if>
            <if test="reverseTotalTax != null">reverse_total_tax,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="redConfirmedUuid != null and redConfirmedUuid != ''">#{redConfirmedUuid},</if>
            <if test="applyReason != null">#{applyReason},</if>
            <if test="reverseState != null">#{reverseState},</if>
            <if test="buyerName != null">#{buyerName},</if>
            <if test="buyerTaxCode != null">#{buyerTaxCode},</if>
            <if test="redConfirmNo != null">#{redConfirmNo},</if>
            <if test="confirmState != null">#{confirmState},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="totalTax != null">#{totalTax},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="sellerName != null">#{sellerName},</if>
            <if test="sellerTaxCode != null">#{sellerTaxCode},</if>
            <if test="entryIdentity != null">#{entryIdentity},</if>
            <if test="entryDate != null">#{entryDate},</if>
            <if test="confirmDate != null">#{confirmDate},</if>
            <if test="reverseInvoiceCode != null">#{reverseInvoiceCode},</if>
            <if test="reverseDate != null">#{reverseDate},</if>
            <if test="reverseTotalPrice != null">#{reverseTotalPrice},</if>
            <if test="reverseTotalTax != null">#{reverseTotalTax},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSicvInvoiceRedConfirm">
        update sicv_invoice_red_confirm
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="invoiceId != null">invoice_id = #{invoiceId},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="redConfirmedUuid != null and redConfirmedUuid != ''">red_confirmed_uuid = #{redConfirmedUuid},
            </if>
            <if test="applyReason != null">apply_reason = #{applyReason},</if>
            <if test="reverseState != null">reverse_state = #{reverseState},</if>
            <if test="buyerName != null">buyer_name = #{buyerName},</if>
            <if test="buyerTaxCode != null">buyer_tax_code = #{buyerTaxCode},</if>
            <if test="redConfirmNo != null">red_confirm_no = #{redConfirmNo},</if>
            <if test="confirmState != null">confirm_state = #{confirmState},</if>
            <if test="invoiceCode != null">invoice_code = #{invoiceCode},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="totalTax != null">total_tax = #{totalTax},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="sellerName != null">seller_name = #{sellerName},</if>
            <if test="sellerTaxCode != null">seller_tax_code = #{sellerTaxCode},</if>
            <if test="entryIdentity != null">entry_identity = #{entryIdentity},</if>
            <if test="entryDate != null">entry_date = #{entryDate},</if>
            <if test="confirmDate != null">confirm_date = #{confirmDate},</if>
            <if test="reverseInvoiceCode != null">reverse_invoice_code = #{reverseInvoiceCode},</if>
            <if test="reverseDate != null">reverse_date = #{reverseDate},</if>
            <if test="reverseTotalPrice != null">reverse_total_price = #{reverseTotalPrice},</if>
            <if test="reverseTotalTax != null">reverse_total_tax = #{reverseTotalTax},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>
</mapper>