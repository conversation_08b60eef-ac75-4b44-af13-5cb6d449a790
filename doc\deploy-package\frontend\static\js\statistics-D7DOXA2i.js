import{i as O}from"./index-o0oVz-rl.js";import{_ as U,O as q,d as F,r as i,x as H,E as J,e as l,c as K,o as Q,f as t,h as e,i as s,l as o,a5 as W,t as b,a6 as Y,a7 as Z,a1 as tt,n as z,A as et,B as st}from"./index-Cox2ohSR.js";import{a as at,b as nt}from"./exceptionOrder-D6yIBntP.js";const r=m=>(et("data-v-a390e633"),m=m(),st(),m),ot={class:"app-container"},it={class:"statistics-content"},lt={class:"statistics-icon total"},ct={class:"statistics-info"},dt={class:"statistics-number"},rt=r(()=>s("div",{class:"statistics-label"},"异常订单总数",-1)),ut={class:"statistics-content"},_t={class:"statistics-icon pending"},pt={class:"statistics-info"},ft={class:"statistics-number"},ht=r(()=>s("div",{class:"statistics-label"},"待处理订单",-1)),vt={class:"statistics-content"},mt={class:"statistics-icon today"},gt={class:"statistics-info"},yt={class:"statistics-number"},bt=r(()=>s("div",{class:"statistics-label"},"今日新增",-1)),Ct={class:"statistics-content"},wt={class:"statistics-icon processed"},xt={class:"statistics-info"},St={class:"statistics-number"},kt=r(()=>s("div",{class:"statistics-label"},"已处理订单",-1)),Ot=r(()=>s("div",{class:"card-header"},[s("span",null,"异常类型分布")],-1)),zt=r(()=>s("div",{class:"card-header"},[s("span",null,"处理状态分布")],-1)),Dt={class:"card-header"},Et=r(()=>s("span",null,"异常订单趋势（最近30天）",-1)),Nt=r(()=>s("div",{class:"card-header"},[s("span",null,"场库异常统计")],-1)),Rt=q({name:"ExceptionOrderStatistics"}),Tt=Object.assign(Rt,{setup(m){const{proxy:Bt}=F(),f=i({}),D=i([]),C=i([]),E=i([]),w=i([]),h=i(30),N=i(),R=i(),T=i();let u=null,_=null,p=null;function L(){at().then(a=>{f.value=a.data,D.value=a.data.typeStats||[],C.value=a.data.statusStats||[],E.value=a.data.warehouseStats||[],V(),$()})}function B(){nt(h.value).then(a=>{w.value=a.data||[],M()})}function V(){u||(u=O(N.value));const a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"异常类型",type:"pie",radius:"50%",data:D.value.map(n=>({value:n.count,name:n.typeName})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};u.setOption(a)}function $(){_||(_=O(R.value));const a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"处理状态",type:"pie",radius:"50%",data:C.value.map(n=>({value:n.count,name:n.statusName})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};_.setOption(a)}function M(){p||(p=O(T.value));const a={tooltip:{trigger:"axis"},legend:{data:["异常订单数量"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:w.value.map(n=>n.date)},yAxis:{type:"value"},series:[{name:"异常订单数量",type:"line",stack:"Total",data:w.value.map(n=>n.count)}]};p.setOption(a)}function x(a){h.value=a,B()}function P(){const a=C.value.find(n=>n.status===2);return a?a.count:0}function I(a){const n=f.value.totalCount||1;return Math.round(a/n*100)}function A(){u&&u.resize(),_&&_.resize(),p&&p.resize()}return H(()=>{L(),B(),window.addEventListener("resize",A)}),J(()=>{window.removeEventListener("resize",A),u&&u.dispose(),_&&_.dispose(),p&&p.dispose()}),(a,n)=>{const g=l("el-icon"),c=l("el-card"),d=l("el-col"),y=l("el-row"),S=l("el-button"),X=l("el-button-group"),k=l("el-table-column"),j=l("el-progress"),G=l("el-table");return Q(),K("div",ot,[t(y,{gutter:20,class:"statistics-cards"},{default:e(()=>[t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",it,[s("div",lt,[t(g,null,{default:e(()=>[t(o(W))]),_:1})]),s("div",ct,[s("div",dt,b(o(f).totalCount||0),1),rt])])]),_:1})]),_:1}),t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",ut,[s("div",_t,[t(g,null,{default:e(()=>[t(o(Y))]),_:1})]),s("div",pt,[s("div",ft,b(o(f).pendingCount||0),1),ht])])]),_:1})]),_:1}),t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",vt,[s("div",mt,[t(g,null,{default:e(()=>[t(o(Z))]),_:1})]),s("div",gt,[s("div",yt,b(o(f).todayNewCount||0),1),bt])])]),_:1})]),_:1}),t(d,{span:6},{default:e(()=>[t(c,{class:"statistics-card"},{default:e(()=>[s("div",Ct,[s("div",wt,[t(g,null,{default:e(()=>[t(o(tt))]),_:1})]),s("div",xt,[s("div",St,b(P()),1),kt])])]),_:1})]),_:1})]),_:1}),t(y,{gutter:20,class:"chart-container"},{default:e(()=>[t(d,{span:12},{default:e(()=>[t(c,null,{header:e(()=>[Ot]),default:e(()=>[s("div",{ref_key:"typeChartRef",ref:N,style:{height:"300px"}},null,512)]),_:1})]),_:1}),t(d,{span:12},{default:e(()=>[t(c,null,{header:e(()=>[zt]),default:e(()=>[s("div",{ref_key:"statusChartRef",ref:R,style:{height:"300px"}},null,512)]),_:1})]),_:1})]),_:1}),t(y,{gutter:20,class:"chart-container"},{default:e(()=>[t(d,{span:24},{default:e(()=>[t(c,null,{header:e(()=>[s("div",Dt,[Et,t(X,null,{default:e(()=>[t(S,{size:"small",onClick:n[0]||(n[0]=v=>x(7)),type:o(h)===7?"primary":""},{default:e(()=>[z("7天")]),_:1},8,["type"]),t(S,{size:"small",onClick:n[1]||(n[1]=v=>x(15)),type:o(h)===15?"primary":""},{default:e(()=>[z("15天")]),_:1},8,["type"]),t(S,{size:"small",onClick:n[2]||(n[2]=v=>x(30)),type:o(h)===30?"primary":""},{default:e(()=>[z("30天")]),_:1},8,["type"])]),_:1})])]),default:e(()=>[s("div",{ref_key:"trendChartRef",ref:T,style:{height:"400px"}},null,512)]),_:1})]),_:1})]),_:1}),t(y,{class:"chart-container"},{default:e(()=>[t(d,{span:24},{default:e(()=>[t(c,null,{header:e(()=>[Nt]),default:e(()=>[t(G,{data:o(E),style:{width:"100%"}},{default:e(()=>[t(k,{prop:"warehouseName",label:"场库名称"}),t(k,{prop:"count",label:"异常订单数量",sortable:""}),t(k,{label:"占比"},{default:e(v=>[t(j,{percentage:I(v.row.count),"stroke-width":8,"show-text":!0,format:()=>I(v.row.count)+"%"},null,8,["percentage","format"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])}}}),Vt=U(Tt,[["__scopeId","data-v-a390e633"]]);export{Vt as default};
