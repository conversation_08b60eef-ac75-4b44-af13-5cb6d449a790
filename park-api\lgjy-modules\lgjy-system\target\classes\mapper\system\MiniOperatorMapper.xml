<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniOperatorMapper">

    <resultMap type="com.lgjy.system.domain.MiniOperator" id="MiniOperatorResult">
        <result property="id" column="id" />
        <result property="companyName" column="company_name" />
        <result property="primaryContactName" column="primary_contact_name" />
        <result property="primaryContactPhone" column="primary_contact_phone" />
        <result property="remark" column="remark" />
        <result property="userId" column="user_id" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="createdBy" column="created_by" />
        <result property="updatedBy" column="updated_by" />
    </resultMap>

    <sql id="selectMiniOperatorVo">
        select o.id, o.company_name, o.primary_contact_name, o.primary_contact_phone, o.remark, o.user_id, o.delete_flag,
               o.create_by, o.create_time, o.update_by, o.update_time,
               coalesce(cu.nick_name, cu.user_name, o.create_by) as created_by,
               coalesce(uu.nick_name, uu.user_name, o.update_by) as updated_by
        from mini_operator o
        left join sys_user cu on o.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on o.update_by = uu.user_id and uu.delete_flag = '0'
    </sql>

    <select id="selectMiniOperatorList" parameterType="com.lgjy.system.domain.MiniOperator" resultMap="MiniOperatorResult">
        <include refid="selectMiniOperatorVo"/>
        <where>
            o.delete_flag = 0
            <if test="companyName != null and companyName != ''"> and o.company_name like concat('%', #{companyName}, '%')</if>
            <if test="primaryContactName != null and primaryContactName != ''"> and o.primary_contact_name like concat('%', #{primaryContactName}, '%')</if>
            <if test="primaryContactPhone != null and primaryContactPhone != ''"> and o.primary_contact_phone like concat('%', #{primaryContactPhone}, '%')</if>
            <if test="userId != null"> and o.user_id = #{userId}</if>
        </where>
        order by o.create_time desc
    </select>

    <select id="selectMiniOperatorById" parameterType="Long" resultMap="MiniOperatorResult">
        <include refid="selectMiniOperatorVo"/>
        where o.id = #{id} and o.delete_flag = 0
    </select>

    <select id="selectMiniOperatorAll" resultMap="MiniOperatorResult">
        <include refid="selectMiniOperatorVo"/>
        where o.delete_flag = 0
        order by o.company_name
    </select>

    <select id="checkCompanyNameUnique" parameterType="String" resultMap="MiniOperatorResult">
        <include refid="selectMiniOperatorVo"/>
        where o.company_name = #{companyName} and o.delete_flag = 0 limit 1
    </select>

    <insert id="insertMiniOperator" parameterType="com.lgjy.system.domain.MiniOperator">
        insert into mini_operator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="primaryContactName != null and primaryContactName != ''">primary_contact_name,</if>
            <if test="primaryContactPhone != null and primaryContactPhone != ''">primary_contact_phone,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="primaryContactName != null and primaryContactName != ''">#{primaryContactName},</if>
            <if test="primaryContactPhone != null and primaryContactPhone != ''">#{primaryContactPhone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            #{createBy},
            now(),
            #{updateBy},
            now()
        </trim>
    </insert>

    <update id="updateMiniOperator" parameterType="com.lgjy.system.domain.MiniOperator">
        update mini_operator
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="primaryContactName != null and primaryContactName != ''">primary_contact_name = #{primaryContactName},</if>
            <if test="primaryContactPhone != null and primaryContactPhone != ''">primary_contact_phone = #{primaryContactPhone},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            update_by = #{updateBy},
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniOperatorById" parameterType="Long">
        update mini_operator set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniOperatorByIds" parameterType="String">
        update mini_operator set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
