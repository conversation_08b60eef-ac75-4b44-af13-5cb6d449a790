import{a as A,u as O}from"./user-B7ln4gAm.js";import{C as y}from"./index-Cy2yB1cz.js";import{O as E,u as K,d as F,r as u,D as Q,e as s,Q as q,c as G,o as C,i as m,f as t,R as H,h as a,l,j as J,t as S,P as V,n as $}from"./index-Cox2ohSR.js";const L={class:"app-container"},M=m("h4",{class:"form-header h4"},"基本信息",-1),W=m("h4",{class:"form-header h4"},"角色信息",-1),X={style:{"text-align":"center","margin-left":"-120px","margin-top":"30px"}},Y=E({name:"AuthRole"}),le=Object.assign(Y,{components:{CustomPagination:y}},{setup(Z){const b=K(),{proxy:f}=F(),g=u(!0),v=u(0),i=u(1),c=u(10),h=u([]),_=u([]),r=u({nickName:void 0,userName:void 0,userId:void 0});function T(o){k(o)&&f.$refs.roleRef.toggleRowSelection(o)}function j(o){h.value=o.map(e=>e.roleId)}function z(o){return o.roleId}function k(o){return o.status==="0"}function w(){const o={path:"/system/user"};f.$tab.closeOpenPage(o)}function B(){const o=r.value.userId,e=h.value.join(",");O({userId:o,roleIds:e}).then(d=>{f.$modal.msgSuccess("授权成功"),w()})}return(()=>{const o=b.params&&b.params.userId;o&&(g.value=!0,A(o).then(e=>{r.value=e.user,_.value=e.roles,v.value=_.value.length,Q(()=>{_.value.forEach(d=>{d.flag&&f.$refs.roleRef.toggleRowSelection(d)})}),g.value=!1}))})(),(o,e)=>{const d=s("el-input"),R=s("el-form-item"),I=s("el-col"),D=s("el-row"),N=s("el-form"),p=s("el-table-column"),P=s("el-table"),x=s("el-button"),U=q("loading");return C(),G("div",L,[M,t(N,{model:l(r),"label-width":"80px"},{default:a(()=>[t(D,null,{default:a(()=>[t(I,{span:8,offset:2},{default:a(()=>[t(R,{label:"用户昵称",prop:"nickName"},{default:a(()=>[t(d,{modelValue:l(r).nickName,"onUpdate:modelValue":e[0]||(e[0]=n=>l(r).nickName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(I,{span:8,offset:2},{default:a(()=>[t(R,{label:"登录账号",prop:"userName"},{default:a(()=>[t(d,{modelValue:l(r).userName,"onUpdate:modelValue":e[1]||(e[1]=n=>l(r).userName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),W,H((C(),J(P,{"row-key":z,onRowClick:T,ref:"roleRef",onSelectionChange:j,data:l(_).slice((l(i)-1)*l(c),l(i)*l(c))},{default:a(()=>[t(p,{label:"序号",width:"55",type:"index",align:"center"},{default:a(n=>[m("span",null,S((l(i)-1)*l(c)+n.$index+1),1)]),_:1}),t(p,{type:"selection","reserve-selection":!0,selectable:k,width:"55"}),t(p,{label:"角色编号",align:"center",prop:"roleId"}),t(p,{label:"角色名称",align:"center",prop:"roleName"}),t(p,{label:"权限字符",align:"center",prop:"roleKey"}),t(p,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(n=>[m("span",null,S(o.parseTime(n.row.createTime)),1)]),_:1})]),_:1},8,["data"])),[[U,l(g)]]),t(y,{total:l(v),"current-page":l(i),"onUpdate:currentPage":e[2]||(e[2]=n=>V(i)?i.value=n:null),"page-size":l(c),"onUpdate:pageSize":e[3]||(e[3]=n=>V(c)?c.value=n:null)},null,8,["total","current-page","page-size"]),t(N,{"label-width":"100px"},{default:a(()=>[m("div",X,[t(x,{type:"primary",onClick:e[4]||(e[4]=n=>B())},{default:a(()=>[$("提交")]),_:1}),t(x,{onClick:e[5]||(e[5]=n=>w())},{default:a(()=>[$("返回")]),_:1})])]),_:1})])}}});export{le as default};
