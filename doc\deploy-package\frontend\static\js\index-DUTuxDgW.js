import{_ as ve,O as be,d as we,r as c,C as ye,T as Ce,x as ke,e as r,Q as A,c as T,o as g,R as w,f as e,S as Ne,h as t,m as xe,l as a,L as De,M as Ve,j as y,n as u,t as d,i as f,A as Se,B as Te}from"./index-Cox2ohSR.js";import{g as Ie}from"./member-B3y_SpmJ.js";import{l as Le,d as Pe}from"./exceptionOrder-D6yIBntP.js";import{C as G}from"./index-Cy2yB1cz.js";const Ue=N=>(Se("data-v-b8db37f8"),N=N(),Te(),N),qe={class:"app-container"},ze={class:"money-text"},Oe={class:"error-detail-view"},Re={style:{color:"#f56c6c","font-weight":"bold"}},Be={class:"image-section"},Ee=Ue(()=>f("h3",{style:{"margin-bottom":"15px",color:"#303133"}},"相关图片",-1)),Me={class:"image-container"},$e={key:0,class:"image-item"},Qe={key:1,class:"no-image"},Ye={class:"dialog-footer"},je=be({name:"ErrorDataLog"}),We=Object.assign(je,{components:{CustomPagination:G}},{setup(N){const{proxy:C}=we(),{error_data_code:I}=C.useDict("error_data_code"),z=c([]),x=c(!1),L=c(!0),P=c(!0),O=c([]),H=c(!0),R=c(!0),B=c(0),J=c(""),h=c([]),X=c([]),E=c([]),M=c(!1),Z=ye({form:{},queryParams:{pageNum:1,pageSize:10,plateNum:null,parkingId:null,errCode:null,beginTime:null,endTime:null},rules:{plateNum:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],parkingId:[{required:!0,message:"场库ID不能为空",trigger:"change"}],errCode:[{required:!0,message:"错误类型不能为空",trigger:"change"}]}}),{queryParams:i,form:s,rules:Fe}=Ce(Z);function k(){L.value=!0,i.value.params={},h.value!=null&&h.value!=""&&(i.value.params.beginTime=h.value[0],i.value.params.endTime=h.value[1]),Le(i.value).then(o=>{z.value=o.rows,B.value=o.total,L.value=!1})}function U(){i.value.pageNum=1,k()}function ee(){h.value=[],C.resetForm("queryRef"),Object.assign(i.value,{pageNum:1,pageSize:10,plateNum:null,parkingId:null,errCode:null,beginTime:null,endTime:null}),U()}function te(o){O.value=o.map(n=>n.id),H.value=o.length!=1,R.value=!o.length}function le(o){$(),M.value=!0,s.value={...o},x.value=!0}function ae(){C.download("system/errorDataLog/export",{...i.value},`错误数据日志_${new Date().getTime()}.xlsx`)}function $(){s.value={},M.value=!0}function oe(){x.value=!1,$()}function Q(o){const n=o.id||O.value;C.$modal.confirm('是否确认删除记录ID为"'+n+'"的数据项？').then(function(){return Pe(n)}).then(()=>{k(),C.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ne(){Ie().then(o=>{X.value=o.data||[],E.value=re(o.data)})}function re(o){if(!o||o.length===0)return[];const n=o.filter(p=>p.parentId==="0"),q=o.filter(p=>p.parentId!=="0");return n.map(p=>{const D=q.filter(b=>b.parentId===p.id).map(b=>({value:b.id,label:b.warehouseName,isLeaf:!0}));return{value:p.id,label:p.warehouseName,children:D.length>0?D:void 0}})}function Y(o){return o?o.length===8?"success":"primary":"info"}function j(o){return o?o.length===8?"#d4edda":"#cce7ff":"#909399"}return ke(()=>{k(),ne()}),(o,n)=>{const q=r("el-input"),p=r("el-form-item"),D=r("el-cascader"),b=r("el-option"),ie=r("el-select"),se=r("el-date-picker"),v=r("el-button"),ue=r("el-form"),V=r("el-col"),de=r("right-toolbar"),W=r("el-row"),m=r("el-table-column"),F=r("el-tag"),K=r("dict-tag"),pe=r("el-table"),_=r("el-descriptions-item"),ce=r("el-descriptions"),me=r("el-image"),_e=r("el-empty"),ge=r("el-dialog"),S=A("hasPermi"),fe=A("loading");return g(),T("div",qe,[w(e(ue,{model:a(i),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[e(p,{label:"车牌号",prop:"plateNum"},{default:t(()=>[e(q,{modelValue:a(i).plateNum,"onUpdate:modelValue":n[0]||(n[0]=l=>a(i).plateNum=l),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:xe(U,["enter"])},null,8,["modelValue"])]),_:1}),e(p,{label:"场库/停车场",prop:"parkingId"},{default:t(()=>[e(D,{modelValue:a(i).parkingId,"onUpdate:modelValue":n[1]||(n[1]=l=>a(i).parkingId=l),options:E.value,props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),e(p,{label:"错误类型",prop:"errCode"},{default:t(()=>[e(ie,{modelValue:a(i).errCode,"onUpdate:modelValue":n[2]||(n[2]=l=>a(i).errCode=l),placeholder:"请选择错误类型",clearable:"",style:{width:"200px"}},{default:t(()=>[(g(!0),T(De,null,Ve(a(I),l=>(g(),y(b,{key:l.value,label:l.label,value:parseInt(l.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"更新时间"},{default:t(()=>[e(se,{modelValue:h.value,"onUpdate:modelValue":n[3]||(n[3]=l=>h.value=l),style:{width:"240px"},"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(p,null,{default:t(()=>[e(v,{type:"primary",icon:"Search",onClick:U},{default:t(()=>[u("搜索")]),_:1}),e(v,{icon:"Refresh",onClick:ee},{default:t(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ne,P.value]]),e(W,{gutter:10,class:"mb8"},{default:t(()=>[e(V,{span:1.5},{default:t(()=>[w((g(),y(v,{type:"danger",plain:"",icon:"Delete",disabled:R.value,onClick:Q},{default:t(()=>[u("删除")]),_:1},8,["disabled"])),[[S,["system:errorDataLog:remove"]]])]),_:1}),e(V,{span:1.5},{default:t(()=>[w((g(),y(v,{type:"warning",plain:"",icon:"Download",onClick:ae},{default:t(()=>[u("导出")]),_:1})),[[S,["system:errorDataLog:export"]]])]),_:1}),e(de,{showSearch:P.value,"onUpdate:showSearch":n[4]||(n[4]=l=>P.value=l),onQueryTable:k},null,8,["showSearch"])]),_:1}),w((g(),y(pe,{data:z.value,onSelectionChange:te},{default:t(()=>[e(m,{type:"selection",width:"55",align:"center"}),e(m,{label:"记录ID",align:"center",prop:"id",width:"180","show-overflow-tooltip":""}),e(m,{label:"车牌号",align:"center",prop:"plateNum",width:"120"},{default:t(l=>[e(F,{type:Y(l.row.plateNum),color:j(l.row.plateNum),effect:"plain"},{default:t(()=>[u(d(l.row.plateNum),1)]),_:2},1032,["type","color"])]),_:1}),e(m,{label:"场库名称",align:"center",prop:"parkingName",width:"150"}),e(m,{label:"错误类型",align:"center",prop:"errCode",width:"120"},{default:t(l=>[e(K,{options:a(I),value:l.row.errCode},null,8,["options","value"])]),_:1}),e(m,{label:"入场时间",align:"center",prop:"inTime",width:"150"}),e(m,{label:"出场时间",align:"center",prop:"outTime",width:"150"},{default:t(l=>[u(d(l.row.outTime||"未出场"),1)]),_:1}),e(m,{label:"金额",align:"center",prop:"money",width:"100"},{default:t(l=>[f("span",ze,"¥"+d(l.row.money||"0.00"),1)]),_:1}),e(m,{label:"备注",align:"center",prop:"remark",width:"200","show-overflow-tooltip":""}),e(m,{label:"最后更新时间",align:"center",prop:"lastUpdate",width:"160"},{default:t(l=>[f("span",null,d(o.parseTime(l.row.lastUpdate,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(m,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"120"},{default:t(l=>[w((g(),y(v,{link:"",type:"primary",icon:"View",onClick:he=>le(l.row)},{default:t(()=>[u("查看")]),_:2},1032,["onClick"])),[[S,["system:errorDataLog:query"]]]),w((g(),y(v,{link:"",type:"primary",icon:"Delete",onClick:he=>Q(l.row)},{default:t(()=>[u("删除")]),_:2},1032,["onClick"])),[[S,["system:errorDataLog:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,L.value]]),e(G,{total:B.value,"current-page":a(i).pageNum,"onUpdate:currentPage":n[5]||(n[5]=l=>a(i).pageNum=l),"page-size":a(i).pageSize,"onUpdate:pageSize":n[6]||(n[6]=l=>a(i).pageSize=l),onPagination:k},null,8,["total","current-page","page-size"]),e(ge,{title:J.value,modelValue:x.value,"onUpdate:modelValue":n[7]||(n[7]=l=>x.value=l),width:"1000px","append-to-body":""},{footer:t(()=>[f("div",Ye,[e(v,{onClick:oe},{default:t(()=>[u("关 闭")]),_:1})])]),default:t(()=>[f("div",Oe,[e(W,{gutter:20},{default:t(()=>[e(V,{span:14},{default:t(()=>[e(ce,{title:"错误数据日志详情",column:2,border:""},{default:t(()=>[e(_,{label:"记录ID"},{default:t(()=>[u(d(a(s).id),1)]),_:1}),e(_,{label:"车牌号"},{default:t(()=>[e(F,{type:Y(a(s).plateNum),color:j(a(s).plateNum),effect:"plain"},{default:t(()=>[u(d(a(s).plateNum),1)]),_:1},8,["type","color"])]),_:1}),e(_,{label:"错误类型"},{default:t(()=>[e(K,{options:a(I),value:a(s).errCode},null,8,["options","value"])]),_:1}),e(_,{label:"场库名称"},{default:t(()=>[u(d(a(s).parkingName),1)]),_:1}),e(_,{label:"入场时间"},{default:t(()=>[u(d(a(s).inTime||"无"),1)]),_:1}),e(_,{label:"入场通道"},{default:t(()=>[u(d(a(s).inChannelName||"无"),1)]),_:1}),e(_,{label:"出场时间"},{default:t(()=>[u(d(a(s).outTime||"未出场"),1)]),_:1}),e(_,{label:"出场通道"},{default:t(()=>[u(d(a(s).outChannelName||"无"),1)]),_:1}),e(_,{label:"金额"},{default:t(()=>[f("span",Re," ¥"+d(a(s).money||"0.00"),1)]),_:1}),e(_,{label:"最后更新时间"},{default:t(()=>[u(d(o.parseTime(a(s).lastUpdate,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(_,{label:"备注",span:2},{default:t(()=>[u(d(a(s).remark||"无"),1)]),_:1})]),_:1})]),_:1}),e(V,{span:10},{default:t(()=>[f("div",Be,[Ee,f("div",Me,[a(s).imgPath?(g(),T("div",$e,[e(me,{src:a(s).imgPath,fit:"cover",style:{width:"100%",height:"300px","border-radius":"4px"},"preview-src-list":[a(s).imgPath],"preview-teleported":""},null,8,["src","preview-src-list"])])):(g(),T("div",Qe,[e(_e,{description:"暂无图片"})]))])])]),_:1})]),_:1})])]),_:1},8,["title","modelValue"])])}}}),Je=ve(We,[["__scopeId","data-v-b8db37f8"]]);export{Je as default};
