# System Architecture & Design Principles
- User prefers dynamic parking lot configuration over hardcoded parking lot IDs in annotations to avoid source code changes when parking lots are updated.
- User prefers batch loading/caching patterns to avoid repeated database queries for parking lot information, wants to load multiple parking lot IDs into managed bean objects at once rather than querying database on each request.
- User prefers using dictionary-based configuration for managing enums, operations, and paths in the gate system instead of hardcoding them, allowing dynamic modification through dictionary updates rather than code changes.
- User prefers gate system upgrade/decoupling approach with multi-brand support and modular architecture for the parking management system.
- User prefers minimal logging approach - only include essential logs needed to identify problems, avoid excessive debug output.
- User prefers to use lgjy-auth ASCII art design as template for standardizing log prefixes across all backend services in the parking management system.
- Gate system dictionary-driven refactoring: use digital enum values (operation types 1-8, brand IDs 1-7) instead of strings, with memory pre-loading mechanism, achieving 130x performance improvement, zero-code configuration updates, and a fully dictionary-driven architecture.
- 道闸系统改造核心技术：NumericDictMemoryPreloader内存预加载器，数字化策略接口INumericGateStrategy，API路径字典化管理(品牌ID_操作ID格式)，响应时间从5-15ms降至0.01ms。
- 道闸系统改造方案文档已创建：《道闸系统字典驱动改造方案.md》包含完整的数字化架构设计、性能测试数据、5阶段实施计划和运维管理方案。
- User prefers systematic 5-step approach for gate system dictionary-driven refactoring: document analysis, codebase status analysis, gap analysis, detailed refactoring plan creation, and structured plan presentation with priority phases, specific tasks, file modifications, risk assessment, and testing strategies.
- Only create script files when the user explicitly requests it, and always ask the user for confirmation before creating a script.
- In Windows systems, only create PowerShell files (.ps1) as script files, do not create .bat files.

# Database Structure
- User's database name is 'parknew' for the parking management system with separate 'nacos' database for service configurations.
- User prefers unified warehouse logic with parent-child warehouse relationships instead of separate warehouse and parking lot concepts, referencing existing system department logic patterns.
- In mini_warehouse table, parent_id=0 indicates warehouse (场库) information, parent_id≠0 indicates parking lot (停车场) information.
- User prefers consistent field naming in database tables - wants to change complex_id to warehouseId for standardization.
- User prefers delete_flag field name instead of del_flag in database tables to avoid backend-database logic inconsistencies.
- User prefers to modify database tables to add missing fields rather than removing them from code.
- User prefers to keep deprecated fields in database tables for compatibility with old data but remove them from new system code.
- wx_user table user_type field values: 0=普通用户, 1=集团客户, 2=VIP客户, with default value 0.
- User prefers to modify actual database table data/fields rather than implementing simulated code logic - data can be simulated but code logic should be real.

# UI/UX Preferences
- User prefers modern, professional UI design with high-end feel, emphasizing visual hierarchy, improved color schemes, typography, interactive experience, and responsive design.
- User prefers deep blue color (#003366) for the left navigation bar.
- User prefers vertical carousel layout over horizontal layout for admin panel parking lot image display.
- User prefers carousel preview components to have 16:9 aspect ratio and adequate spacing between upload components and carousel preview areas.
- User prefers to set the width of input boxes and selection boxes in query conditions to 200px for interface consistency.
- User prefers view dialogs to display information without input fields, using pure display format with improved content and element layout styling.
- User prefers horizontal (left-right) layout over vertical (up-down) layout for field display in tables for better aesthetics.
- User prefers status field text to be centered across all pages for consistent UI styling.
- User prefers admin homepage layout with time filter buttons in top-left corner, data totals in cards above, large fold line charts below, and consistent styling without clutter.
- User prefers chart data to show whole numbers (not decimals like 0.2 people), Y-axis with switchable scales (20/50/100 people buttons), and X-axis showing specific dates based on selected time period (e.g., 7 days before 7.16 when selecting 7-day period).
- User prefers chart Y-axis with switchable scale buttons (20/50/100 people), no decimal numbers for user counts, X-axis showing specific dates based on selected time period, and requires real database queries instead of mock data for all chart interfaces.
- User prefers time controls on the right side of dashboard, X-axis should show time units with year/month context (e.g., 2025年 for year view, 7月 for month view), remove redundant time displays, and show months instead of full dates for year period since year is already indicated.

# Dictionary & Configuration Management
- User prefers all hardcoded options to be managed using dictionaries, EXCEPT for vehicle type options which should be dynamically loaded from the database.
- User prefers comprehensive dictionary-based refactoring for order management module covering both parking order and exception order management pages.
- User prefers to prioritize frontend/backend code modifications to use dictionary-based implementations instead of hardcoded approaches.
- For Excel export dictionary conversion: use @Excel annotation with readConverterExp attribute to display text labels instead of numeric values.

# Menu & Module Organization
- User prefers order management to be organized with main 'Order Management' menu containing three sub-menus: 'Parking Order Management', 'Exception Order Management', and 'Vehicle Entry/Exit Record Management'.
- User wants to add a new merchant management menu with sub-items: merchant information, merchant packages, merchant members, merchant voucher purchases, and QR code voucher purchases.
- User prefers whitelist management to be at same menu level as mini-program users.
- User prefers car owner management (车主管理) module with mini-program users submenu displaying only: user account, nickname, phone, avatar, disabled status, user type fields.

# Member & Package Management
- User prefers member management modules to use mini_ prefix for database tables and follow RuoYi framework development standards with Vue3 + Element Plus frontend stack.
- User prefers cascading selection UI pattern for parking management: show operator first, then warehouse, then conditionally show parking lot selection.
- User prefers solutions that maintain cascading selector display functionality (回显) over simple frontend data type conversion approaches, wants solutions that are both functional and easy to modify.
- User prefers VIP member management to use package names instead of VIP types, requiring changes to database fields, queries, forms, and display logic.
- User prefers special member management to allow VIP and group customer display without using dictionaries, as an exception to the general dictionary-based approach.
- User prefers member package management to display warehouse/sub-warehouse hierarchy and use cascading dropdown selection.
- User prefers warehouse selection components to follow the same UI pattern as the member package management warehouse selection (cascading dropdown with operator->warehouse->parking lot hierarchy).

# Vehicle Entry/Exit Record Management
- User prefers vehicle entry/exit record management module to be placed under Order Management menu at same level as parking and exception order management, using gate_parking_info table with RuoYi framework standards and consistent UI styling with existing order management modules.

# Codebase Consistency
- User prefers to check existing field implementations in the codebase before making changes to ensure consistency with established patterns.

# Admin Homepage
- User prefers admin homepage dynamic data implementation with real-time statistics (today/monthly/total), database-driven data display, caching for performance, RuoYi framework standards, and comprehensive testing including data accuracy and loading performance validation.
- User prefers dashboard optimization solutions without performance monitoring and testing components - wants simpler implementation focused on SQL and caching optimization only.