#!/bin/bash

# 停车管理系统备份脚本
# 作者: DevOps Team
# 版本: 1.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 解析命令行参数
BACKUP_ENV="test"
BACKUP_TYPE="full"
BACKUP_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            BACKUP_ENV="${1#*=}"
            shift
            ;;
        --type=*)
            BACKUP_TYPE="${1#*=}"
            shift
            ;;
        --name=*)
            BACKUP_NAME="${1#*=}"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV            备份环境 (test|prod) 默认: test"
            echo "  --type=TYPE          备份类型 (full|config|data) 默认: full"
            echo "  --name=NAME          备份名称 (可选)"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 生成备份名称
generate_backup_name() {
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local git_commit=""
    
    if [ -d "$PROJECT_ROOT/.git" ]; then
        cd "$PROJECT_ROOT"
        git_commit="-$(git rev-parse --short HEAD)"
    fi
    
    if [ -n "$BACKUP_NAME" ]; then
        echo "${BACKUP_NAME}-${timestamp}${git_commit}"
    else
        echo "backup-${BACKUP_ENV}-${timestamp}${git_commit}"
    fi
}

# 创建备份目录
create_backup_directory() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log "INFO" "创建备份目录: $backup_path"
    
    mkdir -p "$backup_path"
    
    # 创建子目录
    mkdir -p "$backup_path/config"
    mkdir -p "$backup_path/data"
    mkdir -p "$backup_path/logs"
    mkdir -p "$backup_path/docker"
    
    echo "$backup_path"
}

# 备份配置文件
backup_config() {
    local backup_path="$1"
    
    log "INFO" "备份配置文件..."
    
    # 备份Docker Compose配置
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        cp "$DOCKER_COMPOSE_FILE" "$backup_path/config/"
        log "INFO" "已备份Docker Compose配置"
    fi
    
    # 备份Nginx配置
    local nginx_config_dir="$DEPLOY_DIR/nginx"
    if [ -d "$nginx_config_dir" ]; then
        cp -r "$nginx_config_dir" "$backup_path/config/"
        log "INFO" "已备份Nginx配置"
    fi
    
    # 备份SSL证书
    local ssl_dir="/opt/park/ssl"
    if [ -d "$ssl_dir" ]; then
        cp -r "$ssl_dir" "$backup_path/config/"
        log "INFO" "已备份SSL证书"
    fi
    
    # 备份环境配置
    if [ -f "$SCRIPTS_ROOT/config/environment.conf" ]; then
        cp "$SCRIPTS_ROOT/config/environment.conf" "$backup_path/config/"
        log "INFO" "已备份环境配置"
    fi
    
    # 备份服务配置
    if [ -f "$SCRIPTS_ROOT/config/services.conf" ]; then
        cp "$SCRIPTS_ROOT/config/services.conf" "$backup_path/config/"
        log "INFO" "已备份服务配置"
    fi
}

# 备份数据库
backup_database() {
    local backup_path="$1"
    
    log "INFO" "备份数据库..."
    
    # 检查MySQL容器是否运行
    if ! docker ps --format "{{.Names}}" | grep -q "^park-mysql$"; then
        log "WARN" "MySQL容器未运行，跳过数据库备份"
        return 0
    fi
    
    # 备份数据库
    local db_backup_file="$backup_path/data/database-backup.sql"
    
    if docker exec park-mysql mysqldump \
        -u "$TEST_MYSQL_USER" \
        -p"$TEST_MYSQL_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --all-databases > "$db_backup_file"; then
        
        log "INFO" "数据库备份完成: $db_backup_file"
        
        # 压缩数据库备份
        gzip "$db_backup_file"
        log "INFO" "数据库备份已压缩: ${db_backup_file}.gz"
    else
        log "ERROR" "数据库备份失败"
        return 1
    fi
}

# 备份Redis数据
backup_redis() {
    local backup_path="$1"
    
    log "INFO" "备份Redis数据..."
    
    # 检查Redis容器是否运行
    if ! docker ps --format "{{.Names}}" | grep -q "^park-redis$"; then
        log "WARN" "Redis容器未运行，跳过Redis备份"
        return 0
    fi
    
    # 触发Redis保存
    docker exec park-redis redis-cli -a "$TEST_REDIS_PASSWORD" BGSAVE
    
    # 等待保存完成
    sleep 5
    
    # 复制Redis数据文件
    local redis_backup_dir="$backup_path/data/redis"
    mkdir -p "$redis_backup_dir"
    
    if docker cp park-redis:/data/dump.rdb "$redis_backup_dir/"; then
        log "INFO" "Redis数据备份完成: $redis_backup_dir/dump.rdb"
    else
        log "WARN" "Redis数据备份失败"
    fi
}

# 备份Nacos配置
backup_nacos() {
    local backup_path="$1"
    
    log "INFO" "备份Nacos配置..."
    
    # 检查Nacos是否可访问
    if ! curl -s "http://localhost:8848/nacos/v1/ns/operator/metrics" > /dev/null; then
        log "WARN" "Nacos服务不可访问，跳过Nacos备份"
        return 0
    fi
    
    # 导出Nacos配置
    local nacos_backup_file="$backup_path/data/nacos-config.zip"
    
    if curl -s "http://localhost:8848/nacos/v1/cs/configs?export=true&group=DEFAULT_GROUP" \
        -o "$nacos_backup_file"; then
        log "INFO" "Nacos配置备份完成: $nacos_backup_file"
    else
        log "WARN" "Nacos配置备份失败"
    fi
}

# 备份应用日志
backup_logs() {
    local backup_path="$1"
    
    log "INFO" "备份应用日志..."
    
    # 备份系统日志
    if [ -d "$LOGS_DIR" ]; then
        cp -r "$LOGS_DIR"/* "$backup_path/logs/" 2>/dev/null || true
        log "INFO" "已备份系统日志"
    fi
    
    # 备份Docker容器日志
    local containers=("park-gateway" "park-auth" "park-system" "park-file" "park-wx" "park-gate" "park-wx-auth")
    
    for container in "${containers[@]}"; do
        if docker ps --format "{{.Names}}" | grep -q "^$container$"; then
            docker logs "$container" > "$backup_path/logs/${container}.log" 2>&1 || true
            log "DEBUG" "已备份容器日志: $container"
        fi
    done
    
    log "INFO" "应用日志备份完成"
}

# 备份Docker镜像信息
backup_docker_info() {
    local backup_path="$1"
    
    log "INFO" "备份Docker信息..."
    
    # 保存镜像列表
    docker images --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}\t{{.Size}}\t{{.CreatedAt}}" \
        > "$backup_path/docker/images.txt"
    
    # 保存容器状态
    docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" \
        > "$backup_path/docker/containers.txt"
    
    # 保存网络信息
    docker network ls > "$backup_path/docker/networks.txt"
    
    # 保存卷信息
    docker volume ls > "$backup_path/docker/volumes.txt"
    
    log "INFO" "Docker信息备份完成"
}

# 创建备份清单
create_backup_manifest() {
    local backup_path="$1"
    local backup_name="$2"
    
    log "INFO" "创建备份清单..."
    
    local manifest_file="$backup_path/backup-manifest.json"
    
    cat > "$manifest_file" << EOF
{
    "backup_name": "$backup_name",
    "backup_type": "$BACKUP_TYPE",
    "environment": "$BACKUP_ENV",
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "project_version": "$PROJECT_VERSION",
    "git_branch": "$GIT_BRANCH_CURRENT",
    "git_commit": "$GIT_COMMIT",
    "backup_size": "$(du -sh "$backup_path" | cut -f1)",
    "files": {
        "config": $(find "$backup_path/config" -type f 2>/dev/null | wc -l),
        "data": $(find "$backup_path/data" -type f 2>/dev/null | wc -l),
        "logs": $(find "$backup_path/logs" -type f 2>/dev/null | wc -l),
        "docker": $(find "$backup_path/docker" -type f 2>/dev/null | wc -l)
    }
}
EOF

    log "INFO" "备份清单已创建: $manifest_file"
}

# 清理旧备份
cleanup_old_backups() {
    log "INFO" "清理旧备份..."
    
    # 保留最近7天的备份
    find "$BACKUP_DIR" -type d -name "backup-*" -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    
    log "INFO" "旧备份清理完成"
}

# 主备份函数
main() {
    log "INFO" "开始备份..."
    log "INFO" "备份环境: $BACKUP_ENV"
    log "INFO" "备份类型: $BACKUP_TYPE"
    
    # 生成备份名称
    local backup_name=$(generate_backup_name)
    log "INFO" "备份名称: $backup_name"
    
    # 创建备份目录
    local backup_path=$(create_backup_directory "$backup_name")
    
    # 根据备份类型执行不同的备份操作
    case "$BACKUP_TYPE" in
        "full")
            backup_config "$backup_path"
            backup_database "$backup_path"
            backup_redis "$backup_path"
            backup_nacos "$backup_path"
            backup_logs "$backup_path"
            backup_docker_info "$backup_path"
            ;;
        "config")
            backup_config "$backup_path"
            backup_nacos "$backup_path"
            ;;
        "data")
            backup_database "$backup_path"
            backup_redis "$backup_path"
            ;;
        *)
            log "ERROR" "不支持的备份类型: $BACKUP_TYPE"
            return 1
            ;;
    esac
    
    # 创建备份清单
    create_backup_manifest "$backup_path" "$backup_name"
    
    # 清理旧备份
    cleanup_old_backups
    
    log "INFO" "备份完成: $backup_path"
    log "INFO" "备份大小: $(du -sh "$backup_path" | cut -f1)"
    
    # 输出备份路径供其他脚本使用
    echo "$backup_path"
    
    return 0
}

# 执行主函数
main "$@"
