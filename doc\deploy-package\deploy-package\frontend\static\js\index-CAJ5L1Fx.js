import{v as V,O as _e,d as ve,r as c,C as we,T as ye,x as ke,e as p,Q as H,c as L,o as g,R as _,f as l,S as j,l as t,h as n,m as B,n as b,j as y,P as G,i as M,t as J,D as X}from"./index-D_FV2sri.js";function Te(f){return V({url:"/system/owner/blacklist/list",method:"get",params:f})}function Ve(f){return V({url:"/system/owner/blacklist/"+f,method:"get"})}function Ne(f){return V({url:"/system/owner/blacklist",method:"post",data:f})}function Ue(f){return V({url:"/system/owner/blacklist",method:"put",data:f})}function De(f){return V({url:"/system/owner/blacklist/"+f,method:"delete"})}function Z(){return V({url:"/system/owner/blacklist/warehouseOptions",method:"get"})}const Ce={class:"app-container"},xe={key:1},Ie={class:"dialog-footer"},Se=_e({name:"Blacklist"}),Pe=Object.assign(Se,{setup(f){const{proxy:v}=ve(),E=c([]),R=c([]),U=c([]),w=c(!1),x=c(!0),D=c(!0),I=c([]),q=c(!0),O=c(!0),S=c(0),Y=c(""),ee=we({form:{},queryParams:{pageNum:1,pageSize:10,plateNo:null,name:null,phoneNumber:null,warehouseId:null},rules:{plateNo:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],warehouseId:[{required:!0,message:"场库不能为空",trigger:"change"}],beginTime:[{required:!0,message:"生效时间不能为空",trigger:"blur"}],endTime:[{required:!0,message:"失效时间不能为空",trigger:"blur"}]}}),{queryParams:s,form:i,rules:le}=ye(ee);function k(){x.value=!0,Te(s.value).then(o=>{E.value=o.rows,S.value=o.total,x.value=!1})}function $(){Z().then(o=>{R.value=o.data,U.value=W(o.data)})}function W(o){if(!o||o.length===0)return[];const e=[],r={};o.forEach(u=>{r[u.id]={value:u.id,label:u.warehouseName,children:[]}}),o.forEach(u=>{u.parentId==="0"||u.parentId===0?e.push(r[u.id]):r[u.parentId]&&r[u.parentId].children.push(r[u.id])});function d(u){u.forEach(m=>{m.children&&m.children.length===0?delete m.children:m.children&&d(m.children)})}return d(e),e}function K(o,e){if(!o||!e)return null;const r=e.find(u=>u.id===o);if(!r)return null;if(r.parentId==="0"||r.parentId===0)return[o];const d=K(r.parentId,e);return d?[...d,o]:[o]}function te(){w.value=!1,P()}function P(){i.value={id:null,plateNo:null,name:null,phoneNumber:null,warehouseId:null,beginTime:null,endTime:null,imgUrl:null},v.resetForm("blacklistRef")}function N(){s.value.pageNum=1,k()}function ae(){v.resetForm("queryRef"),N()}function ne(o){I.value=o.map(e=>e.id),q.value=o.length!=1,O.value=!o.length}function oe(){P(),$(),w.value=!0,Y.value="添加黑名单管理"}function Q(o){P();const e=o.id||I.value;Promise.all([Z(),Ve(e)]).then(([r,d])=>{R.value=r.data,U.value=W(r.data),w.value=!0,Y.value="修改黑名单管理",X(()=>{i.value=d.data,i.value.beginTime&&(i.value.beginTime=i.value.beginTime.split(" ")[0]),i.value.endTime&&(i.value.endTime=i.value.endTime.split(" ")[0]),X(()=>{if(i.value.warehouseId){const u=K(i.value.warehouseId,r.data);u&&u.length>0&&(i.value.warehouseId=u)}})})})}function ie(){v.$refs.blacklistRef.validate(o=>{if(o){let e={...i.value};Array.isArray(e.warehouseId)&&(e.warehouseId=e.warehouseId[e.warehouseId.length-1]),e.beginTime&&(e.beginTime=e.beginTime+" 00:00:00"),e.endTime&&(e.endTime=e.endTime+" 23:59:59"),e.id!=null?Ue(e).then(r=>{v.$modal.msgSuccess("修改成功"),w.value=!1,k()}):Ne(e).then(r=>{v.$modal.msgSuccess("新增成功"),w.value=!1,k()})}})}function z(o){const e=o.id||I.value;v.$modal.confirm('是否确认删除黑名单管理编号为"'+e+'"的数据项？').then(function(){return De(e)}).then(()=>{k(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function re(){v.download("system/owner/blacklist/export",{...s.value},`blacklist_${new Date().getTime()}.xlsx`)}function ue(o){const e=new Date;return e.setHours(0,0,0,0),o.getTime()<e.getTime()}function de(o){const e=new Date;if(e.setHours(0,0,0,0),i.value.beginTime){const r=new Date(i.value.beginTime);return r.setHours(0,0,0,0),o.getTime()<Math.max(e.getTime(),r.getTime())}return o.getTime()<e.getTime()}return ke(()=>{k(),$()}),(o,e)=>{const r=p("el-input"),d=p("el-form-item"),u=p("el-cascader"),m=p("el-button"),A=p("el-form"),C=p("el-col"),se=p("right-toolbar"),me=p("el-row"),h=p("el-table-column"),pe=p("el-image"),ce=p("el-table"),fe=p("pagination"),F=p("el-date-picker"),ge=p("el-dialog"),T=H("hasPermi"),be=H("loading");return g(),L("div",Ce,[_(l(A,{model:t(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[l(d,{label:"车牌号",prop:"plateNo"},{default:n(()=>[l(r,{modelValue:t(s).plateNo,"onUpdate:modelValue":e[0]||(e[0]=a=>t(s).plateNo=a),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:B(N,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"姓名",prop:"name"},{default:n(()=>[l(r,{modelValue:t(s).name,"onUpdate:modelValue":e[1]||(e[1]=a=>t(s).name=a),placeholder:"请输入姓名",clearable:"",style:{width:"200px"},onKeyup:B(N,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"手机号",prop:"phoneNumber"},{default:n(()=>[l(r,{modelValue:t(s).phoneNumber,"onUpdate:modelValue":e[2]||(e[2]=a=>t(s).phoneNumber=a),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:B(N,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"场库/停车场",prop:"warehouseId"},{default:n(()=>[l(u,{modelValue:t(s).warehouseId,"onUpdate:modelValue":e[3]||(e[3]=a=>t(s).warehouseId=a),options:t(U),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0},placeholder:"请选择场库/停车场",clearable:"",style:{width:"200px"}},null,8,["modelValue","options"])]),_:1}),l(d,null,{default:n(()=>[l(m,{type:"primary",icon:"Search",onClick:N},{default:n(()=>[b("搜索")]),_:1}),l(m,{icon:"Refresh",onClick:ae},{default:n(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[j,t(D)]]),l(me,{gutter:10,class:"mb8"},{default:n(()=>[l(C,{span:1.5},{default:n(()=>[_((g(),y(m,{type:"primary",plain:"",icon:"Plus",onClick:oe},{default:n(()=>[b("新增")]),_:1})),[[T,["owner:blacklist:add"]]])]),_:1}),l(C,{span:1.5},{default:n(()=>[_((g(),y(m,{type:"success",plain:"",icon:"Edit",disabled:t(q),onClick:Q},{default:n(()=>[b("修改")]),_:1},8,["disabled"])),[[T,["owner:blacklist:edit"]]])]),_:1}),l(C,{span:1.5},{default:n(()=>[_((g(),y(m,{type:"danger",plain:"",icon:"Delete",disabled:t(O),onClick:z},{default:n(()=>[b("删除")]),_:1},8,["disabled"])),[[T,["owner:blacklist:remove"]]])]),_:1}),l(C,{span:1.5},{default:n(()=>[_((g(),y(m,{type:"warning",plain:"",icon:"Download",onClick:re},{default:n(()=>[b("导出")]),_:1})),[[T,["owner:blacklist:export"]]])]),_:1}),l(se,{showSearch:t(D),"onUpdate:showSearch":e[4]||(e[4]=a=>G(D)?D.value=a:null),onQueryTable:k},null,8,["showSearch"])]),_:1}),_((g(),y(ce,{data:t(E),onSelectionChange:ne},{default:n(()=>[l(h,{type:"selection",width:"55",align:"center"}),l(h,{label:"车牌号",align:"center",prop:"plateNo"}),l(h,{label:"车主姓名",align:"center",prop:"name"}),l(h,{label:"联系电话",align:"center",prop:"phoneNumber"}),l(h,{label:"场库名称",align:"center",prop:"warehouseName"}),l(h,{label:"生效时间",align:"center",prop:"beginTime",width:"180"},{default:n(a=>[M("span",null,J(o.parseTime(a.row.beginTime,"{y}-{m}-{d}")),1)]),_:1}),l(h,{label:"失效时间",align:"center",prop:"endTime",width:"180"},{default:n(a=>[M("span",null,J(o.parseTime(a.row.endTime,"{y}-{m}-{d}"))+" 23:59:59",1)]),_:1}),l(h,{label:"证据图片",align:"center",prop:"imgUrl",width:"100"},{default:n(a=>[a.row.imgUrl?(g(),y(pe,{key:0,style:{width:"50px",height:"50px"},src:a.row.imgUrl,"preview-src-list":[a.row.imgUrl],fit:"cover"},null,8,["src","preview-src-list"])):(g(),L("span",xe,"-"))]),_:1}),l(h,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(a=>[_((g(),y(m,{link:"",type:"primary",icon:"Edit",onClick:he=>Q(a.row)},{default:n(()=>[b("修改")]),_:2},1032,["onClick"])),[[T,["owner:blacklist:edit"]]]),_((g(),y(m,{link:"",type:"primary",icon:"Delete",onClick:he=>z(a.row)},{default:n(()=>[b("删除")]),_:2},1032,["onClick"])),[[T,["owner:blacklist:remove"]]])]),_:1})]),_:1},8,["data"])),[[be,t(x)]]),_(l(fe,{total:t(S),page:t(s).pageNum,"onUpdate:page":e[5]||(e[5]=a=>t(s).pageNum=a),limit:t(s).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>t(s).pageSize=a),onPagination:k},null,8,["total","page","limit"]),[[j,t(S)>0]]),l(ge,{title:t(Y),modelValue:t(w),"onUpdate:modelValue":e[14]||(e[14]=a=>G(w)?w.value=a:null),width:"500px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:n(()=>[M("div",Ie,[l(m,{type:"primary",onClick:ie},{default:n(()=>[b("确 定")]),_:1}),l(m,{onClick:te},{default:n(()=>[b("取 消")]),_:1})])]),default:n(()=>[l(A,{ref:"blacklistRef",model:t(i),rules:t(le),"label-width":"80px"},{default:n(()=>[l(d,{label:"车牌号",prop:"plateNo"},{default:n(()=>[l(r,{modelValue:t(i).plateNo,"onUpdate:modelValue":e[7]||(e[7]=a=>t(i).plateNo=a),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1}),l(d,{label:"车主姓名",prop:"name"},{default:n(()=>[l(r,{modelValue:t(i).name,"onUpdate:modelValue":e[8]||(e[8]=a=>t(i).name=a),placeholder:"请输入车主姓名"},null,8,["modelValue"])]),_:1}),l(d,{label:"联系电话",prop:"phoneNumber"},{default:n(()=>[l(r,{modelValue:t(i).phoneNumber,"onUpdate:modelValue":e[9]||(e[9]=a=>t(i).phoneNumber=a),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),l(d,{label:"场库/停车场",prop:"warehouseId"},{default:n(()=>[l(u,{ref:"cascaderRef",modelValue:t(i).warehouseId,"onUpdate:modelValue":e[10]||(e[10]=a=>t(i).warehouseId=a),options:t(U),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0},placeholder:"请选择场库/停车场",clearable:"",style:{width:"100%"}},null,8,["modelValue","options"])]),_:1}),l(d,{label:"生效时间",prop:"beginTime"},{default:n(()=>[l(F,{modelValue:t(i).beginTime,"onUpdate:modelValue":e[11]||(e[11]=a=>t(i).beginTime=a),type:"date",placeholder:"选择生效日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":ue,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(d,{label:"失效时间",prop:"endTime"},{default:n(()=>[l(F,{modelValue:t(i).endTime,"onUpdate:modelValue":e[12]||(e[12]=a=>t(i).endTime=a),type:"date",placeholder:"选择失效日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":de,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(d,{label:"证据图片",prop:"imgUrl"},{default:n(()=>[l(r,{modelValue:t(i).imgUrl,"onUpdate:modelValue":e[13]||(e[13]=a=>t(i).imgUrl=a),placeholder:"请输入证据图片URL"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Pe as default};
