#!/bin/bash

# 健康检查验证脚本
# 用于验证修复后的微服务健康检查功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} ${timestamp} - $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} ${timestamp} - $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
    esac
}

# 服务端口映射
declare -A SERVICE_PORTS=(
    ["park-gateway"]="8080"
    ["park-auth"]="9204"
    ["park-wx-auth"]="9205"
    ["park-system"]="9201"
    ["park-file"]="9202"
    ["park-wx"]="9206"
    ["park-gate"]="9203"
)

# 检查单个服务健康状态
check_service_health() {
    local service=$1
    local port=${SERVICE_PORTS[$service]}
    
    if [ -z "$port" ]; then
        log "WARNING" "服务 $service 没有配置端口，跳过检查"
        return 0
    fi
    
    local health_url="http://localhost:$port/actuator/health"
    
    log "INFO" "检查服务健康状态: $service ($health_url)"
    
    # 检查端口是否开放
    if ! nc -z localhost $port 2>/dev/null; then
        log "ERROR" "服务 $service 端口 $port 不可达"
        return 1
    fi
    
    # 检查健康端点
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$health_url" 2>/dev/null || echo "000")
    local http_code=${response: -3}
    
    if [ "$http_code" = "200" ]; then
        local status=$(cat /tmp/health_response.json 2>/dev/null | grep -o '"status":"[^"]*"' | cut -d'"' -f4 || echo "UNKNOWN")
        if [ "$status" = "UP" ]; then
            log "SUCCESS" "服务 $service 健康检查通过 (状态: $status)"
            return 0
        else
            log "ERROR" "服务 $service 健康检查失败 (状态: $status)"
            return 1
        fi
    else
        log "ERROR" "服务 $service 健康检查端点返回错误 (HTTP: $http_code)"
        return 1
    fi
}

# 检查Nacos服务注册状态
check_nacos_registration() {
    local service=$1
    
    log "INFO" "检查服务在Nacos中的注册状态: $service"
    
    local nacos_url="http://localhost:8848/nacos/v1/ns/instance/list?serviceName=$service"
    local response=$(curl -s "$nacos_url" 2>/dev/null || echo '{"hosts":[]}')
    
    local instance_count=$(echo "$response" | grep -o '"hosts":\[[^]]*\]' | grep -o '{"instanceId"' | wc -l || echo "0")
    
    if [ "$instance_count" -gt 0 ]; then
        log "SUCCESS" "服务 $service 已在Nacos中注册 (实例数: $instance_count)"
        return 0
    else
        log "ERROR" "服务 $service 未在Nacos中注册"
        return 1
    fi
}

# 检查Docker容器健康状态
check_docker_health() {
    local service=$1
    
    log "INFO" "检查Docker容器健康状态: $service"
    
    local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$service" 2>/dev/null || echo "unknown")
    
    case $health_status in
        "healthy")
            log "SUCCESS" "Docker容器 $service 健康状态: $health_status"
            return 0
            ;;
        "unhealthy")
            log "ERROR" "Docker容器 $service 健康状态: $health_status"
            return 1
            ;;
        "starting")
            log "WARNING" "Docker容器 $service 健康状态: $health_status (正在启动)"
            return 2
            ;;
        *)
            log "WARNING" "Docker容器 $service 健康状态: $health_status (未知状态)"
            return 2
            ;;
    esac
}

# 主验证函数
main() {
    log "INFO" "开始验证微服务健康检查功能..."
    
    local failed_services=()
    local warning_services=()
    
    # 等待服务启动
    log "INFO" "等待服务启动完成..."
    sleep 30
    
    # 检查所有服务
    for service in "${!SERVICE_PORTS[@]}"; do
        log "INFO" "========== 检查服务: $service =========="
        
        local service_failed=false
        
        # 检查Docker健康状态
        if ! check_docker_health "$service"; then
            local docker_result=$?
            if [ $docker_result -eq 1 ]; then
                service_failed=true
            elif [ $docker_result -eq 2 ]; then
                warning_services+=("$service")
            fi
        fi
        
        # 检查服务健康端点
        if ! check_service_health "$service"; then
            service_failed=true
        fi
        
        # 检查Nacos注册状态
        if ! check_nacos_registration "$service"; then
            service_failed=true
        fi
        
        if [ "$service_failed" = true ]; then
            failed_services+=("$service")
        fi
        
        echo ""
    done
    
    # 生成报告
    log "INFO" "========== 验证结果汇总 =========="
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        log "SUCCESS" "所有微服务健康检查验证通过！"
        
        if [ ${#warning_services[@]} -gt 0 ]; then
            log "WARNING" "以下服务有警告: ${warning_services[*]}"
        fi
        
        exit 0
    else
        log "ERROR" "以下服务验证失败: ${failed_services[*]}"
        
        if [ ${#warning_services[@]} -gt 0 ]; then
            log "WARNING" "以下服务有警告: ${warning_services[*]}"
        fi
        
        exit 1
    fi
}

# 清理临时文件
cleanup() {
    rm -f /tmp/health_response.json
}

trap cleanup EXIT

# 执行主函数
main "$@"
