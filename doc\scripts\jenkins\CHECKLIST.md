# Jenkins 流水线部署检查清单

## 🔍 部署前检查

### 1. 服务器环境检查
- [ ] 服务器可通过SSH访问 (*************:1224)
- [ ] Docker服务运行正常
- [ ] Nginx服务运行正常
- [ ] 防火墙转发规则配置正确 (3433→433)
- [ ] 系统端口监听正常 (433, 9001, 9433)

### 2. Jenkins配置检查
- [ ] Jenkins服务运行正常 (jenkins-park容器)
- [ ] Jenkins可通过外部访问 (https://test-parknew.lgfw24hours.com:3443/jenkins)
- [ ] Git凭据配置正确 (codeup-credentials)
- [ ] Pipeline任务创建完成

### 3. 代码仓库检查
- [ ] 后端仓库可访问 (https://codeup.aliyun.com/.../park-api.git)
- [ ] 前端仓库可访问 (https://codeup.aliyun.com/.../park-ui.git)
- [ ] 分支存在 (后端:park, 前端:test)
- [ ] scripts目录存在于后端仓库

## 🚀 部署流程检查

### 1. 代码拉取阶段
- [ ] 后端代码拉取成功
- [ ] 前端代码拉取成功
- [ ] 脚本文件拉取成功
- [ ] Git环境变量设置正确

### 2. 环境准备阶段
- [ ] 项目目录创建成功 (/opt/park-new)
- [ ] 代码复制到项目目录
- [ ] 脚本执行权限设置
- [ ] 必要工具检查通过

### 3. 代码验证阶段
- [ ] 代码质量检查 (SonarQube)
- [ ] 安全扫描 (依赖漏洞检测)
- [ ] 脚本语法检查
- [ ] Docker文件检查

### 4. 构建阶段
- [ ] 后端构建成功 (Maven)
- [ ] 前端构建成功 (Node.js)
- [ ] 构建日志记录完整
- [ ] 构建产物生成

### 5. 测试阶段
- [ ] 后端单元测试通过
- [ ] 前端单元测试通过
- [ ] 集成测试通过
- [ ] API测试通过

### 6. 镜像构建阶段
- [ ] 后端Docker镜像构建成功
- [ ] 前端Docker镜像构建成功
- [ ] 镜像标签正确
- [ ] 镜像大小合理

### 7. 部署阶段
- [ ] 容器停止成功
- [ ] 新容器启动成功
- [ ] 容器健康检查通过
- [ ] 端口映射正确

## ✅ 部署后验证

### 1. 网络架构验证
- [ ] 防火墙转发规则正常
- [ ] Nginx配置正确
- [ ] 系统端口监听正常
- [ ] DNS解析正常

### 2. 容器状态验证
- [ ] jenkins-park容器运行正常
- [ ] park-nginx容器运行正常
- [ ] park-backend容器运行正常
- [ ] 容器端口映射正确

### 3. 应用功能验证
- [ ] 前端页面可访问 (https://test-parknew.lgfw24hours.com:3443/)
- [ ] Jenkins可访问 (https://test-parknew.lgfw24hours.com:3443/jenkins)
- [ ] API健康检查通过 (https://test-parknew.lgfw24hours.com:3443/api/actuator/health)
- [ ] 数据库连接正常
- [ ] Redis连接正常

### 4. 业务功能验证
- [ ] 用户登录功能正常
- [ ] 停车场列表加载正常
- [ ] 订单管理功能正常
- [ ] 支付功能正常

### 5. 性能验证
- [ ] 前端响应时间 < 5秒
- [ ] API响应时间 < 2秒
- [ ] 容器资源使用正常
- [ ] 内存使用 < 80%

### 6. 安全验证
- [ ] HTTPS证书有效
- [ ] 敏感端口未暴露
- [ ] 访问权限控制正常
- [ ] 日志记录完整

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 代码拉取失败
```bash
# 检查Git凭据
jenkins -> 凭据管理 -> 检查codeup-credentials

# 检查网络连接
curl -I https://codeup.aliyun.com

# 检查仓库地址和分支
git ls-remote https://codeup.aliyun.com/.../park-api.git
```

#### 2. 容器启动失败
```bash
# 检查容器日志
docker logs jenkins-park
docker logs park-nginx
docker logs park-backend

# 检查端口占用
netstat -tlnp | grep -E ':(9001|9433|8080)'

# 检查Docker服务
systemctl status docker
```

#### 3. 网络访问失败
```bash
# 检查防火墙规则
iptables -t nat -L -n | grep 3433

# 检查Nginx配置
nginx -t
systemctl status nginx

# 检查DNS解析
nslookup test-parknew.lgfw24hours.com
```

#### 4. API访问失败
```bash
# 检查后端容器状态
docker ps | grep park-backend

# 检查数据库连接
docker exec park-backend curl localhost:8080/actuator/health/db

# 检查nginx转发配置
docker exec park-nginx nginx -t
```

## 📊 监控和维护

### 日志文件位置
- Jenkins构建日志: Jenkins Web界面
- 应用日志: `/opt/park-new/logs/`
- 容器日志: `docker logs <container_name>`
- 系统日志: `/var/log/`

### 定期检查项目
- [ ] 每日检查容器状态
- [ ] 每周检查磁盘空间
- [ ] 每月检查安全更新
- [ ] 每季度检查性能指标

### 备份策略
- [ ] 数据库每日备份
- [ ] 配置文件版本控制
- [ ] 镜像定期推送到仓库
- [ ] 日志文件定期归档

## 📞 联系信息

### 技术支持
- 开发团队: [开发团队联系方式]
- 运维团队: [运维团队联系方式]
- 紧急联系: [紧急联系方式]

### 相关文档
- [Jenkins用户手册](./README.md)
- [环境配置指南](./SETUP.md)
- [故障排除指南](./TROUBLESHOOTING.md)
- [API文档](../docs/API.md)
