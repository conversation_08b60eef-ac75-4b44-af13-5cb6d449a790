#!/bin/bash

# 停车管理系统后端构建脚本
# 作者: DevOps Team
# 版本: 1.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 解析命令行参数
SPECIFIC_SERVICE=""
SKIP_TESTS="false"
CLEAN_BUILD="false"
BUILD_DOCKER="true"

while [[ $# -gt 0 ]]; do
    case $1 in
        --service=*)
            SPECIFIC_SERVICE="${1#*=}"
            shift
            ;;
        --skip-tests)
            SKIP_TESTS="true"
            shift
            ;;
        --clean)
            CLEAN_BUILD="true"
            shift
            ;;
        --no-docker)
            BUILD_DOCKER="false"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --service=SERVICE    构建指定服务 (gateway|auth|system|file|wx|gate|wx_auth)"
            echo "  --skip-tests         跳过测试"
            echo "  --clean              清理构建"
            echo "  --no-docker          不构建Docker镜像"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 构建Maven项目
build_maven_project() {
    local service="$1"
    local module="${MAVEN_MODULES[$service]}"
    
    if [ -z "$module" ]; then
        log "WARN" "服务 $service 没有对应的Maven模块，跳过"
        return 0
    fi
    
    log "INFO" "构建Maven模块: $module"
    
    cd "$BACKEND_DIR"
    
    # 构建命令
    local mvn_cmd="mvn"
    
    if [ "$CLEAN_BUILD" = "true" ]; then
        mvn_cmd="$mvn_cmd clean"
    fi
    
    mvn_cmd="$mvn_cmd package -pl $module -am"
    
    if [ "$SKIP_TESTS" = "true" ]; then
        mvn_cmd="$mvn_cmd -DskipTests"
    fi
    
    # 设置Maven选项
    export MAVEN_OPTS="$MAVEN_OPTS"
    
    log "INFO" "执行命令: $mvn_cmd"
    
    if ! $mvn_cmd; then
        log "ERROR" "Maven构建失败: $service"
        return 1
    fi
    
    log "INFO" "Maven构建完成: $service"
    return 0
}

# 构建Docker镜像
build_docker_image() {
    local service="$1"
    local build_path="${SERVICE_BUILD_PATHS[$service]}"
    local image_name="${SERVICE_IMAGES[$service]}"
    
    if [ -z "$build_path" ] || [ -z "$image_name" ]; then
        log "WARN" "服务 $service 没有Docker构建配置，跳过"
        return 0
    fi
    
    log "INFO" "构建Docker镜像: $service"
    
    local dockerfile_path="$BACKEND_DIR/$build_path/Dockerfile"
    
    if [ ! -f "$dockerfile_path" ]; then
        log "ERROR" "Dockerfile不存在: $dockerfile_path"
        return 1
    fi
    
    cd "$BACKEND_DIR"
    
    # 构建镜像
    local build_cmd="docker build -t $image_name -f $build_path/Dockerfile ."
    
    log "INFO" "执行命令: $build_cmd"
    
    if ! $build_cmd; then
        log "ERROR" "Docker镜像构建失败: $service"
        return 1
    fi
    
    log "INFO" "Docker镜像构建完成: $service ($image_name)"
    return 0
}

# 构建单个服务
build_service() {
    local service="$1"
    
    log "INFO" "开始构建服务: $service"
    
    # Maven构建
    if ! build_maven_project "$service"; then
        log "ERROR" "服务 $service Maven构建失败"
        return 1
    fi
    
    # Docker镜像构建
    if [ "$BUILD_DOCKER" = "true" ]; then
        if ! build_docker_image "$service"; then
            log "ERROR" "服务 $service Docker镜像构建失败"
            return 1
        fi
    fi
    
    log "INFO" "服务 $service 构建完成"
    return 0
}

# 主构建函数
main() {
    log "INFO" "开始后端构建..."
    log "INFO" "项目版本: $PROJECT_VERSION"
    log "INFO" "Git分支: $GIT_BRANCH_CURRENT"
    log "INFO" "Git提交: $GIT_COMMIT_SHORT"
    
    # 确定要构建的服务
    local services_to_build=()
    
    if [ -n "$SPECIFIC_SERVICE" ]; then
        # 构建指定服务
        local service_upper=$(echo "$SPECIFIC_SERVICE" | tr '[:lower:]' '[:upper:]')
        services_to_build=("$service_upper")
        log "INFO" "构建指定服务: $SPECIFIC_SERVICE"
    else
        # 构建所有后端服务
        services_to_build=($(get_services_by_group "backend"))
        log "INFO" "构建所有后端服务"
    fi
    
    # 按顺序构建服务
    local failed_services=()
    
    for service in "${services_to_build[@]}"; do
        if ! build_service "$service"; then
            failed_services+=("$service")
        fi
    done
    
    # 构建结果
    if [ ${#failed_services[@]} -eq 0 ]; then
        log "INFO" "所有服务构建成功"
        
        # 显示构建的镜像
        if [ "$BUILD_DOCKER" = "true" ]; then
            log "INFO" "构建的Docker镜像:"
            for service in "${services_to_build[@]}"; do
                local image_name="${SERVICE_IMAGES[$service]}"
                if [ -n "$image_name" ]; then
                    docker images "$image_name" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
                fi
            done
        fi
        
        return 0
    else
        log "ERROR" "以下服务构建失败: ${failed_services[*]}"
        return 1
    fi
}

# 执行主函数
main "$@"
