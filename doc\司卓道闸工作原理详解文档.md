# 司卓道闸工作原理详解文档

## 目录

1. [系统概述](#1-系统概述)
2. [整体架构流程](#2-整体架构流程)
3. [核心接口详解](#3-核心接口详解)
4. [司卓适配器实现](#4-司卓适配器实现)
5. [司卓工具类详解](#5-司卓工具类详解)
6. [业务接口说明](#6-业务接口说明)
7. [关键参数定义](#7-关键参数定义)
8. [错误处理机制](#8-错误处理机制)
9. [调试和监控](#9-调试和监控)

---

## 1. 系统概述

司卓道闸系统是停车场管理系统中专门用于控制司卓厂商道闸设备的核心模块。系统采用适配器模式，将统一的道闸控制接口适配为司卓特定的 API 协议，实现对司卓道闸设备的远程控制。

### 1.1 主要功能

- **道闸控制**：远程开闸、关闸操作
- **车辆管理**：车辆入场、出场处理
- **支付处理**：停车费用支付
- **状态监控**：设备状态实时监控
- **日志记录**：完整的操作审计日志

### 1.2 技术特点

- **协议适配**：司卓特定的 API 协议和签名算法
- **安全认证**：MD5 签名验证机制
- **重试机制**：网络异常自动重试
- **日志审计**：详细的操作日志记录

---

## 2. 整体架构流程

### 2.1 系统架构图

```mermaid
graph TD
    A[前端界面] --> B[API网关]
    B --> C[停车管理服务]
    C --> D[道闸网关服务]
    D --> E[司卓适配器]
    E --> F[司卓工具类]
    E --> G[司卓道闸设备]

    H[(MySQL数据库)] --> C
    I[Redis缓存] --> C
    J[日志系统] --> E
```

### 2.2 完整调用时序图

```mermaid
sequenceDiagram
    participant Frontend as 前端界面
    participant Gateway as API网关
    participant ParkingManage as 停车管理服务
    participant Database as 数据库
    participant GateService as 道闸网关服务
    participant SZAdapter as 司卓适配器
    participant SZUtil as 司卓工具类
    participant SZDevice as 司卓道闸设备
    participant LogSystem as 日志系统

    Frontend->>Gateway: 1. POST /device/gate/controlBarrierGate
    Note over Frontend,Gateway: {gateNo: "GATE001", controlType: 0}

    Gateway->>ParkingManage: 2. 路由到停车管理服务
    ParkingManage->>ParkingManage: 3. JWT Token验证
    ParkingManage->>Database: 4. 查询设备信息
    Database-->>ParkingManage: 5. 返回设备状态

    ParkingManage->>ParkingManage: 6. 验证设备状态
    Note over ParkingManage: 检查设备是否在线

    ParkingManage->>GateService: 7. Feign调用道闸网关
    Note over ParkingManage,GateService: /nicheng/GateControl/gateControl<br/>{parkingId: "sizhuo_001", channelIndex: 1, controlType: 0}

    GateService->>GateService: 8. 路由判断
    Note over GateService: 根据parkingId路由到司卓适配器

    GateService->>SZAdapter: 9. 调用司卓适配器
    SZAdapter->>SZUtil: 10. 获取停车场ID映射
    SZUtil-->>SZAdapter: 11. 返回司卓停车场ID

    SZAdapter->>SZUtil: 12. 获取API地址
    SZUtil-->>SZAdapter: 13. 返回司卓API URL

    SZAdapter->>SZAdapter: 14. 构建请求数据
    Note over SZAdapter: 组装timestamp, channelIndex, controlType

    SZAdapter->>SZUtil: 15. 生成签名
    SZUtil-->>SZAdapter: 16. 返回MD5签名

    SZAdapter->>SZDevice: 17. HTTP POST 司卓API
    Note over SZAdapter,SZDevice: 司卓协议格式数据

    SZDevice-->>SZAdapter: 18. 返回控制结果
    Note over SZDevice,SZAdapter: {resultCode: 1, resultMsg: "操作成功"}

    SZAdapter->>LogSystem: 19. 记录操作日志
    SZAdapter->>SZAdapter: 20. 处理响应结果

    SZAdapter-->>GateService: 21. 返回标准化结果
    GateService-->>ParkingManage: 22. 返回控制结果

    ParkingManage->>Database: 23. 保存操作记录
    ParkingManage-->>Gateway: 24. 返回最终结果
    Gateway-->>Frontend: 25. 显示操作状态
```

---

## 3. 核心接口详解

### 3.1 前端调用接口

#### 接口信息

- **接口名称**: `POST /device/gate/controlBarrierGate`
- **实现位置**: `SicvDeviceBarrierGateController.controlBarrierGate()`
- **功能描述**: 前端发起道闸控制请求

#### 请求参数

```json
{
  "gateNo": "GATE001", // 栏位编号（必填）
  "controlType": 0, // 控制类型：0-开闸，1-关闸
  "warehouseId": 1, // 停车场ID
  "plateNo": "沪A12345" // 车牌号（可选）
}
```

#### 响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null,
  "success": true
}
```

#### 实现代码

```java
@PostMapping("/controlBarrierGate")
public Result<?> controlBarrierGate(@RequestBody SicvDeviceBarrierGate barrierGate) {
    sicvDeviceBarrierGateService.controlBarrierGate(barrierGate);
    return Result.success();
}
```

### 3.2 停车管理服务内部调用

#### 方法信息

- **方法名称**: `BarrierGateServiceImpl.control()`
- **功能描述**: 验证设备状态并调用道闸网关服务

#### 核心参数

```java
BarrierGateRecord barrierGateRecord = {
    gateNo: "GATE001",             // 栏位编号
    type: 0,                       // 控制类型
    warehouseId: 1,                // 停车场ID
    plateNo: "沪A12345"            // 车牌号
}
```

#### 关键业务逻辑

```java
@Override
public Result<?> control(BarrierGateRecord barrierGateRecord) {
    String token = SecurityUtils.getToken(request);
    String userId = JwtUtils.getUserId(token);

    try {
        // 1. 参数验证
        if (barrierGateRecord == null || StringUtils.isEmpty(barrierGateRecord.getGateNo())) {
            return Result.fail("参数不能为空");
        }

        // 2. 验证设备状态
        BarrierGate device = barrierGateMapper.selectByGateNo(barrierGateRecord.getGateNo());
        if (device == null) {
            return Result.fail("设备不存在");
        }
        if (device.getStatus() != 1) {
            return Result.fail("设备离线，无法控制");
        }

        // 3. 调用道闸网关服务
        Result<?> result = gateServer.gateControl(
            String.valueOf(barrierGateRecord.getType()),  // "0" 或 "1"
            barrierGateRecord.getGateNo()                 // "GATE001"
        );

        Thread.sleep(5000L); // 等待设备响应

        // 4. 处理结果并记录日志
        Boolean success = result.getResult();
        if (success) {
            barrierGateRecord.setUserId(Long.parseLong(userId));
            barrierGateRecord.setCreateBy(Long.parseLong(userId));
            barrierGateRecordMapper.insertSelective(barrierGateRecord);
            return Result.success("道闸控制成功");
        } else {
            return Result.fail("道闸控制失败：" + result.getMsg());
        }

    } catch (Exception e) {
        log.error("道闸控制异常", e);
        return Result.fail("道闸控制异常：" + e.getMessage());
    }
}
```

### 3.3 道闸网关服务接口

#### 接口信息

- **接口名称**: `GET/POST /nicheng/GateControl/gateControl`
- **实现位置**: `GateController.gateControl()`
- **功能描述**: 根据停车场 ID 路由到对应厂商适配器

#### 请求参数

```java
@RequestParam("parkingId") String parkingId,      // 停车场ID，用于路由厂商
@RequestParam("channelIndex") int channelIndex,   // 通道索引（1-进场，2-出场）
@RequestParam("controlType") int controlType      // 控制类型（0-开闸，1-关闸）
```

#### 路由逻辑

```java
@RequestMapping("/gateControl")
@ResponseBody
public CommonPojo gateControl(@RequestParam("parkingId") String parkingId,
                             @RequestParam("channelIndex") int channelIndex,
                             @RequestParam("controlType") int controlType,
                             HttpServletRequest request)
        throws UnsupportedEncodingException, NoSuchAlgorithmException {

    LOGGER.info("gateControl接口被调用, remoteAddr: {}, channelIndex: {}, controlType: {}",
               request.getRemoteAddr(), channelIndex, controlType);

    // 根据停车场ID路由到对应厂商的适配器
    if (parkingId.equals(Common.parkingId_sizhuo)) {
        // 司卓道闸控制
        return gateControl2Service.gateControl(parkingId, channelIndex, controlType, request.getRemoteAddr());
    } else if (parkingId.equals(Common.parkingId_hongyin)) {
        // 泓音道闸控制
        return gateControlService.gateControl(parkingId, channelIndex, controlType, request.getRemoteAddr());
    } else {
        // 默认使用司卓道闸控制
        return gateControl2Service.gateControl(parkingId, channelIndex, controlType, request.getRemoteAddr());
    }
}
```

---

## 4. 司卓适配器实现

### 4.1 司卓道闸控制核心方法

#### 方法信息

- **方法名称**: `GateControl2ServiceImpl.gateControl()`
- **实现位置**: `gate/nicheng-gate-server/src/main/java/com/sicv/parking/gate/service/impl/GateControl2ServiceImpl.java`
- **功能描述**: 司卓道闸控制的核心实现

#### 方法签名

```java
public CommonPojo gateControl(String parkingId, int channelIndex, int controlType, String accessAddress)
```

#### 核心参数说明

```java
String parkingId,      // 停车场ID："sizhuo_001"
int channelIndex,      // 通道索引：1-进场，2-出场
int controlType,       // 控制类型：0-开闸，1-关闸
String accessAddress   // 访问地址（用于日志记录）
```

#### 完整实现代码

```java
public CommonPojo gateControl(String parkingId, int channelIndex, int controlType, String accessAddress) {
    String parm = "parkingId:" + parkingId + "&&" + "channelIndex:" + channelIndex + "&&" + "controlType:" + controlType;
    CommonPojo commonPojo = new CommonPojo();
    commonPojo.setResult(true);

    try {
        // 1. 构建基础数据
        long time = System.currentTimeMillis();
        JSONObject data = new JSONObject();
        data.put("timestamp", time);                    // 时间戳
        data.put("channelIndex", channelIndex);         // 通道索引
        data.put("controlType", controlType);           // 控制类型

        // 2. 构建签名数据
        StringBuilder signData = new StringBuilder();
        signData.append("channelIndex=").append(channelIndex)
               .append("&controlType=").append(controlType)
               .append("&parkingID=").append(SZUtil.getParkingId(parkingId))  // 司卓内部停车场ID
               .append("&timestamp=").append(time);

        // 3. 生成司卓签名
        String sign = SZUtil.getSign(signData.toString());
        data.put("sign", sign);                         // 签名
        data.put("parkingID", SZUtil.getParkingId(parkingId));  // 司卓停车场ID

        // 4. 构建请求
        String url = SZUtil.getUrl(parkingId) + "/gateControl";
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json;charset=UTF-8");
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(data, headers);

        // 5. 发送请求
        LOGGER.info("司卓道闸控制请求: url={}, data={}", url, data.toJSONString());
        JSONObject jsonObject = restTemplate.postForObject(url, httpEntity, JSONObject.class);
        LOGGER.info("司卓道闸控制响应: {}", jsonObject.toJSONString());

        // 6. 处理响应结果
        if (jsonObject.getInteger("resultCode") == 1) {
            commonPojo.setResult(true);
            commonPojo.setMsg("司卓道闸控制成功");
        } else {
            commonPojo.setResult(false);
            commonPojo.setMsg("司卓道闸控制失败：" + jsonObject.getString("resultMsg"));
        }

        // 7. 记录操作日志
        GateLog gateLog = new GateLog();
        gateLog.setParkingId(parkingId);
        gateLog.setChannelIndex(channelIndex);
        gateLog.setControlType(controlType);
        gateLog.setAccessAddress(accessAddress);
        gateLog.setResult(commonPojo.isResult() ? 1 : 0);
        gateLog.setErrorMsg(commonPojo.getMsg());
        gateLog.setCreateTime(new Date());
        gateLogMapper.insert(gateLog);

    } catch (Exception e) {
        LOGGER.error("司卓道闸控制异常", e);
        commonPojo.setResult(false);
        commonPojo.setMsg("司卓道闸控制异常：" + e.getMessage());
    }

    return commonPojo;
}
```

### 4.2 司卓 API 请求格式

#### 请求地址构建

```java
String url = SZUtil.getUrl(parkingId) + "/gateControl";
// 示例：http://sz-api1.example.com/gateControl
```

#### 请求头设置

```java
HttpHeaders headers = new HttpHeaders();
headers.add("Content-Type", "application/json;charset=UTF-8");
```

#### 请求体格式

```json
{
  "timestamp": 1703123456789, // 时间戳（毫秒）
  "channelIndex": 1, // 通道索引
  "controlType": 0, // 控制类型
  "parkingID": "SZ001", // 司卓停车场ID
  "sign": "ABC123DEF456..." // MD5签名
}
```

#### 响应格式

```json
{
  "resultCode": 1, // 1-成功，0-失败
  "resultMsg": "操作成功", // 结果消息
  "data": {
    // 附加数据（可选）
    "gateStatus": "opened", // 道闸状态
    "timestamp": 1703123456789 // 响应时间戳
  }
}
```

---

## 5. 司卓工具类详解

### 5.1 SZUtil 工具类概述

**文件位置**: `gate/nicheng-gate-server/src/main/java/com/sicv/parking/gate/util/SZUtil.java`

司卓工具类提供了司卓道闸系统所需的核心工具方法，包括停车场 ID 映射、API 地址获取、签名生成等功能。

### 5.2 停车场 ID 映射

#### 方法信息

- **方法名称**: `SZUtil.getParkingId(String parkingId)`
- **功能描述**: 将系统内部停车场 ID 映射为司卓系统的停车场 ID

#### 实现代码

```java
/**
 * 获取司卓系统的停车场ID
 * @param parkingId 系统内部停车场ID
 * @return 司卓停车场ID
 */
public static String getParkingId(String parkingId) {
    switch(parkingId) {
        case "nicheng_001":
            return "SZ001";
        case "yunle_002":
            return "SZ002";
        case "xilan_003":
            return "SZ003";
        case "xingning_004":
            return "SZ004";
        case "yuehui_005":
            return "SZ005";
        case "zhaolu_006":
            return "SZ006";
        case "changxiang_007":
            return "SZ007";
        case "changxiang2_008":
            return "SZ008";
        case "huifeng_009":
            return "SZ009";
        case "huating_010":
            return "SZ010";
        default:
            return "SZ999";  // 默认停车场ID
    }
}
```

#### 映射关系表

| 系统内部 ID  | 司卓停车场 ID | 停车场名称 |
| ------------ | ------------- | ---------- |
| nicheng_001  | SZ001         | 泥城停车场 |
| yunle_002    | SZ002         | 云乐停车场 |
| xilan_003    | SZ003         | 西兰停车场 |
| xingning_004 | SZ004         | 兴宁停车场 |
| yuehui_005   | SZ005         | 悦汇停车场 |

### 5.3 API 地址获取

#### 方法信息

- **方法名称**: `SZUtil.getUrl(String parkingId)`
- **功能描述**: 根据停车场 ID 获取对应的司卓 API 地址

#### 实现代码

```java
/**
 * 获取司卓API地址
 * @param parkingId 停车场ID
 * @return 司卓API基础地址
 */
public static String getUrl(String parkingId) {
    switch(parkingId) {
        case "nicheng_001":
            return "http://sz-api1.example.com";
        case "yunle_002":
            return "http://sz-api2.example.com";
        case "xilan_003":
            return "http://sz-api3.example.com";
        case "xingning_004":
            return "http://sz-api4.example.com";
        default:
            return "http://sz-api-default.example.com";
    }
}
```

#### API 地址配置表

| 停车场 ID   | API 地址                   | 环境     |
| ----------- | -------------------------- | -------- |
| nicheng_001 | http://sz-api1.example.com | 生产环境 |
| yunle_002   | http://sz-api2.example.com | 生产环境 |
| test        | http://sz-test.example.com | 测试环境 |

### 5.4 签名生成算法

#### 方法信息

- **方法名称**: `SZUtil.getSign(String signData)`
- **功能描述**: 生成司卓 API 所需的 MD5 签名

#### 签名算法流程

```mermaid
graph TD
    A[原始签名数据] --> B[添加密钥]
    B --> C[UTF-8编码]
    C --> D[MD5加密]
    D --> E[转换为16进制]
    E --> F[转换为大写]
    F --> G[返回签名]
```

#### 实现代码

```java
/**
 * 生成司卓API签名
 * @param signData 待签名的数据字符串
 * @return MD5签名字符串（大写）
 */
public static String getSign(String signData) {
    try {
        // 1. 添加密钥
        String dataWithKey = signData + "&key=" + SECRET_KEY;

        // 2. MD5加密
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(dataWithKey.getBytes("UTF-8"));

        // 3. 转换为16进制字符串
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }

        // 4. 转换为大写并返回
        return sb.toString().toUpperCase();

    } catch (Exception e) {
        throw new RuntimeException("签名生成失败", e);
    }
}
```

#### 签名示例

```java
// 原始数据
String signData = "channelIndex=1&controlType=0&parkingID=SZ001&timestamp=1703123456789";

// 添加密钥后
String dataWithKey = "channelIndex=1&controlType=0&parkingID=SZ001&timestamp=1703123456789&key=your_secret_key";

// MD5签名结果
String sign = "A1B2C3D4E5F6789012345678901234AB";
```

### 5.5 其他工具方法

#### 时间戳生成

```java
/**
 * 获取当前时间戳（毫秒）
 * @return 时间戳
 */
public static long getCurrentTimestamp() {
    return System.currentTimeMillis();
}
```

#### 参数验证

```java
/**
 * 验证必要参数
 * @param parkingId 停车场ID
 * @param channelIndex 通道索引
 * @param controlType 控制类型
 * @return 验证结果
 */
public static boolean validateParams(String parkingId, int channelIndex, int controlType) {
    if (StringUtils.isEmpty(parkingId)) {
        return false;
    }
    if (channelIndex < 1 || channelIndex > 10) {
        return false;
    }
    if (controlType < 0 || controlType > 1) {
        return false;
    }
    return true;
}
```

---

## 6. 业务接口说明

### 6.1 车辆入场接口

#### 接口信息

- **方法名称**: `noPlateIn(String parkingId, String plateNum, int channelIndex, String accessAddress)`
- **功能描述**: 处理车辆入场，无牌车或识别失败车辆的入场处理

#### 核心参数

```java
String parkingId,      // 停车场ID："nicheng_001"
String plateNum,       // 车牌号："沪A12345"（无牌车可为空）
int channelIndex,      // 通道索引：1-进场通道
String accessAddress   // 访问地址（用于日志）
```

#### 司卓 API 请求

```json
{
  "timestamp": 1703123456789,
  "plate": "沪A12345",
  "channelIndex": 1,
  "parkingID": "SZ001",
  "sign": "ABC123..."
}
```

#### 实现代码片段

```java
public CommonPojo noPlateIn(String parkingId, String plateNum, int channelIndex, String accessAddress) {
    CommonPojo commonPojo = new CommonPojo();
    try {
        long time = System.currentTimeMillis();
        JSONObject data = new JSONObject();
        data.put("timestamp", time);
        data.put("plate", plateNum);
        data.put("channelIndex", channelIndex);

        // 构建签名数据
        StringBuilder signData = new StringBuilder();
        signData.append("channelIndex=").append(channelIndex)
               .append("&parkingID=").append(SZUtil.getParkingId(parkingId))
               .append("&plate=").append(plateNum)
               .append("&timestamp=").append(time);

        String sign = SZUtil.getSign(signData.toString());
        data.put("sign", sign);
        data.put("parkingID", SZUtil.getParkingId(parkingId));

        String url = SZUtil.getUrl(parkingId) + "/noPlateIn";
        // 发送请求...

    } catch (Exception e) {
        // 异常处理...
    }
    return commonPojo;
}
```

### 6.2 车辆出场接口

#### 接口信息

- **方法名称**: `noPlateOut(String parkingId, String plateNum, double money, int payType, int channelIndex, String accessAddress)`
- **功能描述**: 处理车辆出场，包含费用计算和支付处理

#### 核心参数

```java
String parkingId,      // 停车场ID
String plateNum,       // 车牌号："沪A12345"
double money,          // 停车费用：15.50（元）
int payType,           // 支付类型：1-现金，2-微信，3-支付宝
int channelIndex,      // 通道索引：2-出场通道
String accessAddress   // 访问地址
```

#### 司卓 API 请求

```json
{
  "timestamp": 1703123456789,
  "plate": "沪A12345",
  "amount": 1550, // 金额（分）
  "payType": 2, // 支付类型
  "channelIndex": 2, // 出场通道
  "parkingID": "SZ001",
  "sign": "DEF456..."
}
```

### 6.3 订单支付接口

#### 接口信息

- **方法名称**: `payOrder(String parkingId, String plateNum, double money, int payType, String accessAddress)`
- **功能描述**: 处理停车费用支付

#### 核心参数

```java
String parkingId,      // 停车场ID
String plateNum,       // 车牌号
double money,          // 支付金额：15.50（元）
int payType,           // 支付类型
String accessAddress   // 访问地址
```

#### 司卓支付请求格式

```json
{
  "amount": 1550, // 金额（分）
  "timestamp": 1703123456789, // 时间戳
  "plate": "沪A12345", // 车牌号
  "payType": 2, // 支付类型
  "tradeNo": "sicv20231221001", // 交易号（系统生成）
  "parkingID": "SZ001",
  "sign": "GHI789..." // 签名
}
```

#### 交易号生成规则

```java
// 交易号格式：sicv + yyyyMMddHHmmss + 3位随机数
String tradeNo = "sicv" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) +
                String.format("%03d", new Random().nextInt(1000));
// 示例：sicv20231221143025001
```

### 6.4 车辆信息保存接口

#### 接口信息

- **方法名称**: `saveCar(int type, String userId, long id, String parkingId, String plateNum, double money, long beginDate, long endDate, String accessAddress)`
- **功能描述**: 保存月卡车辆或临时车辆信息

#### 核心参数

```java
int type,              // 车辆类型：1-月卡车，2-临时车
String userId,         // 用户ID
long id,               // 车辆记录ID
String parkingId,      // 停车场ID
String plateNum,       // 车牌号
double money,          // 费用金额
long beginDate,        // 开始时间（时间戳）
long endDate,          // 结束时间（时间戳）
String accessAddress   // 访问地址
```

#### 司卓车辆信息格式

```json
{
  "timestamp": 1703123456789,
  "plate": "沪A12345",
  "carType": 1, // 车辆类型
  "amount": 30000, // 月卡金额（分）
  "beginTime": 1703123456789, // 开始时间
  "endTime": 1706123456789, // 结束时间
  "parkingID": "SZ001",
  "sign": "JKL012..."
}
```

---

## 7. 关键参数定义

### 7.1 控制类型 (controlType)

| 值  | 含义         | 说明                     |
| --- | ------------ | ------------------------ |
| 0   | 开闸（抬杆） | 道闸杆抬起，允许车辆通过 |
| 1   | 关闸（落杆） | 道闸杆落下，阻止车辆通过 |

### 7.2 通道索引 (channelIndex)

| 值   | 含义     | 说明                   |
| ---- | -------- | ---------------------- |
| 1    | 进场通道 | 车辆入场专用通道       |
| 2    | 出场通道 | 车辆出场专用通道       |
| 3-10 | 其他通道 | 根据停车场实际情况配置 |

### 7.3 支付类型 (payType)

| 值  | 含义       | 说明               |
| --- | ---------- | ------------------ |
| 1   | 现金支付   | 现场现金缴费       |
| 2   | 微信支付   | 微信扫码支付       |
| 3   | 支付宝支付 | 支付宝扫码支付     |
| 4   | 银行卡支付 | 刷卡支付           |
| 5   | 免费放行   | 月卡车辆或免费车辆 |

### 7.4 设备状态 (status)

| 值  | 含义     | 说明               |
| --- | -------- | ------------------ |
| 1   | 正常运行 | 设备在线且功能正常 |
| 2   | 离线状态 | 设备网络连接中断   |
| 3   | 故障状态 | 设备硬件或软件故障 |

### 7.5 车辆类型 (carType)

| 值  | 含义     | 说明           |
| --- | -------- | -------------- |
| 1   | 月卡车辆 | 包月停车的车辆 |
| 2   | 临时车辆 | 按次收费的车辆 |
| 3   | VIP 车辆 | 免费或优惠车辆 |
| 4   | 访客车辆 | 访客预约车辆   |

### 7.6 司卓响应码 (resultCode)

| 值  | 含义     | 说明             |
| --- | -------- | ---------------- |
| 1   | 操作成功 | 道闸控制成功执行 |
| 0   | 操作失败 | 道闸控制执行失败 |
| -1  | 参数错误 | 请求参数格式错误 |
| -2  | 签名错误 | 签名验证失败     |
| -3  | 设备离线 | 道闸设备不在线   |

---

## 8. 错误处理机制

### 8.1 重试机制配置

#### RestTemplate 重试配置

```java
// 重试配置参数
private final RestTemplate restTemplate = RetryRestTemplate.build(
    3,      // 重试次数：3次
    100,    // 初始延迟：100ms
    2000,   // 连接超时：2000ms
    2000    // 读取超时：2000ms
);
```

#### 重试策略

```mermaid
graph TD
    A[发送请求] --> B{请求成功?}
    B -->|是| C[返回结果]
    B -->|否| D{重试次数<3?}
    D -->|是| E[等待100ms]
    E --> F[重试次数+1]
    F --> A
    D -->|否| G[返回失败]
```

### 8.2 异常处理

#### 网络异常处理

```java
try {
    JSONObject response = restTemplate.postForObject(url, httpEntity, JSONObject.class);
    // 处理正常响应
} catch (ResourceAccessException e) {
    // 网络连接异常
    LOGGER.error("司卓API网络连接异常: {}", e.getMessage());
    commonPojo.setResult(false);
    commonPojo.setMsg("网络连接异常，请检查网络状态");
} catch (HttpClientErrorException e) {
    // HTTP客户端错误（4xx）
    LOGGER.error("司卓API客户端错误: {}", e.getMessage());
    commonPojo.setResult(false);
    commonPojo.setMsg("请求参数错误：" + e.getMessage());
} catch (HttpServerErrorException e) {
    // HTTP服务器错误（5xx）
    LOGGER.error("司卓API服务器错误: {}", e.getMessage());
    commonPojo.setResult(false);
    commonPojo.setMsg("服务器内部错误，请稍后重试");
} catch (Exception e) {
    // 其他异常
    LOGGER.error("司卓道闸控制异常", e);
    commonPojo.setResult(false);
    commonPojo.setMsg("系统异常：" + e.getMessage());
}
```

### 8.3 参数验证

#### 输入参数验证

```java
/**
 * 验证道闸控制参数
 */
private boolean validateGateControlParams(String parkingId, int channelIndex, int controlType) {
    // 停车场ID验证
    if (StringUtils.isEmpty(parkingId)) {
        LOGGER.error("停车场ID不能为空");
        return false;
    }

    // 通道索引验证
    if (channelIndex < 1 || channelIndex > 10) {
        LOGGER.error("通道索引超出范围: {}", channelIndex);
        return false;
    }

    // 控制类型验证
    if (controlType < 0 || controlType > 1) {
        LOGGER.error("控制类型无效: {}", controlType);
        return false;
    }

    return true;
}
```

### 8.4 响应结果验证

#### 司卓响应验证

```java
/**
 * 验证司卓API响应
 */
private boolean validateSZResponse(JSONObject response) {
    if (response == null) {
        LOGGER.error("司卓API响应为空");
        return false;
    }

    if (!response.containsKey("resultCode")) {
        LOGGER.error("司卓API响应缺少resultCode字段");
        return false;
    }

    if (!response.containsKey("resultMsg")) {
        LOGGER.error("司卓API响应缺少resultMsg字段");
        return false;
    }

    return true;
}
```

---

## 9. 调试和监控

### 9.1 日志记录

#### 操作日志记录

```java
/**
 * 记录道闸操作日志
 */
private void logGateOperation(String parkingId, int channelIndex, int controlType,
                             String accessAddress, CommonPojo result) {
    try {
        GateLog gateLog = new GateLog();
        gateLog.setParkingId(parkingId);
        gateLog.setChannelIndex(channelIndex);
        gateLog.setControlType(controlType);
        gateLog.setAccessAddress(accessAddress);
        gateLog.setResult(result.isResult() ? 1 : 0);
        gateLog.setErrorMsg(result.getMsg());
        gateLog.setCreateTime(new Date());
        gateLog.setOperator("SYSTEM");

        gateLogMapper.insert(gateLog);
        LOGGER.info("道闸操作日志记录成功: {}", gateLog);

    } catch (Exception e) {
        LOGGER.error("记录道闸操作日志失败", e);
    }
}
```

#### 请求响应日志

```java
// 请求日志
LOGGER.info("司卓道闸控制请求 - URL: {}, 参数: {}", url, data.toJSONString());

// 响应日志
LOGGER.info("司卓道闸控制响应 - 结果: {}", response.toJSONString());

// 异常日志
LOGGER.error("司卓道闸控制异常 - 停车场: {}, 通道: {}, 类型: {}", parkingId, channelIndex, controlType, e);
```

### 9.2 性能监控

#### 接口耗时监控

```java
/**
 * 监控接口执行时间
 */
public CommonPojo gateControlWithMonitoring(String parkingId, int channelIndex, int controlType, String accessAddress) {
    long startTime = System.currentTimeMillis();

    try {
        CommonPojo result = gateControl(parkingId, channelIndex, controlType, accessAddress);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 记录执行时间
        LOGGER.info("司卓道闸控制执行时间: {}ms, 停车场: {}, 结果: {}", duration, parkingId, result.isResult());

        // 如果执行时间过长，记录警告
        if (duration > 5000) {
            LOGGER.warn("司卓道闸控制执行时间过长: {}ms, 停车场: {}", duration, parkingId);
        }

        return result;

    } catch (Exception e) {
        long endTime = System.currentTimeMillis();
        LOGGER.error("司卓道闸控制异常，执行时间: {}ms", endTime - startTime, e);
        throw e;
    }
}
```

### 9.3 健康检查

#### 设备连通性检查

```java
/**
 * 检查司卓设备连通性
 */
public boolean checkSZDeviceHealth(String parkingId) {
    try {
        String url = SZUtil.getUrl(parkingId) + "/health";

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json;charset=UTF-8");
        HttpEntity<String> httpEntity = new HttpEntity<>("{}", headers);

        ResponseEntity<String> response = restTemplate.exchange(
            url, HttpMethod.GET, httpEntity, String.class);

        return response.getStatusCode() == HttpStatus.OK;

    } catch (Exception e) {
        LOGGER.error("司卓设备健康检查失败: {}", parkingId, e);
        return false;
    }
}
```

### 9.4 告警机制

#### 异常告警

```java
/**
 * 发送异常告警
 */
private void sendAlert(String parkingId, String errorMsg) {
    try {
        AlertMessage alert = new AlertMessage();
        alert.setLevel("ERROR");
        alert.setTitle("司卓道闸控制异常");
        alert.setContent(String.format("停车场: %s, 错误信息: %s", parkingId, errorMsg));
        alert.setTimestamp(new Date());

        // 发送告警（可以是邮件、短信、钉钉等）
        alertService.sendAlert(alert);

    } catch (Exception e) {
        LOGGER.error("发送告警失败", e);
    }
}
```

---

## 总结

司卓道闸系统通过精心设计的适配器模式，实现了与司卓厂商设备的无缝对接。系统具有以下特点：

### 主要优势

1. **协议适配完善**：完整实现了司卓 API 协议和签名算法
2. **错误处理健全**：多层次的异常处理和重试机制
3. **日志记录详细**：完整的操作审计和性能监控
4. **参数验证严格**：多重参数验证确保数据安全
5. **扩展性良好**：便于添加新的业务接口和功能

### 核心流程总结

```
前端请求 → 停车管理服务 → 道闸网关服务 → 司卓适配器 → 司卓设备
   ↓           ↓              ↓             ↓           ↓
参数验证    权限验证        路由选择      协议转换     设备控制
   ↓           ↓              ↓             ↓           ↓
结果返回 ← 日志记录 ← 标准化响应 ← 结果处理 ← 状态反馈
```

### 技术要点

- **签名算法**：MD5 加密 + 密钥验证
- **重试机制**：3 次重试 + 指数退避
- **超时控制**：连接超时 2 秒 + 读取超时 2 秒
- **日志审计**：请求/响应/异常全记录
- **健康检查**：定期设备连通性检测

通过本文档的详细说明，开发人员可以深入理解司卓道闸系统的工作原理，进行功能开发、问题排查和系统维护。系统的模块化设计和完善的错误处理机制，确保了在生产环境中的稳定运行。
