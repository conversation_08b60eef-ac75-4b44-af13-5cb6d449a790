<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.gate.mapper.ParkingInfoMapper">
    <sql  id="BaseResultMap">
        select id, parkingId, plateNum, carType, inTime, inChannelId, inChannelName, inPic, outTime, outChannelId, outChannelName, outPic, money, payType, status, lastUpdate
        from gate_parking_info
    </sql>

    <select id="findParkingInfoByPlateNum" resultType="com.lgjy.gate.pojo.ParkingInfoPojo">
    <include refid="BaseResultMap"/>
        where plateNum = #{plateNum} and parkingId = #{parkingId} and status = 0
        order by inTime desc
    </select>

    <insert id="ParkingInfoInsert">
        insert into gate_parking_info(id, parkingId, plateNum, carType, inTime, inChannelId, inChannelName, inPic, outTime, outChannelId, outChannelName, outPic, money, payType, status, lastUpdate)
        values (#{id}, #{parkingId}, #{plateNum}, #{carType}, #{inTime}, #{inChannelId}, #{inChannelName}, #{inPic}, #{outTime}, #{outChannelId}, #{outChannelName}, #{outPic}, #{money}, #{payType}, #{status}, #{lastUpdate})
    </insert>

    <select id="findParkingInfoByPlateNumAndInTime" resultType="com.lgjy.gate.pojo.ParkingInfoPojo">
        <include refid="BaseResultMap"/>
        where plateNum = #{plateNum} and parkingId = #{parkingId}
        and inTime=#{inTime} and status = 0
    </select>

    <select id="findParkingInfo" resultType="com.lgjy.gate.pojo.ParkingInfoPojo">
        <include refid="BaseResultMap"/>
        where parkingId=#{parkingId} and plateNum = #{plateNum}
        and status = 0 order by lastupdate desc
    </select>

    <update id="updateParkingInfo">
        update gate_parking_info
        <set>
            <if test="parkingId != null">
                parkingId = #{parkingId},
            </if>
            <if test="plateNum != null">
                plateNum = #{plateNum},
            </if>
            <if test="carType != null">
                carType = #{carType},
            </if>
            <if test="inTime != null">
                inTime = #{inTime},
            </if>
            <if test="inChannelId != null">
                inChannelId = #{inChannelId},
            </if>
            <if test="inChannelName != null">
                inChannelName = #{inChannelName},
            </if>
            <if test="inPic != null">
                inPic = #{inPic},
            </if>
            <if test="outTime != null">
                outTime = #{outTime},
            </if>
            <if test="outChannelId != null">
                outChannelId = #{outChannelId},
            </if>
            <if test="outChannelName != null">
                outChannelName = #{outChannelName},
            </if>
            <if test="outPic != null">
                outPic = #{outPic},
            </if>
            <if test="money != null">
                money = #{money},
            </if>
            <if test="payType != null">
                payType = #{payType},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="lastUpdate != null">
                lastUpdate = #{lastUpdate},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>