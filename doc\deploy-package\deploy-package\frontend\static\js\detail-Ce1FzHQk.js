import{_ as Y,O as Z,u as ee,a as le,d as te,r as V,C as ae,x as oe,e as i,c as b,o as m,f as e,h as l,k as S,n,t as u,l as r,i as g,a0 as y,j as w,A as ne,B as ue}from"./index-D_FV2sri.js";import{g as se,h as de}from"./exceptionOrder-CWFW8jX7.js";const ie=v=>(ne("data-v-8f6a5025"),v=v(),ue(),v),re={class:"app-container"},pe={class:"card-header"},ce=ie(()=>g("span",null,"异常订单详情",-1)),_e={class:"exception-amount-text"},me={key:0,class:"compensation-amount"},fe={key:1},ve={key:0,class:"operation-buttons"},he={class:"dialog-footer"},be=Z({name:"ExceptionOrderDetail"}),ye=Object.assign(be,{setup(v){const N=ee(),D=le(),{proxy:x}=te(),{exception_order_type:O,exception_order_handle_status:B,priority_level:I,source_type:U,sys_yes_no:C,plate_type:P}=x.useDict("exception_order_type","exception_order_handle_status","priority_level","source_type","sys_yes_no","plate_type"),t=V({}),_=V(!1),k=V(""),o=ae({id:null,handleStatus:null,handleRemark:"",resolutionResult:"",compensationAmount:0}),E={handleStatus:[{required:!0,message:"处理状态不能为空",trigger:"change"}],handleRemark:[{required:!0,message:"处理备注不能为空",trigger:"blur"}]};function A(){const d=N.params.id;se(d).then(s=>{t.value=s.data})}function q(){D.go(-1)}function F(){k.value="处理异常订单",o.id=t.value.id,o.handleStatus=1,o.handleRemark="",o.resolutionResult="",o.compensationAmount=0,_.value=!0}function $(){k.value="忽略异常订单",o.id=t.value.id,o.handleStatus=3,o.handleRemark="",o.resolutionResult="",o.compensationAmount=0,_.value=!0}function j(){x.$refs.handleFormRef.validate(d=>{d&&de(o).then(s=>{x.$modal.msgSuccess("处理成功"),_.value=!1,A()})})}function H(d){return d?d.length===8?"success":"primary":"info"}function L(d){return d?d.length===8?"#d4edda":"#cce7ff":"#909399"}function M(d){return d&&d.length===8?2:1}return oe(()=>{A()}),(d,s)=>{const f=i("el-button"),a=i("el-descriptions-item"),z=i("el-tag"),c=i("dict-tag"),G=i("el-descriptions"),J=i("el-card"),R=i("el-option"),K=i("el-select"),h=i("el-form-item"),T=i("el-input"),Q=i("el-input-number"),W=i("el-form"),X=i("el-dialog");return m(),b("div",re,[e(J,{class:"box-card"},{header:l(()=>[g("div",pe,[ce,e(f,{style:{float:"right",padding:"3px 0"},type:"text",onClick:q},{default:l(()=>[n("返回")]),_:1})])]),default:l(()=>[e(G,{column:2,border:""},{default:l(()=>[e(a,{label:"异常订单编号"},{default:l(()=>[n(u(t.value.orderId),1)]),_:1}),e(a,{label:"异常记录ID"},{default:l(()=>[n(u(t.value.id),1)]),_:1}),e(a,{label:"车牌号"},{default:l(()=>[e(z,{type:H(t.value.plateNo),color:L(t.value.plateNo),effect:"plain"},{default:l(()=>[n(u(t.value.plateNo),1)]),_:1},8,["type","color"])]),_:1}),e(a,{label:"车牌类型"},{default:l(()=>[e(c,{options:r(P),value:M(t.value.plateNo)},null,8,["options","value"])]),_:1}),e(a,{label:"场库名称"},{default:l(()=>[n(u(t.value.warehouseName),1)]),_:1}),e(a,{label:"异常类型"},{default:l(()=>[e(c,{options:r(O),value:t.value.exceptionType},null,8,["options","value"])]),_:1}),e(a,{label:"处理状态"},{default:l(()=>[e(c,{options:r(B),value:t.value.handleStatus},null,8,["options","value"])]),_:1}),e(a,{label:"异常金额"},{default:l(()=>[g("span",_e,"¥"+u(t.value.exceptionAmount||"0.00"),1)]),_:1}),e(a,{label:"原始金额"},{default:l(()=>[n(" ¥"+u(t.value.originalAmount||"0.00"),1)]),_:1}),e(a,{label:"实际金额"},{default:l(()=>[n(" ¥"+u(t.value.actualAmount||"0.00"),1)]),_:1}),e(a,{label:"优先级"},{default:l(()=>[e(c,{options:r(I),value:t.value.priorityLevel},null,8,["options","value"])]),_:1}),e(a,{label:"来源类型"},{default:l(()=>[e(c,{options:r(U),value:t.value.sourceType},null,8,["options","value"])]),_:1}),e(a,{label:"开始停车时间"},{default:l(()=>[n(u(r(y)(t.value.beginParkingTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(a,{label:"结束停车时间"},{default:l(()=>[n(u(r(y)(t.value.endParkingTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(a,{label:"停车时长"},{default:l(()=>[n(u(t.value.parkingDuration)+"分钟 ",1)]),_:1}),e(a,{label:"异常发生时间"},{default:l(()=>[n(u(r(y)(t.value.exceptionTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(a,{label:"联系人"},{default:l(()=>[n(u(t.value.contactUser||"-"),1)]),_:1}),e(a,{label:"联系电话"},{default:l(()=>[n(u(t.value.contactPhone||"-"),1)]),_:1}),e(a,{label:"处理人"},{default:l(()=>[n(u(t.value.handleByName||"-"),1)]),_:1}),e(a,{label:"处理时间"},{default:l(()=>[n(u(r(y)(t.value.handleTime,"{y}-{m}-{d} {h}:{i}:{s}")||"-"),1)]),_:1}),e(a,{label:"补偿金额"},{default:l(()=>[t.value.compensationAmount>0?(m(),b("span",me," ¥"+u(t.value.compensationAmount),1)):(m(),b("span",fe,"-"))]),_:1}),e(a,{label:"是否已补偿"},{default:l(()=>[e(c,{options:r(C),value:t.value.isCompensated},null,8,["options","value"])]),_:1}),e(a,{label:"是否需要跟进"},{default:l(()=>[e(c,{options:r(C),value:t.value.followUpRequired},null,8,["options","value"])]),_:1}),e(a,{label:"异常描述",span:2},{default:l(()=>[n(u(t.value.exceptionDesc),1)]),_:1}),e(a,{label:"处理备注",span:2},{default:l(()=>[n(u(t.value.handleRemark||"-"),1)]),_:1}),e(a,{label:"处理结果",span:2},{default:l(()=>[n(u(t.value.resolutionResult||"-"),1)]),_:1})]),_:1}),t.value.handleStatus!==2&&t.value.handleStatus!==3?(m(),b("div",ve,[e(f,{type:"primary",onClick:F},{default:l(()=>[n("处理订单")]),_:1}),e(f,{type:"warning",onClick:$},{default:l(()=>[n("忽略订单")]),_:1})])):S("",!0)]),_:1}),e(X,{title:k.value,modelValue:_.value,"onUpdate:modelValue":s[5]||(s[5]=p=>_.value=p),width:"600px","append-to-body":""},{footer:l(()=>[g("div",he,[e(f,{type:"primary",onClick:j},{default:l(()=>[n("确 定")]),_:1}),e(f,{onClick:s[4]||(s[4]=p=>_.value=!1)},{default:l(()=>[n("取 消")]),_:1})])]),default:l(()=>[e(W,{ref:"handleFormRef",model:o,rules:E,"label-width":"100px"},{default:l(()=>[e(h,{label:"处理状态",prop:"handleStatus"},{default:l(()=>[e(K,{modelValue:o.handleStatus,"onUpdate:modelValue":s[0]||(s[0]=p=>o.handleStatus=p),placeholder:"请选择处理状态"},{default:l(()=>[e(R,{label:"处理中",value:1}),e(R,{label:"已处理",value:2}),e(R,{label:"已忽略",value:3})]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"处理备注",prop:"handleRemark"},{default:l(()=>[e(T,{modelValue:o.handleRemark,"onUpdate:modelValue":s[1]||(s[1]=p=>o.handleRemark=p),type:"textarea",rows:4,placeholder:"请输入处理备注"},null,8,["modelValue"])]),_:1}),o.handleStatus===2?(m(),w(h,{key:0,label:"处理结果",prop:"resolutionResult"},{default:l(()=>[e(T,{modelValue:o.resolutionResult,"onUpdate:modelValue":s[2]||(s[2]=p=>o.resolutionResult=p),type:"textarea",rows:3,placeholder:"请输入处理结果"},null,8,["modelValue"])]),_:1})):S("",!0),o.handleStatus===2?(m(),w(h,{key:1,label:"补偿金额",prop:"compensationAmount"},{default:l(()=>[e(Q,{modelValue:o.compensationAmount,"onUpdate:modelValue":s[3]||(s[3]=p=>o.compensationAmount=p),precision:2,min:0,placeholder:"补偿金额"},null,8,["modelValue"])]),_:1})):S("",!0)]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}}),ke=Y(ye,[["__scopeId","data-v-8f6a5025"]]);export{ke as default};
