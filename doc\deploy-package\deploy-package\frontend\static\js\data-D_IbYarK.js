import{O as Ce,d as Ve,r as c,u as we,C as he,T as ke,e as i,Q as W,c as w,o as u,R as g,f as l,S as Te,l as a,h as t,L as S,M as D,j as m,m as Se,n as r,P as X,t as L,U as De,i as Y,V as Le,W as Ue,X as z,Y as xe,Z as Pe,$ as Re}from"./index-D_FV2sri.js";import{g as $e,o as Ne}from"./type-BxuMtkYW.js";import{C as Z}from"./index-C9zsUI-h.js";const qe={class:"app-container"},ze={key:0},Oe={class:"dialog-footer"},Be=Ce({name:"Data"}),Ie=Object.assign(Be,{components:{CustomPagination:Z}},{setup(Ee){const{proxy:_}=Ve(),{sys_normal_disable:U}=_.useDict("sys_normal_disable"),O=c([]),b=c(!1),x=c(!0),T=c(!0),P=c([]),B=c(!0),E=c(!0),F=c(0),R=c(""),Q=c(""),j=c([]),I=we(),G=c([{value:"default",label:"默认"},{value:"primary",label:"主要"},{value:"success",label:"成功"},{value:"info",label:"信息"},{value:"warning",label:"警告"},{value:"danger",label:"危险"}]),H=he({form:{},queryParams:{pageNum:1,pageSize:10,dictType:void 0,dictLabel:void 0,status:void 0},rules:{dictLabel:[{required:!0,message:"数据标签不能为空",trigger:"blur"}],dictValue:[{required:!0,message:"数据键值不能为空",trigger:"blur"}],dictSort:[{required:!0,message:"数据顺序不能为空",trigger:"blur"}]}}),{queryParams:s,form:n,rules:J}=ke(H);function ee(d){$e(d).then(o=>{s.value.dictType=o.data.dictType,Q.value=o.data.dictType,y()})}function le(){Ne().then(d=>{j.value=d.data})}function y(){x.value=!0,xe(s.value).then(d=>{O.value=d.rows,F.value=d.total,x.value=!1})}function te(){b.value=!1,$()}function $(){n.value={dictCode:void 0,dictLabel:void 0,dictValue:void 0,cssClass:void 0,listClass:"default",dictSort:0,status:"0",remark:void 0},_.resetForm("dataRef")}function N(){s.value.pageNum=1,y()}function ae(){const d={path:"/system/dict"};_.$tab.closeOpenPage(d)}function oe(){_.resetForm("queryRef"),s.value.dictType=Q.value,N()}function ne(){$(),b.value=!0,R.value="添加字典数据",n.value.dictType=s.value.dictType}function se(d){P.value=d.map(o=>o.dictCode),B.value=d.length!=1,E.value=!d.length}function K(d){$();const o=d.dictCode||P.value;Le(o).then(h=>{n.value=h.data,b.value=!0,R.value="修改字典数据"})}function de(){_.$refs.dataRef.validate(d=>{d&&(n.value.dictCode!=null?Pe(n.value).then(o=>{z().removeDict(s.value.dictType),_.$modal.msgSuccess("修改成功"),b.value=!1,y()}):Re(n.value).then(o=>{z().removeDict(s.value.dictType),_.$modal.msgSuccess("新增成功"),b.value=!1,y()}))})}function A(d){const o=d.dictCode||P.value;_.$modal.confirm('是否确认删除字典编码为"'+o+'"的数据项？').then(function(){return Ue(o)}).then(()=>{y(),_.$modal.msgSuccess("删除成功"),z().removeDict(s.value.dictType)}).catch(()=>{})}function ue(){_.download("system/dict/data/export",{...s.value},`dict_data_${new Date().getTime()}.xlsx`)}return ee(I.params&&I.params.dictId),le(),(d,o)=>{const h=i("el-option"),q=i("el-select"),p=i("el-form-item"),C=i("el-input"),f=i("el-button"),M=i("el-form"),k=i("el-col"),ie=i("right-toolbar"),re=i("el-row"),v=i("el-table-column"),pe=i("el-tag"),ce=i("dict-tag"),me=i("el-table"),fe=i("el-input-number"),_e=i("el-radio"),ve=i("el-radio-group"),be=i("el-dialog"),V=W("hasPermi"),ge=W("loading");return u(),w("div",qe,[g(l(M,{model:a(s),ref:"queryRef",inline:!0},{default:t(()=>[l(p,{label:"字典名称",prop:"dictType"},{default:t(()=>[l(q,{modelValue:a(s).dictType,"onUpdate:modelValue":o[0]||(o[0]=e=>a(s).dictType=e),style:{width:"200px"}},{default:t(()=>[(u(!0),w(S,null,D(a(j),e=>(u(),m(h,{key:e.dictId,label:e.dictName,value:e.dictType},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"字典标签",prop:"dictLabel"},{default:t(()=>[l(C,{modelValue:a(s).dictLabel,"onUpdate:modelValue":o[1]||(o[1]=e=>a(s).dictLabel=e),placeholder:"请输入字典标签",clearable:"",style:{width:"200px"},onKeyup:Se(N,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"状态",prop:"status"},{default:t(()=>[l(q,{modelValue:a(s).status,"onUpdate:modelValue":o[2]||(o[2]=e=>a(s).status=e),placeholder:"数据状态",clearable:"",style:{width:"200px"}},{default:t(()=>[(u(!0),w(S,null,D(a(U),e=>(u(),m(h,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,null,{default:t(()=>[l(f,{type:"primary",icon:"Search",onClick:N},{default:t(()=>[r("搜索")]),_:1}),l(f,{icon:"Refresh",onClick:oe},{default:t(()=>[r("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Te,a(T)]]),l(re,{gutter:10,class:"mb8"},{default:t(()=>[l(k,{span:1.5},{default:t(()=>[g((u(),m(f,{type:"primary",plain:"",icon:"Plus",onClick:ne},{default:t(()=>[r("新增")]),_:1})),[[V,["system:dict:add"]]])]),_:1}),l(k,{span:1.5},{default:t(()=>[g((u(),m(f,{type:"success",plain:"",icon:"Edit",disabled:a(B),onClick:K},{default:t(()=>[r("修改")]),_:1},8,["disabled"])),[[V,["system:dict:edit"]]])]),_:1}),l(k,{span:1.5},{default:t(()=>[g((u(),m(f,{type:"danger",plain:"",icon:"Delete",disabled:a(E),onClick:A},{default:t(()=>[r("删除")]),_:1},8,["disabled"])),[[V,["system:dict:remove"]]])]),_:1}),l(k,{span:1.5},{default:t(()=>[g((u(),m(f,{type:"warning",plain:"",icon:"Download",onClick:ue},{default:t(()=>[r("导出")]),_:1})),[[V,["system:dict:export"]]])]),_:1}),l(k,{span:1.5},{default:t(()=>[l(f,{type:"warning",plain:"",icon:"Close",onClick:ae},{default:t(()=>[r("关闭")]),_:1})]),_:1}),l(ie,{showSearch:a(T),"onUpdate:showSearch":o[3]||(o[3]=e=>X(T)?T.value=e:null),onQueryTable:y},null,8,["showSearch"])]),_:1}),g((u(),m(me,{data:a(O),onSelectionChange:se},{default:t(()=>[l(v,{type:"selection",width:"55",align:"center"}),l(v,{label:"字典编码",align:"center",prop:"dictCode"}),l(v,{label:"字典标签",align:"center",prop:"dictLabel"},{default:t(e=>[(e.row.listClass==""||e.row.listClass=="default")&&(e.row.cssClass==""||e.row.cssClass==null)?(u(),w("span",ze,L(e.row.dictLabel),1)):(u(),m(pe,{key:1,type:e.row.listClass=="primary"?"primary":e.row.listClass||"primary",class:De(e.row.cssClass)},{default:t(()=>[r(L(e.row.dictLabel),1)]),_:2},1032,["type","class"]))]),_:1}),l(v,{label:"字典键值",align:"center",prop:"dictValue"}),l(v,{label:"字典排序",align:"center",prop:"dictSort"}),l(v,{label:"状态",align:"center",prop:"status"},{default:t(e=>[l(ce,{options:a(U),value:e.row.status},null,8,["options","value"])]),_:1}),l(v,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),l(v,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(e=>[Y("span",null,L(d.parseTime(e.row.createTime)),1)]),_:1}),l(v,{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},{default:t(e=>[g((u(),m(f,{link:"",type:"primary",icon:"Edit",onClick:ye=>K(e.row)},{default:t(()=>[r("修改")]),_:2},1032,["onClick"])),[[V,["system:dict:edit"]]]),g((u(),m(f,{link:"",type:"primary",icon:"Delete",onClick:ye=>A(e.row)},{default:t(()=>[r("删除")]),_:2},1032,["onClick"])),[[V,["system:dict:remove"]]])]),_:1})]),_:1},8,["data"])),[[ge,a(x)]]),l(Z,{total:a(F),"current-page":a(s).pageNum,"onUpdate:currentPage":o[4]||(o[4]=e=>a(s).pageNum=e),"page-size":a(s).pageSize,"onUpdate:pageSize":o[5]||(o[5]=e=>a(s).pageSize=e),onPagination:y},null,8,["total","current-page","page-size"]),l(be,{title:a(R),modelValue:a(b),"onUpdate:modelValue":o[14]||(o[14]=e=>X(b)?b.value=e:null),width:"500px","append-to-body":""},{footer:t(()=>[Y("div",Oe,[l(f,{type:"primary",onClick:de},{default:t(()=>[r("确 定")]),_:1}),l(f,{onClick:te},{default:t(()=>[r("取 消")]),_:1})])]),default:t(()=>[l(M,{ref:"dataRef",model:a(n),rules:a(J),"label-width":"80px"},{default:t(()=>[l(p,{label:"字典类型"},{default:t(()=>[l(C,{modelValue:a(n).dictType,"onUpdate:modelValue":o[6]||(o[6]=e=>a(n).dictType=e),disabled:!0},null,8,["modelValue"])]),_:1}),l(p,{label:"数据标签",prop:"dictLabel"},{default:t(()=>[l(C,{modelValue:a(n).dictLabel,"onUpdate:modelValue":o[7]||(o[7]=e=>a(n).dictLabel=e),placeholder:"请输入数据标签"},null,8,["modelValue"])]),_:1}),l(p,{label:"数据键值",prop:"dictValue"},{default:t(()=>[l(C,{modelValue:a(n).dictValue,"onUpdate:modelValue":o[8]||(o[8]=e=>a(n).dictValue=e),placeholder:"请输入数据键值"},null,8,["modelValue"])]),_:1}),l(p,{label:"样式属性",prop:"cssClass"},{default:t(()=>[l(C,{modelValue:a(n).cssClass,"onUpdate:modelValue":o[9]||(o[9]=e=>a(n).cssClass=e),placeholder:"请输入样式属性"},null,8,["modelValue"])]),_:1}),l(p,{label:"显示排序",prop:"dictSort"},{default:t(()=>[l(fe,{modelValue:a(n).dictSort,"onUpdate:modelValue":o[10]||(o[10]=e=>a(n).dictSort=e),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),l(p,{label:"回显样式",prop:"listClass"},{default:t(()=>[l(q,{modelValue:a(n).listClass,"onUpdate:modelValue":o[11]||(o[11]=e=>a(n).listClass=e)},{default:t(()=>[(u(!0),w(S,null,D(a(G),e=>(u(),m(h,{key:e.value,label:e.label+"("+e.value+")",value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"状态",prop:"status"},{default:t(()=>[l(ve,{modelValue:a(n).status,"onUpdate:modelValue":o[12]||(o[12]=e=>a(n).status=e)},{default:t(()=>[(u(!0),w(S,null,D(a(U),e=>(u(),m(_e,{key:e.value,value:e.value},{default:t(()=>[r(L(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"备注",prop:"remark"},{default:t(()=>[l(C,{modelValue:a(n).remark,"onUpdate:modelValue":o[13]||(o[13]=e=>a(n).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ie as default};
