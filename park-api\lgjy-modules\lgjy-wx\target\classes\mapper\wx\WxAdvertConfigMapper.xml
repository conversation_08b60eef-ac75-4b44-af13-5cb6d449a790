<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxAdvertConifgMapper">
    <sql id="selectMiniAdvertConfigVo">
        select id, remark, advert_title, url, pic_url, status, delete_flag, create_by, create_time, update_by, update_time from mini_advert_config
    </sql>

    <select id="selectWxAdvertConfigList" resultType="com.lgjy.wx.domain.WxAdvertConfig">
        <include refid="selectMiniAdvertConfigVo"/>
        where delete_flag = 0 and status = 1
    </select>

</mapper>