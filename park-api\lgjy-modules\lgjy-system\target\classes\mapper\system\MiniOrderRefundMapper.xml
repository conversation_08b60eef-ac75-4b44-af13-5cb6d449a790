<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniOrderRefundMapper">
    
    <resultMap type="MiniOrderRefund" id="MiniOrderRefundResult">
        <result property="id"    column="id"    />
        <result property="tradeId"    column="trade_id"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="refundType"    column="refund_type"    />
        <result property="originalAmount"    column="original_amount"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMiniOrderRefundVo">
        select id, trade_id, refund_reason, refund_type, original_amount, refund_amount, create_by, create_time, update_by, update_time from mini_order_refund
    </sql>

    <select id="selectMiniOrderRefundList" parameterType="MiniOrderRefund" resultMap="MiniOrderRefundResult">
        <include refid="selectMiniOrderRefundVo"/>
        <where>  
            <if test="tradeId != null  and tradeId != ''"> and trade_id = #{tradeId}</if>
            <if test="refundReason != null  and refundReason != ''"> and refund_reason like concat('%', #{refundReason}, '%')</if>
            <if test="refundType != null "> and refund_type = #{refundType}</if>
            <if test="originalAmount != null "> and original_amount = #{originalAmount}</if>
            <if test="refundAmount != null "> and refund_amount = #{refundAmount}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMiniOrderRefundById" parameterType="Long" resultMap="MiniOrderRefundResult">
        <include refid="selectMiniOrderRefundVo"/>
        where id = #{id}
    </select>

    <select id="selectMiniOrderRefundByTradeId" parameterType="String" resultMap="MiniOrderRefundResult">
        <include refid="selectMiniOrderRefundVo"/>
        where trade_id = #{tradeId}
        order by create_time desc
    </select>
        
    <insert id="insertMiniOrderRefund" parameterType="MiniOrderRefund" useGeneratedKeys="true" keyProperty="id">
        insert into mini_order_refund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tradeId != null and tradeId != ''">trade_id,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="refundType != null">refund_type,</if>
            <if test="originalAmount != null">original_amount,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tradeId != null and tradeId != ''">#{tradeId},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="refundType != null">#{refundType},</if>
            <if test="originalAmount != null">#{originalAmount},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMiniOrderRefund" parameterType="MiniOrderRefund">
        update mini_order_refund
        <trim prefix="SET" suffixOverrides=",">
            <if test="tradeId != null and tradeId != ''">trade_id = #{tradeId},</if>
            <if test="refundReason != null and refundReason != ''">refund_reason = #{refundReason},</if>
            <if test="refundType != null">refund_type = #{refundType},</if>
            <if test="originalAmount != null">original_amount = #{originalAmount},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniOrderRefundById" parameterType="Long">
        delete from mini_order_refund where id = #{id}
    </delete>

    <delete id="deleteMiniOrderRefundByIds" parameterType="String">
        delete from mini_order_refund where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
