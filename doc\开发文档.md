# 停车场管理系统开发文档

## 目录

- [1. 项目架构概览](#1-项目架构概览)
- [2. 前端开发指南](#2-前端开发指南)
- [3. 后端开发指南](#3-后端开发指南)
- [4. 数据库设计文档](#4-数据库设计文档)
- [5. 菜单系统文档](#5-菜单系统文档)


---

## 1. 项目架构概览

### 1.1 技术栈

**后端技术栈：**

- Spring Boot 2.x
- Spring Cloud & Alibaba
- Nacos (注册中心、配置中心)
- Redis (缓存、权限认证)
- Sentinel (流量控制)
- Seata (分布式事务)
- MyBatis (数据访问层)
- MySQL (数据库)

**前端技术栈：**

- Vue 3.x
- Element Plus
- Vite (构建工具)
- Pinia (状态管理)
- Vue Router (路由管理)

### 1.2 项目目录结构

```
parking/
├── park-api/                    # 后端项目
│   ├── lgjy-auth/              # 认证中心 [9200]
│   ├── lgjy-gateway/           # 网关模块 [8080]
│   ├── lgjy-api/               # 接口模块
│   │   └── lgjy-api-system/    # 系统接口
│   ├── lgjy-common/            # 通用模块
│   │   ├── lgjy-common-core/   # 核心模块
│   │   ├── lgjy-common-security/ # 安全模块
│   │   └── lgjy-common-redis/  # 缓存服务
│   ├── lgjy-modules/           # 业务模块
│   │   └── lgjy-system/        # 系统模块 [9201]
│   └── sql/                    # 数据库脚本
└── park-ui/                    # 前端项目
    ├── src/
    │   ├── api/                # API接口
    │   ├── components/         # 公共组件
    │   ├── views/              # 页面组件
    │   ├── router/             # 路由配置
    │   ├── store/              # 状态管理
    │   └── utils/              # 工具类
    └── public/                 # 静态资源
```

### 1.3 微服务架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │    │   网关服务       │    │   认证中心       │
│   park-ui       │───▶│  lgjy-gateway   │───▶│   lgjy-auth     │
│   [80]          │    │   [8080]        │    │   [9200]        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   注册中心       │
                       │     Nacos       │
                       └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                ▼               ▼               ▼
    ┌─────────────────┐ ┌─────────────────┐
    │   系统模块       │ │   其他模块       │
    │  lgjy-system    │ │      ...        │
    │   [9201]        │ │                 │
    └─────────────────┘ └─────────────────┘
```

---

## 2. 前端开发指南

### 2.1 项目结构

```
src/
├── api/                    # API接口定义
│   ├── system/            # 系统相关API
│   └── tool/              # 工具相关API
├── assets/                # 静态资源
├── components/            # 公共组件
├── directive/             # 自定义指令
│   └── permission/        # 权限指令
├── layout/                # 布局组件
├── plugins/               # 插件
├── router/                # 路由配置
├── store/                 # 状态管理
│   └── modules/           # 模块化store
├── utils/                 # 工具函数
├── views/                 # 页面组件
└── permission.js          # 权限控制
```

### 2.2 路由配置

#### 2.2.1 静态路由

```javascript
// src/router/index.js
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "/index",
    children: [
      {
        path: "/index",
        component: () => import("@/views/index"),
        name: "Index",
        meta: { title: "首页", icon: "dashboard", affix: true },
      },
    ],
  },
];
```

#### 2.2.2 动态路由

```javascript
// src/router/index.js
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
];
```

### 2.3 状态管理

#### 2.3.1 用户状态管理

```javascript
// src/store/modules/user.js
const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    roles: [],
    permissions: [],
  }),
  actions: {
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo().then((res) => {
          const user = res.user;
          if (res.roles && res.roles.length > 0) {
            this.roles = res.roles;
            this.permissions = res.permissions;
          }
          resolve(res);
        });
      });
    },
  },
});
```

### 2.4 权限控制

#### 2.4.1 路由权限

```javascript
// src/permission.js
router.beforeEach((to, from, next) => {
  if (getToken()) {
    if (useUserStore().roles.length === 0) {
      useUserStore()
        .getInfo()
        .then(() => {
          usePermissionStore()
            .generateRoutes()
            .then((accessRoutes) => {
              accessRoutes.forEach((route) => {
                router.addRoute(route);
              });
              next({ ...to, replace: true });
            });
        });
    } else {
      next();
    }
  } else {
    next("/login");
  }
});
```

#### 2.4.2 按钮权限

```javascript
// 指令方式
<el-button v-hasPermi="['system:user:add']">新增</el-button>;

// 函数方式
import { checkPermi } from "@/utils/permission";
if (checkPermi(["system:user:edit"])) {
  // 有权限执行的操作
}
```

### 2.5 API 接口规范

```javascript
// src/api/system/user.js
import request from "@/utils/request";

// 查询用户列表
export function listUser(query) {
  return request({
    url: "/system/user/list",
    method: "get",
    params: query,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: "/system/user/" + userId,
    method: "get",
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: "/system/user",
    method: "post",
    data: data,
  });
}
```

---

## 3. 后端开发指南

### 3.1 项目结构

```
lgjy-modules/lgjy-system/
├── src/main/java/com/lgjy/system/
│   ├── controller/         # 控制层
│   ├── service/           # 业务层
│   │   └── impl/          # 业务实现层
│   ├── mapper/            # 数据访问层
│   ├── domain/            # 实体类
│   └── config/            # 配置类
└── src/main/resources/
    ├── mapper/            # MyBatis映射文件
    └── application.yml    # 配置文件
```

### 3.2 分层架构

#### 3.2.1 Controller 层

```java
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {

    @Autowired
    private ISysUserService userService;

    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        return toAjax(userService.insertUser(user));
    }
}
```

#### 3.2.2 Service 层

```java
@Service
public class SysUserServiceImpl implements ISysUserService {

    @Autowired
    private SysUserMapper userMapper;

    @Override
    public List<SysUser> selectUserList(SysUser user) {
        return userMapper.selectUserList(user);
    }

    @Override
    public int insertUser(SysUser user) {
        return userMapper.insertUser(user);
    }
}
```

#### 3.2.3 Mapper 层

```java
public interface SysUserMapper {

    public List<SysUser> selectUserList(SysUser user);

    public int insertUser(SysUser user);
}
```

### 3.3 权限验证

#### 3.3.1 注解权限

```java
// 验证用户是否具备某权限
@PreAuthorize("@ss.hasPermi('system:user:view')")

// 验证用户是否不具备某权限，与hasPermi逻辑相反
@PreAuthorize("@ss.lacksPermi('system:user:view')")

// 验证用户是否具有以下任意一个权限
@PreAuthorize("@ss.hasAnyPermi('system:user:view,system:user:edit')")

// 判断用户是否拥有某个角色
@PreAuthorize("@ss.hasRole('admin')")
```

---

## 4. 数据库设计文档

### 4.1 核心表结构

#### 4.1.1 系统用户表 (sys_user)

```sql
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) COMMENT='用户信息表';
```

#### 4.1.2 系统菜单表 (sys_menu)

```sql
CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) DEFAULT '' COMMENT '路由名称',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) COMMENT='菜单权限表';
```



### 4.2 表关系图

```
sys_user (用户表)
    ├── sys_user_role (用户角色关联表)
    │   └── sys_role (角色表)
    │       └── sys_role_menu (角色菜单关联表)
    │           └── sys_menu (菜单表)
    └── sys_user_post (用户岗位关联表)
        └── sys_post (岗位表)

sys_dept (部门表) ←── sys_user (用户表)
```

---

## 5. 菜单系统文档

### 5.1 菜单类型

- **M (目录)**: 菜单目录，用于组织菜单结构
- **C (菜单)**: 具体的菜单项，对应前端路由
- **F (按钮)**: 功能按钮，用于页面内的权限控制

### 5.2 菜单配置

#### 5.2.1 目录配置示例

```json
{
  "menuId": 1,
  "menuName": "系统管理",
  "parentId": 0,
  "orderNum": 1,
  "path": "system",
  "component": null,
  "menuType": "M",
  "visible": "0",
  "status": "0",
  "perms": "",
  "icon": "system"
}
```

#### 5.2.2 菜单配置示例

```json
{
  "menuId": 100,
  "menuName": "用户管理",
  "parentId": 1,
  "orderNum": 1,
  "path": "user",
  "component": "system/user/index",
  "menuType": "C",
  "visible": "0",
  "status": "0",
  "perms": "system:user:list",
  "icon": "user"
}
```

#### 5.2.3 按钮配置示例

```json
{
  "menuId": 1001,
  "menuName": "用户查询",
  "parentId": 100,
  "orderNum": 1,
  "path": "",
  "component": "",
  "menuType": "F",
  "visible": "0",
  "status": "0",
  "perms": "system:user:query",
  "icon": "#"
}
```

### 5.3 动态菜单生成

#### 5.3.1 后端菜单构建

```java
@Override
public List<RouterVo> buildMenus(List<SysMenu> menus) {
    List<RouterVo> routers = new LinkedList<RouterVo>();
    for (SysMenu menu : menus) {
        RouterVo router = new RouterVo();
        router.setHidden("1".equals(menu.getVisible()));
        router.setName(getRouteName(menu));
        router.setPath(getRouterPath(menu));
        router.setComponent(getComponent(menu));
        router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(),
                      StringUtils.equals("1", menu.getIsCache()), menu.getPath()));

        List<SysMenu> cMenus = menu.getChildren();
        if (StringUtils.isNotEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
            router.setAlwaysShow(true);
            router.setRedirect("noRedirect");
            router.setChildren(buildMenus(cMenus));
        }
        routers.add(router);
    }
    return routers;
}
```

#### 5.3.2 前端路由生成

```javascript
// src/store/modules/permission.js
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    if (route.component) {
      if (route.component === "Layout") {
        route.component = Layout;
      } else if (route.component === "ParentView") {
        route.component = ParentView;
      } else {
        route.component = loadView(route.component);
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    }
    return true;
  });
}
```


---

## 6. 开发规范和最佳实践

### 6.1 命名规范

#### 6.1.1 Java 命名规范

- **类名**: 使用 PascalCase，如 `SysUserController`
- **方法名**: 使用 camelCase，如 `selectUserList`
- **变量名**: 使用 camelCase，如 `userName`
- **常量名**: 使用 UPPER_SNAKE_CASE，如 `DEFAULT_PAGE_SIZE`

#### 6.1.2 前端命名规范

- **组件名**: 使用 PascalCase，如 `UserManagement`
- **文件名**: 使用 kebab-case，如 `user-management.vue`
- **方法名**: 使用 camelCase，如 `handleQuery`
- **变量名**: 使用 camelCase，如 `userList`

### 6.2 代码注释规范

#### 6.2.1 Java 注释

```java
/**
 * 查询用户列表
 *
 * @param user 用户信息
 * @return 用户集合
 */
public List<SysUser> selectUserList(SysUser user);
```

#### 7.2.2 前端注释

```javascript
/**
 * 查询用户列表
 */
function getList() {
  loading.value = true;
  listUser(queryParams.value).then((response) => {
    userList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
```

### 7.3 错误处理

#### 7.3.1 后端异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e) {
        return AjaxResult.error(e.getMessage());
    }
}
```

#### 7.3.2 前端错误处理

```javascript
// 统一错误处理
request.interceptors.response.use(
  (response) => {
    const code = response.data.code || 200;
    if (code === 401) {
      // 未授权处理
    } else if (code === 500) {
      // 服务器错误处理
    }
    return response.data;
  },
  (error) => {
    console.error("请求错误：", error);
    return Promise.reject(error);
  }
);
```

---

## 7. 部署指南

### 7.1 开发环境部署

#### 7.1.1 环境要求

- JDK 1.8+
- MySQL 5.7+
- Redis 3.0+
- Node.js 14+
- Nacos 2.0+

#### 7.1.2 后端启动

```bash
# 1. 启动 Nacos
sh startup.sh -m standalone

# 2. 启动 Redis
redis-server

# 3. 导入数据库脚本
mysql -u root -p < sql/parkingnew.sql

# 4. 启动各个服务
java -jar lgjy-gateway.jar
java -jar lgjy-auth.jar
java -jar lgjy-system.jar
```

#### 8.1.3 前端启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 8.2 生产环境部署

#### 8.2.1 Docker 部署

```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine
COPY target/app.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 8.2.2 Nginx 配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location /prod-api/ {
        proxy_pass http://gateway:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 9. 常见问题解答

### 9.1 开发问题

**Q: 权限验证不生效？**
A: 检查以下几点：

1. 确认用户已分配相应角色
2. 检查角色是否分配了相应菜单权限
3. 确认权限标识是否正确配置

### 9.2 部署问题

**Q: 服务启动失败？**
A: 检查以下几点：

1. 确认 Nacos 服务是否正常启动
2. 检查数据库连接配置是否正确
3. 确认端口是否被占用

**Q: 前端页面空白？**
A: 检查以下几点：

1. 确认后端服务是否正常启动
2. 检查网络请求是否正常
3. 查看浏览器控制台错误信息

---

## 10. 附录

### 10.1 相关链接

- [Vue 3 官方文档](https://v3.cn.vuejs.org/)
- [Element Plus 官方文档](https://element-plus.org/zh-CN/)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [Spring Cloud Alibaba 官方文档](https://spring-cloud-alibaba-group.github.io/github-pages/hoxton/zh-cn/index.html)

### 10.2 技术支持

如有技术问题，请联系开发团队或查阅相关技术文档。

---

_文档版本：v1.0_
_最后更新：2024 年 1 月_

```

```
