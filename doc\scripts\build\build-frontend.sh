#!/bin/bash

# 停车管理系统前端构建脚本
# 作者: DevOps Team
# 版本: 1.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 解析命令行参数
BUILD_ENV="test"
BUILD_DOCKER="true"
CLEAN_BUILD="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            BUILD_ENV="${1#*=}"
            shift
            ;;
        --no-docker)
            BUILD_DOCKER="false"
            shift
            ;;
        --clean)
            CLEAN_BUILD="true"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV            构建环境 (test|prod) 默认: test"
            echo "  --no-docker          不构建Docker镜像"
            echo "  --clean              清理构建"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查Node.js版本
check_node_version() {
    if ! command -v node &> /dev/null; then
        log "ERROR" "Node.js未安装"
        return 1
    fi
    
    local node_version=$(node --version | sed 's/v//')
    local required_version="$NODE_VERSION"
    
    log "INFO" "Node.js版本: $node_version (要求: $required_version+)"
    
    # 简单版本比较
    if [[ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" != "$required_version" ]]; then
        log "WARN" "Node.js版本可能过低，建议升级到 $required_version+"
    fi
}

# 构建管理后台
build_admin_ui() {
    log "INFO" "构建管理后台..."
    
    if [ ! -d "$FRONTEND_DIR" ]; then
        log "ERROR" "前端目录不存在: $FRONTEND_DIR"
        return 1
    fi
    
    cd "$FRONTEND_DIR"
    
    # 清理构建
    if [ "$CLEAN_BUILD" = "true" ]; then
        log "INFO" "清理前端构建..."
        rm -rf node_modules dist
    fi
    
    # 安装依赖
    log "INFO" "安装前端依赖..."
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    # 构建项目
    log "INFO" "构建前端项目 (环境: $BUILD_ENV)..."
    
    local build_script="build"
    if [ "$BUILD_ENV" = "prod" ]; then
        build_script="build:prod"
    else
        build_script="build:stage"  # 测试环境
    fi
    
    if ! npm run "$build_script"; then
        log "ERROR" "前端构建失败"
        return 1
    fi
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        log "ERROR" "构建产物不存在: dist目录"
        return 1
    fi
    
    log "INFO" "管理后台构建完成"
    return 0
}

# 构建小程序
build_uniapp() {
    log "INFO" "构建小程序..."
    
    if [ ! -d "$UNIAPP_DIR" ]; then
        log "WARN" "小程序目录不存在: $UNIAPP_DIR，跳过构建"
        return 0
    fi
    
    cd "$UNIAPP_DIR"
    
    # 清理构建
    if [ "$CLEAN_BUILD" = "true" ]; then
        log "INFO" "清理小程序构建..."
        rm -rf node_modules unpackage
    fi
    
    # 安装依赖
    log "INFO" "安装小程序依赖..."
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    # 构建小程序
    log "INFO" "构建小程序..."
    if ! npm run build:mp-weixin; then
        log "ERROR" "小程序构建失败"
        return 1
    fi
    
    # 检查构建结果
    if [ ! -d "unpackage/dist/build/mp-weixin" ]; then
        log "ERROR" "小程序构建产物不存在"
        return 1
    fi
    
    log "INFO" "小程序构建完成"
    return 0
}

# 创建前端Docker镜像
build_frontend_docker() {
    log "INFO" "构建前端Docker镜像..."
    
    cd "$FRONTEND_DIR"
    
    # 创建临时Dockerfile
    cat > Dockerfile.temp << 'EOF'
FROM nginx:1.24-alpine

# 复制构建产物
COPY dist/ /usr/share/nginx/html/

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 设置权限
RUN chmod -R 755 /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
EOF

    # 创建nginx配置文件
    cat > nginx.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://park-gateway:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

    # 构建Docker镜像
    local image_name="park/park-frontend:$PROJECT_VERSION"
    
    if ! docker build -t "$image_name" -f Dockerfile.temp .; then
        log "ERROR" "前端Docker镜像构建失败"
        rm -f Dockerfile.temp nginx.conf
        return 1
    fi
    
    # 清理临时文件
    rm -f Dockerfile.temp nginx.conf
    
    log "INFO" "前端Docker镜像构建完成: $image_name"
    return 0
}

# 复制构建产物到部署目录
copy_build_artifacts() {
    log "INFO" "复制构建产物到部署目录..."
    
    local deploy_frontend_dir="$DEPLOY_DIR/frontend"
    
    # 创建部署前端目录
    mkdir -p "$deploy_frontend_dir"
    
    # 复制管理后台构建产物
    if [ -d "$FRONTEND_DIR/dist" ]; then
        cp -r "$FRONTEND_DIR/dist/"* "$deploy_frontend_dir/"
        log "INFO" "管理后台构建产物已复制到: $deploy_frontend_dir"
    fi
    
    # 复制小程序构建产物（如果存在）
    if [ -d "$UNIAPP_DIR/unpackage/dist/build/mp-weixin" ]; then
        local uniapp_deploy_dir="$DEPLOY_DIR/uniapp"
        mkdir -p "$uniapp_deploy_dir"
        cp -r "$UNIAPP_DIR/unpackage/dist/build/mp-weixin/"* "$uniapp_deploy_dir/"
        log "INFO" "小程序构建产物已复制到: $uniapp_deploy_dir"
    fi
}

# 主构建函数
main() {
    log "INFO" "开始前端构建..."
    log "INFO" "构建环境: $BUILD_ENV"
    log "INFO" "项目版本: $PROJECT_VERSION"
    
    # 检查Node.js版本
    check_node_version
    
    # 构建管理后台
    if ! build_admin_ui; then
        log "ERROR" "管理后台构建失败"
        return 1
    fi
    
    # 构建小程序
    if ! build_uniapp; then
        log "ERROR" "小程序构建失败"
        return 1
    fi
    
    # 构建Docker镜像
    if [ "$BUILD_DOCKER" = "true" ]; then
        if ! build_frontend_docker; then
            log "ERROR" "前端Docker镜像构建失败"
            return 1
        fi
    fi
    
    # 复制构建产物
    copy_build_artifacts
    
    log "INFO" "前端构建完成"
    
    # 显示构建结果
    log "INFO" "构建结果:"
    if [ -d "$FRONTEND_DIR/dist" ]; then
        local dist_size=$(du -sh "$FRONTEND_DIR/dist" | cut -f1)
        log "INFO" "  管理后台: $dist_size"
    fi
    
    if [ -d "$UNIAPP_DIR/unpackage/dist/build/mp-weixin" ]; then
        local uniapp_size=$(du -sh "$UNIAPP_DIR/unpackage/dist/build/mp-weixin" | cut -f1)
        log "INFO" "  小程序: $uniapp_size"
    fi
    
    if [ "$BUILD_DOCKER" = "true" ]; then
        docker images "park/park-frontend:$PROJECT_VERSION" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    fi
    
    return 0
}

# 执行主函数
main "$@"
