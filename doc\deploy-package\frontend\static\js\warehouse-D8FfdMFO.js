import{v as t}from"./index-Cox2ohSR.js";function o(e){return t({url:"/system/platform/warehouse/list",method:"get",params:e})}function s(e){return t({url:"/system/platform/warehouse/treeList",method:"get",params:e})}function a(e){return t({url:"/system/platform/warehouse/"+e,method:"get"})}function u(e){return t({url:"/system/platform/warehouse",method:"post",data:e})}function m(e){return t({url:"/system/platform/warehouse",method:"put",data:e})}function n(e){return t({url:"/system/platform/warehouse/"+e,method:"delete"})}function l(){return t({url:"/system/platform/warehouse/optionSelect",method:"get"})}function h(e){return t({url:"/system/platform/warehouse/optionSelectByOperator",method:"get",params:{operatorId:e}})}export{s as a,u as b,h as c,n as d,a as g,o as l,l as o,m as u};
