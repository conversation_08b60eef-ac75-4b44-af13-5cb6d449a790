# 设计文档

## 概述

本地CI/CD工具是一个基于Python的应用程序，提供用于构建、打包和部署微服务及前端应用程序的Web界面。该系统采用MCP（Model Context Protocol）架构，既支持通过NiceGUI的用户界面进行人工操作，也支持AI代理通过MCP协议进行自动化调用。系统利用Docker进行容器化，SSH/SFTP进行安全的远程部署，架构强调异步操作、实时反馈和强大的错误处理。

## 架构

应用程序采用模块化架构，具有清晰的关注点分离：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP层         │    │   UI层          │    │  服务层         │
│ (AI代理接口)    │◄──►│  (NiceGUI)      │◄──►│ (业务逻辑)      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ MCP工具接口     │    │ 事件处理器      │    │ 构建管理器      │
│ 协议处理        │    │ 状态管理        │    │ 部署管理器      │
│ 请求路由        │    │ 实时日志        │    │ 配置管理器      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                            ┌─────────────────┐
                                            │ 基础设施层      │
                                            │ Docker客户端    │
                                            │ SSH客户端       │
                                            │ 文件系统        │
                                            └─────────────────┘
```

### 核心组件

1. **MCP服务器**: 处理AI代理的MCP协议请求和响应
2. **UI控制器**: 管理NiceGUI界面和用户交互
3. **构建管理器**: 编排Docker构建和前端打包
4. **部署管理器**: 处理SSH连接和远程部署
5. **配置管理器**: 管理应用程序配置和环境设置
6. **日志管理器**: 提供实时日志记录和错误报告

## 组件和接口

### 1. MCP服务器 (`mcp_server.py`)

```python
class MCPServer:
    def __init__(self):
        self.build_manager = BuildManager()
        self.deploy_manager = DeployManager()
        self.log_manager = LogManager()
    
    async def handle_list_tools(self):
        # 返回可用的MCP工具列表
        pass
    
    async def handle_call_tool(self, tool_name: str, arguments: dict):
        # 处理AI代理的工具调用请求
        pass
    
    async def get_services_info(self):
        # 获取可用服务信息
        pass
    
    async def build_services_mcp(self, services: List[str], environment: str):
        # MCP接口的服务构建
        pass
    
    async def deploy_services_mcp(self, deployment_config: dict):
        # MCP接口的服务部署
        pass
```

**职责：**
- 实现MCP协议的服务器端
- 处理AI代理的工具调用请求
- 提供标准化的API接口供AI使用
- 管理AI代理的认证和权限

### 2. UI控制器 (`ui_controller.py`)

```python
class UIController:
    def __init__(self):
        self.build_manager = BuildManager()
        self.deploy_manager = DeployManager()
        self.log_manager = LogManager()
    
    async def render_interface(self):
        # 主UI渲染逻辑
        pass
    
    async def handle_deploy_click(self):
        # 部署按钮事件处理器
        pass
```

**职责：**
- 渲染NiceGUI界面组件
- 处理用户交互和事件
- 协调UI状态和业务逻辑
- 管理主题切换（暗色/亮色模式）

### 3. 构建管理器 (`build_manager.py`)

```python
class BuildManager:
    def __init__(self):
        self.docker_client = docker.from_env()
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
    
    async def build_services(self, selected_services: List[str]) -> Dict[str, bool]:
        # 并行Docker构建
        pass
    
    async def package_frontend(self, frontend_path: str) -> str:
        # 前端打包逻辑
        pass
    
    async def get_build_status(self, build_id: str) -> dict:
        # 获取构建状态（供MCP调用）
        pass
```

**职责：**
- 解析微服务目录和Dockerfile
- 执行并行Docker镜像构建
- 处理前端npm构建和压缩
- 提供构建进度和状态更新
- 为MCP接口提供构建状态查询

### 4. 部署管理器 (`deploy_manager.py`)

```python
class DeployManager:
    def __init__(self):
        self.ssh_pool = SSHConnectionPool()
        self.sftp_client = None
    
    async def deploy_to_server(self, server_config: dict, artifacts: dict):
        # SSH部署逻辑
        pass
    
    async def rollback_deployment(self, server_config: dict):
        # 回滚逻辑
        pass
    
    async def get_deployment_status(self, deployment_id: str) -> dict:
        # 获取部署状态（供MCP调用）
        pass
```

**职责：**
- 管理SSH连接和连接池
- 处理SFTP文件传输
- 执行远程部署命令
- 实现回滚机制
- 为MCP接口提供部署状态查询

### 4. Config Manager (`config_manager.py`)

```python
class ConfigManager:
    def __init__(self):
        self.config = self.load_config()
    
    def discover_services(self) -> List[dict]:
        # Auto-discover microservices
        pass
    
    def load_ssh_keys(self) -> dict:
        # Load SSH keys from ~/.ssh/
        pass
```

**Responsibilities:**
- Load and manage application configuration
- Auto-discover microservices in project directories
- Handle SSH key management
- Environment-specific settings

### 5. Log Manager (`log_manager.py`)

```python
class LogManager:
    def __init__(self):
        self.log_queue = asyncio.Queue()
        self.ui_log = None
    
    async def push_log(self, message: str, level: str = "INFO"):
        # Add log entry
        pass
    
    async def stream_logs(self):
        # Stream logs to UI
        pass
```

**Responsibilities:**
- Centralized logging system
- Real-time log streaming to UI
- Log level management and filtering
- Error highlighting and formatting

## Data Models

### Service Configuration

```python
@dataclass
class ServiceConfig:
    name: str
    path: str
    dockerfile_path: str
    build_context: str
    environment: str
    dependencies: List[str] = field(default_factory=list)
```

### Deployment Configuration

```python
@dataclass
class DeploymentConfig:
    server_host: str
    server_port: int
    username: str
    ssh_key_path: str
    remote_path: str
    environment: str
    docker_compose_path: str
```

### Build Result

```python
@dataclass
class BuildResult:
    service_name: str
    success: bool
    image_id: Optional[str]
    build_time: float
    error_message: Optional[str]
    logs: List[str]
```

## Error Handling

### Error Categories

1. **Build Errors**: Docker build failures, missing Dockerfiles, dependency issues
2. **Network Errors**: SSH connection failures, SFTP transfer errors
3. **System Errors**: Missing dependencies, insufficient permissions
4. **Configuration Errors**: Invalid settings, missing SSH keys

### Error Handling Strategy

```python
class ErrorHandler:
    def __init__(self):
        self.error_callbacks = {}
    
    async def handle_build_error(self, error: BuildError):
        # Stop pipeline, log error, offer retry
        pass
    
    async def handle_deployment_error(self, error: DeploymentError):
        # Offer rollback, log detailed error
        pass
```

### Rollback Mechanism

```python
class RollbackManager:
    def __init__(self):
        self.deployment_history = []
    
    async def create_checkpoint(self, deployment_state: dict):
        # Save current state before deployment
        pass
    
    async def rollback_to_checkpoint(self, checkpoint_id: str):
        # Restore previous deployment state
        pass
```

## Testing Strategy

### Unit Testing
- **Build Manager**: Mock Docker client, test parallel builds
- **Deploy Manager**: Mock SSH connections, test file transfers
- **Config Manager**: Test service discovery and configuration loading
- **UI Controller**: Test event handling and state management

### Integration Testing
- **End-to-End Deployment**: Test complete deployment pipeline
- **Error Scenarios**: Test error handling and rollback mechanisms
- **Performance Testing**: Test parallel builds and connection pooling

### Test Structure

```python
# tests/test_build_manager.py
class TestBuildManager:
    @pytest.fixture
    def mock_docker_client(self):
        # Mock Docker client
        pass
    
    async def test_parallel_builds(self, mock_docker_client):
        # Test parallel Docker builds
        pass
    
    async def test_build_failure_handling(self, mock_docker_client):
        # Test build error scenarios
        pass
```

### Mocking Strategy
- **Docker Client**: Mock image builds and container operations
- **SSH Client**: Mock remote connections and command execution
- **File System**: Mock file operations and directory scanning
- **Network Operations**: Mock SFTP transfers and network timeouts

## Performance Considerations

### Asynchronous Operations
- Use `asyncio` for non-blocking UI updates
- Implement async/await patterns for I/O operations
- Queue-based log streaming for real-time updates

### Resource Management
- Thread pool for Docker builds (max 4 concurrent)
- SSH connection pooling to prevent connection overhead
- Memory-efficient log handling with circular buffers

### Optimization Strategies
- Parallel Docker builds with dependency management
- Incremental frontend builds when possible
- Compressed file transfers to reduce network overhead
- Connection reuse for multiple deployment operations

## Security Considerations

### SSH Security
- Automatic SSH key detection from `~/.ssh/`
- Support for SSH agent integration
- Secure credential storage (no plaintext passwords)
- Connection timeout and retry mechanisms

### Docker Security
- Use official base images when possible
- Scan for security vulnerabilities in built images
- Implement resource limits for build operations
- Secure handling of build secrets and environment variables

### Network Security
- Encrypted SFTP transfers for all file operations
- Secure remote command execution
- Network timeout configurations
- Audit logging for all deployment operations