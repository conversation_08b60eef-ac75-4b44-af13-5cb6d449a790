#!/bin/bash

# 部署清理脚本
# 用于清理失败或中断的部署

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 默认参数
CLEANUP_ENV="test"
FORCE_CLEANUP="false"
CLEANUP_DOCKER="true"
CLEANUP_DATA="false"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            CLEANUP_ENV="${1#*=}"
            shift
            ;;
        --force)
            FORCE_CLEANUP="true"
            shift
            ;;
        --no-docker)
            CLEANUP_DOCKER="false"
            shift
            ;;
        --cleanup-data)
            CLEANUP_DATA="true"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV        清理环境 (test|staging|prod) 默认: test"
            echo "  --force          强制清理"
            echo "  --no-docker      不清理Docker资源"
            echo "  --cleanup-data   清理数据文件（危险操作）"
            echo "  -h, --help       显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

log "INFO" "开始清理部署环境: $CLEANUP_ENV"

# 安全检查
if [ "$CLEANUP_ENV" = "prod" ] && [ "$FORCE_CLEANUP" != "true" ]; then
    log "ERROR" "生产环境清理需要 --force 参数"
    exit 1
fi

# 停止服务
stop_services() {
    log "INFO" "停止服务..."
    
    cd "$PROJECT_ROOT"
    
    # 停止Docker Compose服务
    if [ -f "deploy-package/docker-compose.yml" ]; then
        log "INFO" "停止Docker Compose服务..."
        docker-compose -f deploy-package/docker-compose.yml down --remove-orphans || true
    fi
    
    # 停止可能运行的单独容器
    log "INFO" "停止相关Docker容器..."
    docker ps -q --filter "name=${PROJECT_NAME}" | xargs -r docker stop || true
    
    log "INFO" "服务停止完成"
}

# 清理Docker资源
cleanup_docker_resources() {
    if [ "$CLEANUP_DOCKER" = "true" ]; then
        log "INFO" "清理Docker资源..."
        
        # 删除容器
        log "INFO" "删除相关容器..."
        docker ps -aq --filter "name=${PROJECT_NAME}" | xargs -r docker rm -f || true
        
        # 删除镜像（保留最新的）
        log "INFO" "清理旧的Docker镜像..."
        docker images --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}" | \
            grep "${PROJECT_NAME}" | \
            tail -n +3 | \
            awk '{print $2}' | \
            xargs -r docker rmi -f || true
        
        # 清理未使用的网络
        log "INFO" "清理未使用的Docker网络..."
        docker network prune -f || true
        
        # 清理未使用的卷
        log "INFO" "清理未使用的Docker卷..."
        docker volume prune -f || true
        
        log "INFO" "Docker资源清理完成"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log "INFO" "清理临时文件..."
    
    cd "$PROJECT_ROOT"
    
    # 清理构建临时文件
    find . -name "*.tmp" -type f -delete || true
    find . -name "*.temp" -type f -delete || true
    find . -name "*.lock" -type f -delete || true
    
    # 清理日志文件（保留最近的）
    if [ -d "logs" ]; then
        find logs -name "*.log" -type f -mtime +7 -delete || true
    fi
    
    # 清理Maven临时文件
    if [ -d "park-api/target" ]; then
        rm -rf park-api/target/tmp || true
        rm -rf park-api/target/maven-* || true
    fi
    
    # 清理Node.js临时文件
    if [ -d "park-ui" ]; then
        rm -rf park-ui/.tmp || true
        rm -rf park-ui/dist || true
    fi
    
    log "INFO" "临时文件清理完成"
}

# 清理数据文件（危险操作）
cleanup_data_files() {
    if [ "$CLEANUP_DATA" = "true" ]; then
        log "WARN" "清理数据文件（危险操作）..."
        
        if [ "$CLEANUP_ENV" = "prod" ]; then
            log "ERROR" "拒绝清理生产环境数据文件"
            return 1
        fi
        
        cd "$PROJECT_ROOT"
        
        # 清理数据库文件
        if [ -d "data/mysql" ]; then
            log "WARN" "清理MySQL数据文件..."
            rm -rf data/mysql/* || true
        fi
        
        # 清理Redis数据文件
        if [ -d "data/redis" ]; then
            log "WARN" "清理Redis数据文件..."
            rm -rf data/redis/* || true
        fi
        
        # 清理上传文件
        if [ -d "data/uploads" ]; then
            log "WARN" "清理上传文件..."
            rm -rf data/uploads/* || true
        fi
        
        log "WARN" "数据文件清理完成"
    fi
}

# 重置配置文件
reset_config_files() {
    log "INFO" "重置配置文件..."
    
    cd "$PROJECT_ROOT"
    
    # 恢复默认配置文件
    if [ -f "deploy-package/docker-compose.yml.backup" ]; then
        cp deploy-package/docker-compose.yml.backup deploy-package/docker-compose.yml || true
    fi
    
    # 重置环境变量文件
    if [ -f "deploy-package/.env.${CLEANUP_ENV}.backup" ]; then
        cp "deploy-package/.env.${CLEANUP_ENV}.backup" "deploy-package/.env" || true
    fi
    
    log "INFO" "配置文件重置完成"
}

# 验证清理结果
verify_cleanup() {
    log "INFO" "验证清理结果..."
    
    # 检查是否还有运行的容器
    local running_containers=$(docker ps -q --filter "name=${PROJECT_NAME}" | wc -l)
    if [ "$running_containers" -gt 0 ]; then
        log "WARN" "仍有 $running_containers 个相关容器在运行"
    else
        log "INFO" "所有相关容器已停止"
    fi
    
    # 检查端口占用
    local occupied_ports=$(netstat -tlnp | grep -E ':(8080|8081|3000|80|443)' | wc -l)
    if [ "$occupied_ports" -gt 0 ]; then
        log "WARN" "仍有端口被占用"
        netstat -tlnp | grep -E ':(8080|8081|3000|80|443)'
    else
        log "INFO" "相关端口已释放"
    fi
    
    log "INFO" "清理验证完成"
}

# 主清理函数
main() {
    log "INFO" "开始清理部署环境..."
    log "INFO" "清理环境: $CLEANUP_ENV"
    log "INFO" "强制清理: $FORCE_CLEANUP"
    log "INFO" "清理Docker: $CLEANUP_DOCKER"
    log "INFO" "清理数据: $CLEANUP_DATA"
    
    # 执行清理步骤
    stop_services
    cleanup_docker_resources
    cleanup_temp_files
    
    if [ "$CLEANUP_DATA" = "true" ]; then
        cleanup_data_files
    fi
    
    reset_config_files
    verify_cleanup
    
    log "INFO" "部署环境清理完成"
    return 0
}

# 执行主函数
main "$@"
