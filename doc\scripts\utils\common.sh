#!/bin/bash

# 停车管理系统通用函数库
# 作者: DevOps Team
# 版本: 1.0

# 设置脚本错误时退出
set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 设置默认配置
set_default_config() {
    # 项目配置
    PROJECT_NAME="${PROJECT_NAME:-park-new}"
    PROJECT_ROOT="${PROJECT_ROOT:-/opt/park-new}"
    PROJECT_VERSION="${PROJECT_VERSION:-3.6.6}"

    # 网络架构配置
    SERVER_HOST="${SERVER_HOST:-*************}"
    SSH_PORT="${SSH_PORT:-1224}"
    EXTERNAL_PORT="${EXTERNAL_PORT:-3443}"
    SYSTEM_NGINX_PORT="${SYSTEM_NGINX_PORT:-443}"
    JENKINS_SYSTEM_PORT="${JENKINS_SYSTEM_PORT:-9001}"
    FRONTEND_SYSTEM_PORT="${FRONTEND_SYSTEM_PORT:-9443}"

    # 访问地址配置
    BASE_DOMAIN="${BASE_DOMAIN:-test-parknew.lgfw24hours.com}"
    EXTERNAL_URL="https://${BASE_DOMAIN}:${EXTERNAL_PORT}"
    JENKINS_URL="${EXTERNAL_URL}/jenkins"
    FRONTEND_URL="${EXTERNAL_URL}/"
    API_URL="${EXTERNAL_URL}/prod-api"

    # Docker容器名称
    JENKINS_CONTAINER="${JENKINS_CONTAINER:-jenkins-park}"
    NGINX_CONTAINER="${NGINX_CONTAINER:-park-nginx}"
    BACKEND_CONTAINER="${BACKEND_CONTAINER:-park-backend}"
    MYSQL_CONTAINER="${MYSQL_CONTAINER:-park-mysql}"
    REDIS_CONTAINER="${REDIS_CONTAINER:-park-redis}"

    # 目录配置
    LOGS_DIR="${PROJECT_ROOT}/logs"
    BACKUP_DIR="${PROJECT_ROOT}/backup"
    DEPLOY_DIR="${PROJECT_ROOT}/deploy-package"

    # 日志级别
    LOG_LEVEL="${LOG_LEVEL:-INFO}"

    export PROJECT_NAME PROJECT_ROOT PROJECT_VERSION
    export SERVER_HOST SSH_PORT EXTERNAL_PORT SYSTEM_NGINX_PORT
    export JENKINS_SYSTEM_PORT FRONTEND_SYSTEM_PORT
    export BASE_DOMAIN EXTERNAL_URL JENKINS_URL FRONTEND_URL API_URL
    export JENKINS_CONTAINER NGINX_CONTAINER BACKEND_CONTAINER MYSQL_CONTAINER REDIS_CONTAINER
    export LOGS_DIR BACKUP_DIR DEPLOY_DIR LOG_LEVEL
}

# 加载配置文件（可选）
load_config() {
    # 配置文件已简化，所有配置都在common.sh中设置默认值
    log "INFO" "使用内置默认配置"
}



# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")
            echo -e "\033[32m[$timestamp] [INFO]\033[0m $message" | tee -a "$LOGS_DIR/build.log"
            ;;
        "WARN")
            echo -e "\033[33m[$timestamp] [WARN]\033[0m $message" | tee -a "$LOGS_DIR/build.log"
            ;;
        "ERROR")
            echo -e "\033[31m[$timestamp] [ERROR]\033[0m $message" | tee -a "$LOGS_DIR/build.log" >&2
            ;;
        "DEBUG")
            if [ "$LOG_LEVEL" = "DEBUG" ]; then
                echo -e "\033[36m[$timestamp] [DEBUG]\033[0m $message" | tee -a "$LOGS_DIR/build.log"
            fi
            ;;
        *)
            echo -e "[$timestamp] $level $message" | tee -a "$LOGS_DIR/build.log"
            ;;
    esac
}

# 检查命令是否存在
check_command() {
    local cmd="$1"
    if ! command -v "$cmd" &> /dev/null; then
        log "ERROR" "命令不存在: $cmd"
        return 1
    fi
    return 0
}

# 检查必要的工具
check_prerequisites() {
    log "INFO" "检查必要工具..."
    
    local required_commands=("docker" "docker-compose" "mvn" "git")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if ! check_command "$cmd"; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [ ${#missing_commands[@]} -gt 0 ]; then
        log "ERROR" "缺少必要工具: ${missing_commands[*]}"
        log "ERROR" "请安装缺少的工具后重试"
        exit 1
    fi
    
    log "INFO" "工具检查完成"
}

# 创建必要目录
create_directories() {
    log "INFO" "创建必要目录..."
    
    local dirs=("$LOGS_DIR" "$BACKUP_DIR")
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log "INFO" "创建目录: $dir"
        fi
    done
}

# 获取Git信息
get_git_info() {
    if [ -d "$PROJECT_ROOT/.git" ]; then
        cd "$PROJECT_ROOT"
        GIT_COMMIT=$(git rev-parse HEAD)
        GIT_COMMIT_SHORT=$(git rev-parse --short HEAD)
        GIT_BRANCH_CURRENT=$(git rev-parse --abbrev-ref HEAD)
        GIT_TAG=$(git describe --tags --exact-match 2>/dev/null || echo "")
        
        export GIT_COMMIT GIT_COMMIT_SHORT GIT_BRANCH_CURRENT GIT_TAG
        
        log "INFO" "Git信息: 分支=$GIT_BRANCH_CURRENT, 提交=$GIT_COMMIT_SHORT"
    else
        log "WARN" "未找到Git仓库"
    fi
}

# 检查服务是否运行
is_service_running() {
    local service="$1"
    local container_name="park-${service,,}"
    
    if docker ps --format "table {{.Names}}" | grep -q "^$container_name$"; then
        return 0
    else
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local service="$1"
    local timeout="${2:-60}"
    local interval="${3:-5}"
    
    log "INFO" "等待服务启动: $service"
    
    local count=0
    local max_count=$((timeout / interval))
    
    while [ $count -lt $max_count ]; do
        if is_service_running "$service"; then
            log "INFO" "服务 $service 已启动"
            return 0
        fi
        
        log "DEBUG" "等待服务 $service 启动... ($((count * interval))s/$timeout s)"
        sleep "$interval"
        ((count++))
    done
    
    log "ERROR" "服务 $service 启动超时"
    return 1
}

# 获取服务列表
get_services_by_group() {
    local group="$1"
    
    case "$group" in
        "infrastructure")
            echo "MYSQL REDIS NACOS"
            ;;
        "backend")
            echo "AUTH SYSTEM FILE WX_AUTH WX GATE GATEWAY"
            ;;
        "frontend")
            echo "NGINX"
            ;;
        "all")
            echo "MYSQL REDIS NACOS AUTH SYSTEM FILE WX_AUTH WX GATE GATEWAY NGINX"
            ;;
        *)
            echo "$group"
            ;;
    esac
}

# 按启动顺序排序服务
sort_services_by_order() {
    local services=("$@")
    local sorted_services=()
    
    # 创建临时数组存储服务和其启动顺序
    local service_orders=()
    
    for service in "${services[@]}"; do
        local order="${SERVICE_START_ORDER[$service]:-99}"
        service_orders+=("$order:$service")
    done
    
    # 排序并提取服务名
    IFS=$'\n' sorted_array=($(sort -n <<< "${service_orders[*]}"))
    
    for item in "${sorted_array[@]}"; do
        sorted_services+=("${item#*:}")
    done
    
    echo "${sorted_services[@]}"
}

# 初始化函数
init_common() {
    set_default_config
    load_config
    create_directories
    get_git_info
    # check_prerequisites  # 可选，避免在某些环境下失败
}

# 如果直接执行此脚本，则运行初始化
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    init_common
    log "INFO" "通用函数库初始化完成"
fi
