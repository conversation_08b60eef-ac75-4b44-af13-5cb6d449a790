import{v as q,O as ie,d as me,r as f,C as ce,T as fe,e as p,Q as E,c as C,o as m,R as h,f as e,S as _e,l as t,h as l,m as I,L as H,M as A,j as b,P,n as d,i as $,t as i,k as G}from"./index-D_FV2sri.js";import{C as J}from"./index-C9zsUI-h.js";function ge(k){return q({url:"/system/operlog/list",method:"get",params:k})}function be(k){return q({url:"/system/operlog/"+k,method:"delete"})}function ve(){return q({url:"/system/operlog/clean",method:"delete"})}const ye={class:"app-container"},he={key:0},we={key:1},Ve={class:"dialog-footer"},Ce=ie({name:"Operlog"}),Se=Object.assign(Ce,{components:{CustomPagination:J}},{setup(k){const{proxy:_}=me(),{sys_oper_type:N,sys_common_status:z}=_.useDict("sys_oper_type","sys_common_status"),M=f([]),w=f(!1),R=f(!0),T=f(!0),B=f([]);f(!0);const L=f(!0),O=f(0);f("");const V=f([]),U=f({prop:"operTime",order:"descending"}),W=ce({form:{},queryParams:{pageNum:1,pageSize:10,operIp:void 0,title:void 0,operName:void 0,businessType:void 0,status:void 0}}),{queryParams:n,form:s}=fe(W);function v(){R.value=!0,ge(_.addDateRange(n.value,V.value)).then(u=>{M.value=u.rows,O.value=u.total,R.value=!1})}function X(u,a){return _.selectDictLabel(N.value,u.businessType)}function x(){n.value.pageNum=1,v()}function Z(){V.value=[],_.resetForm("queryRef"),n.value.pageNum=1,_.$refs.operlogRef.sort(U.value.prop,U.value.order)}function ee(u){B.value=u.map(a=>a.operId),L.value=!u.length}function le(u,a,S){n.value.orderByColumn=u.prop,n.value.isAsc=u.order,v()}function te(u){w.value=!0,s.value=u}function oe(u){const a=u.operId||B.value;_.$modal.confirm('是否确认删除日志编号为"'+a+'"的数据项?').then(function(){return be(a)}).then(()=>{v(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ae(){_.$modal.confirm("是否确认清空所有操作日志数据项?").then(function(){return ve()}).then(()=>{v(),_.$modal.msgSuccess("清空成功")}).catch(()=>{})}function ne(){_.download("system/operlog/export",{...n.value},`config_${new Date().getTime()}.xlsx`)}return v(),(u,a)=>{const S=p("el-input"),r=p("el-form-item"),K=p("el-option"),Q=p("el-select"),re=p("el-date-picker"),y=p("el-button"),Y=p("el-form"),c=p("el-col"),se=p("right-toolbar"),j=p("el-row"),g=p("el-table-column"),F=p("dict-tag"),ue=p("el-table"),de=p("el-dialog"),D=E("hasPermi"),pe=E("loading");return m(),C("div",ye,[h(e(Y,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(r,{label:"操作地址",prop:"operIp"},{default:l(()=>[e(S,{modelValue:t(n).operIp,"onUpdate:modelValue":a[0]||(a[0]=o=>t(n).operIp=o),placeholder:"请输入操作地址",clearable:"",style:{width:"240px"},onKeyup:I(x,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"系统模块",prop:"title"},{default:l(()=>[e(S,{modelValue:t(n).title,"onUpdate:modelValue":a[1]||(a[1]=o=>t(n).title=o),placeholder:"请输入系统模块",clearable:"",style:{width:"240px"},onKeyup:I(x,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"操作人员",prop:"operName"},{default:l(()=>[e(S,{modelValue:t(n).operName,"onUpdate:modelValue":a[2]||(a[2]=o=>t(n).operName=o),placeholder:"请输入操作人员",clearable:"",style:{width:"240px"},onKeyup:I(x,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"类型",prop:"businessType"},{default:l(()=>[e(Q,{modelValue:t(n).businessType,"onUpdate:modelValue":a[3]||(a[3]=o=>t(n).businessType=o),placeholder:"操作类型",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),C(H,null,A(t(N),o=>(m(),b(K,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"状态",prop:"status"},{default:l(()=>[e(Q,{modelValue:t(n).status,"onUpdate:modelValue":a[4]||(a[4]=o=>t(n).status=o),placeholder:"操作状态",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),C(H,null,A(t(z),o=>(m(),b(K,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"操作时间",style:{width:"308px"}},{default:l(()=>[e(re,{modelValue:t(V),"onUpdate:modelValue":a[5]||(a[5]=o=>P(V)?V.value=o:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),e(r,null,{default:l(()=>[e(y,{type:"primary",icon:"Search",onClick:x},{default:l(()=>[d("搜索")]),_:1}),e(y,{icon:"Refresh",onClick:Z},{default:l(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[_e,t(T)]]),e(j,{gutter:10,class:"mb8"},{default:l(()=>[e(c,{span:1.5},{default:l(()=>[h((m(),b(y,{type:"danger",plain:"",icon:"Delete",disabled:t(L),onClick:oe},{default:l(()=>[d("删除")]),_:1},8,["disabled"])),[[D,["system:operlog:remove"]]])]),_:1}),e(c,{span:1.5},{default:l(()=>[h((m(),b(y,{type:"danger",plain:"",icon:"Delete",onClick:ae},{default:l(()=>[d("清空")]),_:1})),[[D,["system:operlog:remove"]]])]),_:1}),e(c,{span:1.5},{default:l(()=>[h((m(),b(y,{type:"warning",plain:"",icon:"Download",onClick:ne},{default:l(()=>[d("导出")]),_:1})),[[D,["system:operlog:export"]]])]),_:1}),e(se,{showSearch:t(T),"onUpdate:showSearch":a[6]||(a[6]=o=>P(T)?T.value=o:null),onQueryTable:v},null,8,["showSearch"])]),_:1}),h((m(),b(ue,{ref:"operlogRef",data:t(M),onSelectionChange:ee,"default-sort":t(U),onSortChange:le},{default:l(()=>[e(g,{type:"selection",width:"50",align:"center"}),e(g,{label:"系统模块",align:"center",prop:"title","show-overflow-tooltip":!0}),e(g,{label:"操作类型",align:"center",prop:"businessType"},{default:l(o=>[e(F,{options:t(N),value:o.row.businessType},null,8,["options","value"])]),_:1}),e(g,{label:"请求方式",align:"center",prop:"requestMethod"}),e(g,{label:"操作人员",align:"center",prop:"operName",width:"110","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"操作地址",align:"center",prop:"operIp",width:"130","show-overflow-tooltip":!0}),e(g,{label:"操作状态",align:"center",prop:"status"},{default:l(o=>[e(F,{options:t(z),value:o.row.status},null,8,["options","value"])]),_:1}),e(g,{label:"操作日期",align:"center",prop:"operTime",width:"180",sortable:"custom","sort-orders":["descending","ascending"]},{default:l(o=>[$("span",null,i(u.parseTime(o.row.operTime)),1)]),_:1}),e(g,{label:"消耗时间",align:"center",prop:"costTime",width:"110","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]},{default:l(o=>[$("span",null,i(o.row.costTime)+"毫秒",1)]),_:1}),e(g,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[h((m(),b(y,{link:"",type:"primary",icon:"View",onClick:ke=>te(o.row,o.index)},{default:l(()=>[d("详细")]),_:2},1032,["onClick"])),[[D,["system:operlog:query"]]])]),_:1})]),_:1},8,["data","default-sort"])),[[pe,t(R)]]),e(J,{total:t(O),"current-page":t(n).pageNum,"onUpdate:currentPage":a[7]||(a[7]=o=>t(n).pageNum=o),"page-size":t(n).pageSize,"onUpdate:pageSize":a[8]||(a[8]=o=>t(n).pageSize=o),onPagination:v},null,8,["total","current-page","page-size"]),e(de,{title:"操作日志详细",modelValue:t(w),"onUpdate:modelValue":a[10]||(a[10]=o=>P(w)?w.value=o:null),width:"800px","append-to-body":""},{footer:l(()=>[$("div",Ve,[e(y,{onClick:a[9]||(a[9]=o=>w.value=!1)},{default:l(()=>[d("关 闭")]),_:1})])]),default:l(()=>[e(Y,{model:t(s),"label-width":"100px"},{default:l(()=>[e(j,null,{default:l(()=>[e(c,{span:12},{default:l(()=>[e(r,{label:"操作模块："},{default:l(()=>[d(i(t(s).title)+" / "+i(X(t(s))),1)]),_:1}),e(r,{label:"登录信息："},{default:l(()=>[d(i(t(s).operName)+" / "+i(t(s).operIp),1)]),_:1})]),_:1}),e(c,{span:12},{default:l(()=>[e(r,{label:"请求地址："},{default:l(()=>[d(i(t(s).operUrl),1)]),_:1}),e(r,{label:"请求方式："},{default:l(()=>[d(i(t(s).requestMethod),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(r,{label:"操作方法："},{default:l(()=>[d(i(t(s).method),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(r,{label:"请求参数："},{default:l(()=>[d(i(t(s).operParam),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(r,{label:"返回参数："},{default:l(()=>[d(i(t(s).jsonResult),1)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(r,{label:"操作状态："},{default:l(()=>[t(s).status===0?(m(),C("div",he,"正常")):t(s).status===1?(m(),C("div",we,"失败")):G("",!0)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(r,{label:"消耗时间："},{default:l(()=>[d(i(t(s).costTime)+"毫秒",1)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(r,{label:"操作时间："},{default:l(()=>[d(i(u.parseTime(t(s).operTime)),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[t(s).status===1?(m(),b(r,{key:0,label:"异常信息："},{default:l(()=>[d(i(t(s).errorMsg),1)]),_:1})):G("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{Se as default};
