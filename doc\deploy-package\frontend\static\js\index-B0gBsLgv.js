import{v as R,_ as Ie,O as Ce,d as Te,r as v,C as xe,T as De,x as Se,e as s,Q as te,c as h,o as u,R as C,f as e,S as Ue,h as l,m as Re,l as t,L as O,M as E,j as y,n as r,t as p,i as k,a0 as j,k as G,A as $e,B as qe}from"./index-Cox2ohSR.js";import{o as Be}from"./warehouse-D8FfdMFO.js";import{C as ne}from"./index-Cy2yB1cz.js";function ze(c){return R({url:"/system/gateParkingInfo/list",method:"get",params:c})}function oe(c){return R({url:"/system/gateParkingInfo/"+c,method:"get"})}function Oe(c){return R({url:"/system/gateParkingInfo",method:"post",data:c})}function Ee(c){return R({url:"/system/gateParkingInfo",method:"put",data:c})}function Ge(c){return R({url:"/system/gateParkingInfo/"+c,method:"delete"})}function Me(){return R({url:"/system/gateParkingInfo/carTypeOptions",method:"get"})}const A=c=>($e("data-v-ca1f19be"),c=c(),qe(),c),Fe={class:"app-container"},Le={key:0},Qe={key:1,class:"text-gray-400"},Ye={key:0},je={key:1,class:"text-gray-400"},Ae={class:"text-red-500 font-medium"},Ke={key:0,class:"view-container"},We={style:{color:"#f56c6c","font-weight":"bold"}},He={class:"image-section"},Je=A(()=>k("h3",{style:{"margin-bottom":"15px",color:"#303133"}},"车辆照片",-1)),Xe={class:"image-container"},Ze={key:0,class:"image-item"},el=A(()=>k("h4",null,"入场照片",-1)),ll={key:1,class:"image-item",style:{"margin-top":"20px"}},al=A(()=>k("h4",null,"出场照片",-1)),tl={key:2,class:"no-image"},nl={class:"dialog-footer"},ol=Ce({name:"GateParkingInfo"}),ul=Object.assign(ol,{components:{CustomPagination:ne}},{setup(c){const{proxy:N}=Te(),K=v([]),V=v(!1),M=v(!0),F=v(!0),$=v([]),W=v(!0),H=v(!0),J=v(0),q=v(""),T=v([]),L=v([]),Q=v([]),f=v(!1),ue=xe({form:{},queryParams:{pageNum:1,pageSize:10,plateNum:null,parkingId:null,carType:null,status:null,inBeginTime:null,inEndTime:null},rules:{plateNum:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],carType:[{required:!0,message:"车辆类型不能为空",trigger:"change"}],parkingId:[{required:!0,message:"场库不能为空",trigger:"change"}]}}),{queryParams:d,form:o,rules:ie}=De(ue);function x(){M.value=!0,d.value.params={},T.value!=null&&T.value!=""&&(d.value.inBeginTime=T.value[0],d.value.inEndTime=T.value[1]),ze(d.value).then(i=>{K.value=i.rows,J.value=i.total,M.value=!1})}function Y(){d.value.pageNum=1,x()}function re(){T.value=[],N.resetForm("queryRef"),Object.assign(d.value,{pageNum:1,pageSize:10,plateNum:null,parkingId:null,carType:null,status:null,inBeginTime:null,inEndTime:null}),Y()}function de(i){$.value=i.map(n=>n.id),W.value=i.length!=1,H.value=!i.length}function se(){B(),f.value=!1,V.value=!0,q.value="添加车辆出入场记录"}function pe(i){B(),f.value=!0;const n=i.id||$.value[0];oe(n).then(P=>{o.value=P.data,V.value=!0,q.value="查看车辆出入场记录"})}function me(i){B(),f.value=!1;const n=i.id||$.value[0];oe(n).then(P=>{o.value=P.data,V.value=!0,q.value="修改车辆出入场记录"})}function ce(){N.$refs.gateParkingInfoRef.validate(i=>{i&&(o.value.id!=null?Ee(o.value).then(n=>{N.$modal.msgSuccess("修改成功"),V.value=!1,x()}):Oe(o.value).then(n=>{N.$modal.msgSuccess("新增成功"),V.value=!1,x()}))})}function X(i){const n=i.id||$.value;N.$modal.confirm('是否确认删除车辆出入场记录编号为"'+n+'"的数据项？').then(function(){return Ge(n)}).then(()=>{x(),N.$modal.msgSuccess("删除成功")}).catch(()=>{})}function fe(){N.download("system/gateParkingInfo/export",{...d.value},`车辆出入场记录_${new Date().getTime()}.xlsx`)}function _e(){V.value=!1,B()}function B(){o.value={id:null,parkingId:null,plateNum:null,carType:null,inTime:null,inChannelId:null,inChannelName:null,inPic:null,outTime:null,outChannelId:null,outChannelName:null,outPic:null,money:null,payType:null,status:0},N.resetForm("gateParkingInfoRef")}function Z(i){return i?i.length===8?"success":"primary":""}function ee(i){return i?i.length===8?"#d4edda":"#cce7ff":"#909399"}async function ge(){try{const i=await Be();L.value=i.data||[];const n=await Me();Q.value=n.data||[]}catch(i){console.error("初始化数据失败:",i)}}return Se(()=>{x(),ge()}),(i,n)=>{const P=s("el-input"),_=s("el-form-item"),I=s("el-option"),D=s("el-select"),ve=s("el-date-picker"),b=s("el-button"),le=s("el-form"),g=s("el-col"),he=s("right-toolbar"),S=s("el-row"),m=s("el-table-column"),z=s("el-tag"),ye=s("el-table"),w=s("el-descriptions-item"),be=s("el-descriptions"),ae=s("el-image"),we=s("el-empty"),ke=s("el-input-number"),Ne=s("el-dialog"),U=te("hasPermi"),Ve=te("loading");return u(),h("div",Fe,[C(e(le,{model:t(d),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(_,{label:"车牌号",prop:"plateNum"},{default:l(()=>[e(P,{modelValue:t(d).plateNum,"onUpdate:modelValue":n[0]||(n[0]=a=>t(d).plateNum=a),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:Re(Y,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"场库名称",prop:"parkingId"},{default:l(()=>[e(D,{modelValue:t(d).parkingId,"onUpdate:modelValue":n[1]||(n[1]=a=>t(d).parkingId=a),placeholder:"请选择场库",clearable:"",style:{width:"200px"}},{default:l(()=>[(u(!0),h(O,null,E(L.value,a=>(u(),y(I,{key:a.id,label:a.warehouseName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"车辆类型",prop:"carType"},{default:l(()=>[e(D,{modelValue:t(d).carType,"onUpdate:modelValue":n[2]||(n[2]=a=>t(d).carType=a),placeholder:"请选择车辆类型",clearable:"",style:{width:"200px"}},{default:l(()=>[(u(!0),h(O,null,E(Q.value,a=>(u(),y(I,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"状态",prop:"status"},{default:l(()=>[e(D,{modelValue:t(d).status,"onUpdate:modelValue":n[3]||(n[3]=a=>t(d).status=a),placeholder:"请选择状态",clearable:"",style:{width:"200px"}},{default:l(()=>[e(I,{label:"在场",value:"0"}),e(I,{label:"已出场",value:"1"})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"入场时间",style:{width:"308px"}},{default:l(()=>[e(ve,{modelValue:T.value,"onUpdate:modelValue":n[4]||(n[4]=a=>T.value=a),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(_,null,{default:l(()=>[e(b,{type:"primary",icon:"Search",onClick:Y},{default:l(()=>[r("搜索")]),_:1}),e(b,{icon:"Refresh",onClick:re},{default:l(()=>[r("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ue,F.value]]),e(S,{gutter:10,class:"mb8"},{default:l(()=>[e(g,{span:1.5},{default:l(()=>[C((u(),y(b,{type:"primary",plain:"",icon:"Plus",onClick:se},{default:l(()=>[r("新增")]),_:1})),[[U,["order:gateParkingInfo:add"]]])]),_:1}),e(g,{span:1.5},{default:l(()=>[C((u(),y(b,{type:"success",plain:"",icon:"Edit",disabled:W.value,onClick:me},{default:l(()=>[r("修改")]),_:1},8,["disabled"])),[[U,["order:gateParkingInfo:edit"]]])]),_:1}),e(g,{span:1.5},{default:l(()=>[C((u(),y(b,{type:"danger",plain:"",icon:"Delete",disabled:H.value,onClick:X},{default:l(()=>[r("删除")]),_:1},8,["disabled"])),[[U,["order:gateParkingInfo:remove"]]])]),_:1}),e(g,{span:1.5},{default:l(()=>[C((u(),y(b,{type:"warning",plain:"",icon:"Download",onClick:fe},{default:l(()=>[r("导出")]),_:1})),[[U,["order:gateParkingInfo:export"]]])]),_:1}),e(he,{showSearch:F.value,"onUpdate:showSearch":n[5]||(n[5]=a=>F.value=a),onQueryTable:x},null,8,["showSearch"])]),_:1}),C((u(),y(ye,{data:K.value,onSelectionChange:de},{default:l(()=>[e(m,{type:"selection",width:"55",align:"center"}),e(m,{label:"记录ID",align:"center",prop:"id",width:"180"}),e(m,{label:"车牌号",align:"center",prop:"plateNum",width:"120"},{default:l(a=>[e(z,{type:Z(a.row.plateNum),color:ee(a.row.plateNum),effect:"plain"},{default:l(()=>[r(p(a.row.plateNum),1)]),_:2},1032,["type","color"])]),_:1}),e(m,{label:"车辆类型",align:"center",prop:"carType",width:"100"}),e(m,{label:"场库名称",align:"center",prop:"parkingName",width:"150"}),e(m,{label:"入场时间",align:"center",prop:"inDateTime",width:"160"},{default:l(a=>[k("span",null,p(t(j)(a.row.inDateTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(m,{label:"入场通道",align:"center",prop:"inChannelName",width:"120"}),e(m,{label:"出场时间",align:"center",prop:"outDateTime",width:"160"},{default:l(a=>[a.row.outDateTime?(u(),h("span",Le,p(t(j)(a.row.outDateTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)):(u(),h("span",Qe,"未出场"))]),_:1}),e(m,{label:"出场通道",align:"center",prop:"outChannelName",width:"120"},{default:l(a=>[a.row.outChannelName?(u(),h("span",Ye,p(a.row.outChannelName),1)):(u(),h("span",je,"-"))]),_:1}),e(m,{label:"停车时长",align:"center",prop:"parkingDurationText",width:"120"}),e(m,{label:"停车费用",align:"center",prop:"money",width:"100"},{default:l(a=>[k("span",Ae,"¥"+p(a.row.money||"0.00"),1)]),_:1}),e(m,{label:"支付类型",align:"center",prop:"payTypeName",width:"100"}),e(m,{label:"状态",align:"center",prop:"statusName",width:"80"},{default:l(a=>[e(z,{type:a.row.status===0?"warning":"success"},{default:l(()=>[r(p(a.row.statusName),1)]),_:2},1032,["type"])]),_:1}),e(m,{label:"最后更新",align:"center",prop:"lastUpdate",width:"160"},{default:l(a=>[k("span",null,p(t(j)(a.row.lastUpdate,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(m,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"200"},{default:l(a=>[C((u(),y(b,{link:"",type:"primary",icon:"View",onClick:Pe=>pe(a.row)},{default:l(()=>[r("查看")]),_:2},1032,["onClick"])),[[U,["order:gateParkingInfo:query"]]]),C((u(),y(b,{link:"",type:"primary",icon:"Delete",onClick:Pe=>X(a.row)},{default:l(()=>[r("删除")]),_:2},1032,["onClick"])),[[U,["order:gateParkingInfo:remove"]]])]),_:1})]),_:1},8,["data"])),[[Ve,M.value]]),e(ne,{total:J.value,"current-page":t(d).pageNum,"onUpdate:currentPage":n[6]||(n[6]=a=>t(d).pageNum=a),"page-size":t(d).pageSize,"onUpdate:pageSize":n[7]||(n[7]=a=>t(d).pageSize=a),onPagination:x},null,8,["total","current-page","page-size"]),e(Ne,{title:q.value,modelValue:V.value,"onUpdate:modelValue":n[15]||(n[15]=a=>V.value=a),width:f.value?"1200px":"600px","append-to-body":""},{footer:l(()=>[k("div",nl,[e(b,{onClick:_e},{default:l(()=>[r(p(f.value?"关 闭":"取 消"),1)]),_:1}),f.value?G("",!0):(u(),y(b,{key:0,type:"primary",onClick:ce},{default:l(()=>[r("确 定")]),_:1}))])]),default:l(()=>[f.value?(u(),h("div",Ke,[e(S,{gutter:20},{default:l(()=>[e(g,{span:14},{default:l(()=>[e(be,{title:"车辆出入场记录详情",column:2,border:""},{default:l(()=>[e(w,{label:"记录ID"},{default:l(()=>[r(p(t(o).id),1)]),_:1}),e(w,{label:"车牌号"},{default:l(()=>[e(z,{type:Z(t(o).plateNum),color:ee(t(o).plateNum),effect:"plain"},{default:l(()=>[r(p(t(o).plateNum),1)]),_:1},8,["type","color"])]),_:1}),e(w,{label:"车辆类型"},{default:l(()=>[r(p(t(o).carType),1)]),_:1}),e(w,{label:"场库名称"},{default:l(()=>[r(p(t(o).parkingName),1)]),_:1}),e(w,{label:"入场时间"},{default:l(()=>[r(p(t(o).inDateTime),1)]),_:1}),e(w,{label:"入场通道"},{default:l(()=>[r(p(t(o).inChannelName),1)]),_:1}),e(w,{label:"出场时间"},{default:l(()=>[r(p(t(o).outDateTime||"未出场"),1)]),_:1}),e(w,{label:"出场通道"},{default:l(()=>[r(p(t(o).outChannelName||"未出场"),1)]),_:1}),e(w,{label:"停车费用"},{default:l(()=>[k("span",We,p(t(o).money?`¥${t(o).money}`:"¥0.00"),1)]),_:1}),e(w,{label:"状态"},{default:l(()=>[e(z,{type:t(o).status===0?"warning":"success"},{default:l(()=>[r(p(t(o).status===0?"在场":"离场"),1)]),_:1},8,["type"])]),_:1})]),_:1})]),_:1}),e(g,{span:10},{default:l(()=>[k("div",He,[Je,k("div",Xe,[t(o).inPic?(u(),h("div",Ze,[el,e(ae,{src:t(o).inPic,fit:"cover",style:{width:"100%",height:"200px","border-radius":"4px"},"preview-src-list":[t(o).inPic],"preview-teleported":""},null,8,["src","preview-src-list"])])):G("",!0),t(o).outPic?(u(),h("div",ll,[al,e(ae,{src:t(o).outPic,fit:"cover",style:{width:"100%",height:"200px","border-radius":"4px"},"preview-src-list":[t(o).outPic],"preview-teleported":""},null,8,["src","preview-src-list"])])):G("",!0),!t(o).inPic&&!t(o).outPic?(u(),h("div",tl,[e(we,{description:"暂无照片"})])):G("",!0)])])]),_:1})]),_:1})])):(u(),y(le,{key:1,ref:"gateParkingInfoRef",model:t(o),rules:t(ie),"label-width":"80px"},{default:l(()=>[e(S,null,{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"车牌号",prop:"plateNum"},{default:l(()=>[e(P,{modelValue:t(o).plateNum,"onUpdate:modelValue":n[8]||(n[8]=a=>t(o).plateNum=a),placeholder:"请输入车牌号",disabled:f.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"车辆类型",prop:"carType"},{default:l(()=>[e(D,{modelValue:t(o).carType,"onUpdate:modelValue":n[9]||(n[9]=a=>t(o).carType=a),placeholder:"请选择车辆类型",disabled:f.value},{default:l(()=>[(u(!0),h(O,null,E(Q.value,a=>(u(),y(I,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(S,null,{default:l(()=>[e(g,{span:24},{default:l(()=>[e(_,{label:"场库",prop:"parkingId"},{default:l(()=>[e(D,{modelValue:t(o).parkingId,"onUpdate:modelValue":n[10]||(n[10]=a=>t(o).parkingId=a),placeholder:"请选择场库",disabled:f.value},{default:l(()=>[(u(!0),h(O,null,E(L.value,a=>(u(),y(I,{key:a.id,label:a.warehouseName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(S,null,{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"入场通道",prop:"inChannelName"},{default:l(()=>[e(P,{modelValue:t(o).inChannelName,"onUpdate:modelValue":n[11]||(n[11]=a=>t(o).inChannelName=a),placeholder:"请输入入场通道",disabled:f.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"出场通道",prop:"outChannelName"},{default:l(()=>[e(P,{modelValue:t(o).outChannelName,"onUpdate:modelValue":n[12]||(n[12]=a=>t(o).outChannelName=a),placeholder:"请输入出场通道",disabled:f.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(S,null,{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"停车费用",prop:"money"},{default:l(()=>[e(ke,{modelValue:t(o).money,"onUpdate:modelValue":n[13]||(n[13]=a=>t(o).money=a),precision:2,min:0,disabled:f.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"状态",prop:"status"},{default:l(()=>[e(D,{modelValue:t(o).status,"onUpdate:modelValue":n[14]||(n[14]=a=>t(o).status=a),placeholder:"请选择状态",disabled:f.value},{default:l(()=>[e(I,{label:"在场",value:0}),e(I,{label:"已出场",value:1})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]))]),_:1},8,["title","modelValue","width"])])}}}),sl=Ie(ul,[["__scopeId","data-v-ca1f19be"]]);export{sl as default};
