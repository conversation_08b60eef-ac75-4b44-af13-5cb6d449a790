import{v as V,O as fe,d as ge,r as v,C as ve,T as _e,e as n,Q as F,c as N,o as u,R as C,f as t,S as ye,l as a,h as l,m as Ce,L as I,M as L,j as c,n as s,P as Q,k as he,i as K,t as O}from"./index-Cox2ohSR.js";import{C as M}from"./index-Cy2yB1cz.js";function be(p){return V({url:"/system/advertConfig/list",method:"get",params:p})}function we(p){return V({url:"/system/advertConfig/"+p,method:"get"})}function Ve(p){return V({url:"/system/advertConfig",method:"post",data:p})}function ke(p){return V({url:"/system/advertConfig",method:"put",data:p})}function Ue(p){return V({url:"/system/advertConfig/"+p,method:"delete"})}const Se={class:"app-container"},Te={class:"dialog-footer"},xe=fe({name:"AdvertConfig"}),Re=Object.assign(xe,{components:{CustomPagination:M}},{setup(p){const{proxy:f}=ge(),{advert_status:S}=f.useDict("advert_status"),$=v([]),_=v(!1),T=v(!0),k=v(!0),x=v([]),z=v(!0),A=v(!0),q=v(0),D=v(""),G=ve({form:{},queryParams:{pageNum:1,pageSize:10,advertTitle:void 0,status:void 0},rules:{advertTitle:[{required:!0,message:"广告标题不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:d,form:r,rules:H}=_e(G);function h(){T.value=!0,be(d.value).then(i=>{$.value=i.rows,q.value=i.total,T.value=!1})}function J(){_.value=!1,P()}function P(){r.value={id:void 0,advertTitle:void 0,url:void 0,picUrl:void 0,status:1,remark:void 0},f.resetForm("advertConfigRef")}function R(){d.value.pageNum=1,h()}function W(){f.resetForm("queryRef"),R()}function X(i){x.value=i.map(o=>o.id),z.value=i.length!=1,A.value=!i.length}function Y(){P(),_.value=!0,D.value="添加广告配置信息"}function j(i){P();const o=i.id||x.value;we(o).then(b=>{r.value=b.data,_.value=!0,D.value="修改广告配置信息"})}function Z(){f.$refs.advertConfigRef.validate(i=>{i&&(r.value.id!=null?ke(r.value).then(o=>{f.$modal.msgSuccess("修改成功"),_.value=!1,h()}):Ve(r.value).then(o=>{f.$modal.msgSuccess("新增成功"),_.value=!1,h()}))})}function B(i){const o=i.id||x.value;f.$modal.confirm('是否确认删除广告配置信息编号为"'+o+'"的数据项？').then(function(){return Ue(o)}).then(()=>{h(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(){f.download("system/advertConfig/export",{...d.value},`advertConfig_${new Date().getTime()}.xlsx`)}return h(),(i,o)=>{const b=n("el-input"),y=n("el-form-item"),te=n("el-option"),le=n("el-select"),m=n("el-button"),E=n("el-form"),U=n("el-col"),ae=n("right-toolbar"),oe=n("el-row"),g=n("el-table-column"),ne=n("image-preview"),re=n("dict-tag"),ie=n("el-table"),ue=n("image-upload"),de=n("el-radio"),se=n("el-radio-group"),pe=n("el-dialog"),w=F("hasPermi"),me=F("loading");return u(),N("div",Se,[C(t(E,{model:a(d),ref:"queryRef",inline:!0},{default:l(()=>[t(y,{label:"广告标题",prop:"advertTitle"},{default:l(()=>[t(b,{modelValue:a(d).advertTitle,"onUpdate:modelValue":o[0]||(o[0]=e=>a(d).advertTitle=e),placeholder:"请输入广告标题",clearable:"",style:{width:"200px"},onKeyup:Ce(R,["enter"])},null,8,["modelValue"])]),_:1}),t(y,{label:"状态",prop:"status"},{default:l(()=>[t(le,{modelValue:a(d).status,"onUpdate:modelValue":o[1]||(o[1]=e=>a(d).status=e),placeholder:"广告状态",clearable:"",style:{width:"200px"}},{default:l(()=>[(u(!0),N(I,null,L(a(S),e=>(u(),c(te,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,null,{default:l(()=>[t(m,{type:"primary",icon:"Search",onClick:R},{default:l(()=>[s("搜索")]),_:1}),t(m,{icon:"Refresh",onClick:W},{default:l(()=>[s("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ye,a(k)]]),t(oe,{gutter:10,class:"mb8"},{default:l(()=>[t(U,{span:1.5},{default:l(()=>[C((u(),c(m,{type:"primary",plain:"",icon:"Plus",onClick:Y},{default:l(()=>[s("新增")]),_:1})),[[w,["system:advertConfig:add"]]])]),_:1}),t(U,{span:1.5},{default:l(()=>[C((u(),c(m,{type:"success",plain:"",icon:"Edit",disabled:a(z),onClick:j},{default:l(()=>[s("修改")]),_:1},8,["disabled"])),[[w,["system:advertConfig:edit"]]])]),_:1}),t(U,{span:1.5},{default:l(()=>[C((u(),c(m,{type:"danger",plain:"",icon:"Delete",disabled:a(A),onClick:B},{default:l(()=>[s("删除")]),_:1},8,["disabled"])),[[w,["system:advertConfig:remove"]]])]),_:1}),t(U,{span:1.5},{default:l(()=>[C((u(),c(m,{type:"warning",plain:"",icon:"Download",onClick:ee},{default:l(()=>[s("导出")]),_:1})),[[w,["system:advertConfig:export"]]])]),_:1}),t(ae,{showSearch:a(k),"onUpdate:showSearch":o[2]||(o[2]=e=>Q(k)?k.value=e:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),C((u(),c(ie,{data:a($),onSelectionChange:X},{default:l(()=>[t(g,{type:"selection",width:"55",align:"center"}),t(g,{label:"广告ID",align:"center",prop:"id"}),t(g,{label:"广告标题",align:"center",prop:"advertTitle","show-overflow-tooltip":!0}),t(g,{label:"跳转链接",align:"center",prop:"url","show-overflow-tooltip":!0}),t(g,{label:"图片地址",align:"center",prop:"picUrl",width:"100"},{default:l(e=>[e.row.picUrl?(u(),c(ne,{key:0,src:e.row.picUrl,width:50,height:50},null,8,["src"])):he("",!0)]),_:1}),t(g,{label:"状态",align:"center",prop:"status"},{default:l(e=>[t(re,{options:a(S),value:e.row.status},null,8,["options","value"])]),_:1}),t(g,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(e=>[K("span",null,O(i.parseTime(e.row.createTime)),1)]),_:1}),t(g,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),t(g,{label:"操作",width:"180",align:"center","class-name":"small-padding fixed-width"},{default:l(e=>[C((u(),c(m,{link:"",type:"primary",icon:"Edit",onClick:ce=>j(e.row)},{default:l(()=>[s("修改")]),_:2},1032,["onClick"])),[[w,["system:advertConfig:edit"]]]),C((u(),c(m,{link:"",type:"primary",icon:"Delete",onClick:ce=>B(e.row)},{default:l(()=>[s("删除")]),_:2},1032,["onClick"])),[[w,["system:advertConfig:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,a(T)]]),t(M,{total:a(q),"current-page":a(d).pageNum,"onUpdate:currentPage":o[3]||(o[3]=e=>a(d).pageNum=e),"page-size":a(d).pageSize,"onUpdate:pageSize":o[4]||(o[4]=e=>a(d).pageSize=e),onPagination:h},null,8,["total","current-page","page-size"]),t(pe,{title:a(D),modelValue:a(_),"onUpdate:modelValue":o[10]||(o[10]=e=>Q(_)?_.value=e:null),width:"500px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[K("div",Te,[t(m,{type:"primary",onClick:Z},{default:l(()=>[s("确 定")]),_:1}),t(m,{onClick:J},{default:l(()=>[s("取 消")]),_:1})])]),default:l(()=>[t(E,{ref:"advertConfigRef",model:a(r),rules:a(H),"label-width":"80px"},{default:l(()=>[t(y,{label:"广告标题",prop:"advertTitle"},{default:l(()=>[t(b,{modelValue:a(r).advertTitle,"onUpdate:modelValue":o[5]||(o[5]=e=>a(r).advertTitle=e),placeholder:"请输入广告标题"},null,8,["modelValue"])]),_:1}),t(y,{label:"跳转链接",prop:"url"},{default:l(()=>[t(b,{modelValue:a(r).url,"onUpdate:modelValue":o[6]||(o[6]=e=>a(r).url=e),placeholder:"请输入跳转链接"},null,8,["modelValue"])]),_:1}),t(y,{label:"图片地址",prop:"picUrl"},{default:l(()=>[t(ue,{modelValue:a(r).picUrl,"onUpdate:modelValue":o[7]||(o[7]=e=>a(r).picUrl=e),limit:1,"file-size":5,"file-type":["png","jpg","jpeg","gif","webp"]},null,8,["modelValue"])]),_:1}),t(y,{label:"状态",prop:"status"},{default:l(()=>[t(se,{modelValue:a(r).status,"onUpdate:modelValue":o[8]||(o[8]=e=>a(r).status=e)},{default:l(()=>[(u(!0),N(I,null,L(a(S),e=>(u(),c(de,{key:e.value,value:parseInt(e.value)},{default:l(()=>[s(O(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"备注",prop:"remark"},{default:l(()=>[t(b,{modelValue:a(r).remark,"onUpdate:modelValue":o[9]||(o[9]=e=>a(r).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Re as default};
