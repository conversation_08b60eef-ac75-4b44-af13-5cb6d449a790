[project]
name = "parking-build-tool"
version = "1.0.0"
description = "停车场管理系统自动化构建工具"
authors = [
    {name = "DevOps Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "rich>=13.0.0",
    "inquirer>=3.0.0",
    "pydantic>=2.0.0",
    "pyyaml>=6.0.0",
    "pathlib2>=2.3.0",
]

[project.scripts]
build-manager = "build_manager:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]
