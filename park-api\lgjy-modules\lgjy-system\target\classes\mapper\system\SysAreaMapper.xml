<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.SysAreaMapper">
    
    <resultMap type="SysArea" id="SysAreaResult">
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="areaLevel"    column="area_level"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysAreaVo">
        select area_code, area_name, parent_code, area_level, sort_order, status, create_time, update_time from sys_area
    </sql>

    <select id="selectSysAreaList" parameterType="SysArea" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        <where>  
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="parentCode != null  and parentCode != ''"> and parent_code = #{parentCode}</if>
            <if test="areaLevel != null "> and area_level = #{areaLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by area_level, sort_order
    </select>
    
    <select id="selectSysAreaByAreaCode" parameterType="String" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        where area_code = #{areaCode}
    </select>

    <select id="selectSysAreaByParentCode" parameterType="String" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        where parent_code = #{parentCode} and status = '0'
        order by sort_order
    </select>

    <select id="selectSysAreaByLevel" parameterType="Integer" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        where area_level = #{areaLevel} and status = '0'
        order by sort_order
    </select>
        
    <insert id="insertSysArea" parameterType="SysArea">
        insert into sys_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="areaLevel != null">area_level,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="areaLevel != null">#{areaLevel},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysArea" parameterType="SysArea">
        update sys_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="areaLevel != null">area_level = #{areaLevel},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where area_code = #{areaCode}
    </update>

    <delete id="deleteSysAreaByAreaCode" parameterType="String">
        delete from sys_area where area_code = #{areaCode}
    </delete>

    <delete id="deleteSysAreaByAreaCodes" parameterType="String">
        delete from sys_area where area_code in 
        <foreach item="areaCode" collection="array" open="(" separator="," close=")">
            #{areaCode}
        </foreach>
    </delete>

</mapper>
