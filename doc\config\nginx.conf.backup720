
#user  nobody;
worker_processes  24;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;
#error_log  "pipe:rollback logs/error_log interval=1d baknum=7 maxsize=2G";

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;
    #access_log  "pipe:rollback logs/access_log interval=1d baknum=7 maxsize=2G"  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server {
        listen       81;
        server_name  localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;
        #access_log  "pipe:rollback logs/host.access_log interval=1d baknum=7 maxsize=2G"  main;

        location / {
            root   html;
            index  index.html index.htm;
        }

        include /usr/local/tengine/conf.d/*.conf;

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # pass the Dubbo rpc to Dubbo provider server listening on 127.0.0.1:20880
        #
        #location /dubbo {
        #    dubbo_pass_all_headers on;
        #    dubbo_pass_set args $args;
        #    dubbo_pass_set uri $uri;
        #    dubbo_pass_set method $request_method;
        #
        #    dubbo_pass org.apache.dubbo.samples.tengine.DemoService 0.0.0 tengineDubbo dubbo_backend;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }

    # upstream for Dubbo rpc to Dubbo provider server listening on 127.0.0.1:20880
    #
    #upstream dubbo_backend {
    #    multi 1;
    #    server 127.0.0.1:20880;
    #}

    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

    # 充电后台
    upstream lgjy-api-route {
        server ************:48101 weight=200 max_fails=1 fail_timeout=3;
        server ************:48101 weight=200 max_fails=1 fail_timeout=3 backup;
    }

    upstream lgjy-api-route-cloud {
        server ************:48101 weight=200 max_fails=3 fail_timeout=10s;
        server ************:48101 weight=200 max_fails=3 fail_timeout=10s;
        server ************:48201 weight=200 max_fails=3 fail_timeout=10s;
        server ************:48201 weight=200 max_fails=3 fail_timeout=10s;
        server ************:48202 weight=200 max_fails=3 fail_timeout=10s;
        server ************:48202 weight=200 max_fails=3 fail_timeout=10s;
    }

   upstream lgjy-open-api-route {
         server ************:8209 weight=200 max_fails=1 fail_timeout=3;
         #先用一个节点     
         #server ************:8209 weight=200 max_fails=1 fail_timeout=3;

   }

    # HTTPS server
    
    server {
        listen       443 ssl;
        listen [::]:443 default ssl;
        server_name  pile-charge.lgfw24hours.com test-parknew.lgfw24hours.com;
        client_max_body_size               500m;

       # ssl_certificate /home/<USER>/saas/app/staticFile/cert/scs1745548351514_pile-charge-test.lgfw24hours.com_server.crt;
       # ssl_certificate_key /home/<USER>/saas/app/staticFile/cert/scs1745548351514_pile-charge-test.lgfw24hours.com_server.key;
        
        ssl_certificate /home/<USER>/saas/app/staticFile/cert/scs1746596139916_pile-charge.lgfw24hours.com_server.crt;
        ssl_certificate_key /home/<USER>/saas/app/staticFile/cert/scs1746596139916_pile-charge.lgfw24hours.com_server.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        #ssl_ciphers  HIGH:!aNULL:!MD5;
        #ssl_prefer_server_ciphers  on;

        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;

        add_header Referrer-Policy "strict-origin-when-cross-origin";
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains;";
#        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Permitted-Cross-Domain-Policies "none";
        add_header X-Frame-Options "DENY";
        add_header X-Download-Options "noopen" always;

    
        if ($http_host !~* ^(pile-charge\.lgfw24hours\.com|pile-charge\.lgfw24hours\.com\:2443|www\.pile-charge\.lgfw24hours\.com\:2443|test-parknew\.lgfw24hours\.com\:3443)$) {
            return 403;
        }

        # Jenkins代理配置
        location /jenkins/ {
            proxy_pass https://127.0.0.1:9443/jenkins/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # Jenkins特殊配置
            proxy_buffering off;
            proxy_request_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # 超时设置
            proxy_connect_timeout 150s;
            proxy_send_timeout 100s;
            proxy_read_timeout 100s;
        }

        # 停车管理系统代理配置
        location / {
            # 如果是test-parknew域名，代理到Docker nginx
            if ($host = "test-parknew.lgfw24hours.com") {
                proxy_pass https://127.0.0.1:9443;
                break;
            }

            # 原有的CORS配置（只对原域名生效）
            add_header 'Access-Control-Allow-Origin' 'pile-charge-test.lgfw24hours.com:3443';

            # 允许的HTTP方法
            add_header 'Access-Control-Allow-Methods' 'GET, POST, DELETE, PUT, OPTIONS';

            # 允许的请求头
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

            # 允许携带凭证（如Cookies）
            add_header 'Access-Control-Allow-Credentials' 'true';

            # 对预检请求直接返回204
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }

        location /lg-front {
            root  /home/<USER>/saas/frontend;
            index  index.html;
            try_files $uri $uri/ /lg-front/index.html;
        }


        include /usr/local/tengine/conf.d/*.conf;

    }
	server {
        listen       443 ssl;
        listen [::]:443 ssl;
       	server_name  ticket.lgfw24hours.com;
        client_max_body_size  500m;

        ssl_certificate /home/<USER>/cert/ticket.lgfw24hours.com.pem;
        ssl_certificate_key /home/<USER>/cert/ticket.lgfw24hours.com.key;
        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;
        ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;

        add_header Referrer-Policy "strict-origin-when-cross-origin";
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains;";
        add_header X-Permitted-Cross-Domain-Policies "none";
        add_header X-Frame-Options "DENY";
        add_header X-Download-Options "noopen" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; object-src 'none';";
        
        if ($host !~* ^(ticket\.lgfw24hours\.com)$ ) {
          return 403;
        }

        location / {

            root /home/<USER>/frontend/dist;
            index index.html;
            try_files $uri $uri/ /index.html;

        }


        location /prod-api/{

            proxy_pass http://localhost:8080/;
            proxy_redirect default;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            add_header 'Access-Control-Allow-Origin' '$http_origin';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization';
            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }
       }
}

stream {
    upstream yinweineng_server {
        hash $remote_addr consistent;
        #least_conn;
        server ************:47001 weight=100 max_fails=1 fail_timeout=3;
        server ************:47001 weight=100 max_fails=1 fail_timeout=3;
    }

    upstream yuezhichen_server {
        hash $remote_addr consistent;
        server ************:47002 weight=100 max_fails=1 fail_timeout=3;
        server ************:47002 weight=100 max_fails=1 fail_timeout=3;
    }

    # 因威能源协议
    server {
        listen       6001;
        proxy_pass yinweineng_server;
        
        proxy_connect_timeout 5s;
        proxy_timeout 300s;
        tcp_nodelay on;  # 禁用Nagle算法
        proxy_buffer_size 4k;
  
        proxy_next_upstream on;
        proxy_next_upstream_tries 3;        # 转发尝试请求最多3次
        proxy_next_upstream_timeout 10s;    # 总尝试超时时间为10s
        proxy_socket_keepalive off;  # 开启SO_KEEPALIVE选项进行心跳检测
     }

     # 悦智辰协议
     server {
        listen       6002;
        proxy_pass yuezhichen_server;

        proxy_connect_timeout 5s;
        proxy_timeout 300s;
        tcp_nodelay on;  # 禁用Nagle算法
        proxy_buffer_size 4k;
  
        proxy_next_upstream on;
        proxy_next_upstream_tries 3;        # 转发尝试请求最多3次
        proxy_next_upstream_timeout 10s;    # 总尝试超时时间为10s
        proxy_socket_keepalive off;  # 开启SO_KEEPALIVE选项进行心跳检测
     }

}
