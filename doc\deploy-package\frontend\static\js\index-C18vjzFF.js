import{ai as e,_ as Le,r as E,e as u,c as b,o as i,f as s,i as p,h as t,l as o,a2 as Oe,P as W,L as B,M as z,U as Ae,t as Q,O as he,d as ye,C as be,T as ke,Q as oe,R as N,k as T,S as we,m as xe,j as r,n as a,aj as _e,D as Ce}from"./index-Cox2ohSR.js";import{l as le,g as Ne,d as Ue,u as Se,a as qe}from"./menu-z-nZXjKw.js";let F=[];const Fe=Object.assign({"../../assets/icons/svg/404.svg":()=>e(()=>import("./404-Dy3nURRX.js"),[]),"../../assets/icons/svg/barrier.svg":()=>e(()=>import("./barrier-B9qThlX9.js"),[]),"../../assets/icons/svg/blacklist.svg":()=>e(()=>import("./blacklist-CktHVKZX.js"),[]),"../../assets/icons/svg/bug.svg":()=>e(()=>import("./bug-10dePVta.js"),[]),"../../assets/icons/svg/build.svg":()=>e(()=>import("./build-2jMyI6eP.js"),[]),"../../assets/icons/svg/button.svg":()=>e(()=>import("./button-BlSCM_GH.js"),[]),"../../assets/icons/svg/cascader.svg":()=>e(()=>import("./cascader-CXIOcY1C.js"),[]),"../../assets/icons/svg/chart.svg":()=>e(()=>import("./chart-BsLMrzXU.js"),[]),"../../assets/icons/svg/checkbox.svg":()=>e(()=>import("./checkbox-Bpiun3bf.js"),[]),"../../assets/icons/svg/client.svg":()=>e(()=>import("./client-BuZkq35_.js"),[]),"../../assets/icons/svg/clipboard.svg":()=>e(()=>import("./clipboard-DaV3cn7f.js"),[]),"../../assets/icons/svg/code.svg":()=>e(()=>import("./code-DgJ8cT4a.js"),[]),"../../assets/icons/svg/color.svg":()=>e(()=>import("./color-y1Sshoou.js"),[]),"../../assets/icons/svg/component.svg":()=>e(()=>import("./component-Djp9s69L.js"),[]),"../../assets/icons/svg/coupon.svg":()=>e(()=>import("./coupon-B9I8rPdt.js"),[]),"../../assets/icons/svg/dashboard.svg":()=>e(()=>import("./dashboard-Dy7qt_a2.js"),[]),"../../assets/icons/svg/date-range.svg":()=>e(()=>import("./date-range-B8MgYLb1.js"),[]),"../../assets/icons/svg/date.svg":()=>e(()=>import("./date-B1FSITvi.js"),[]),"../../assets/icons/svg/dict.svg":()=>e(()=>import("./dict-Bi_GqSXR.js"),[]),"../../assets/icons/svg/documentation.svg":()=>e(()=>import("./documentation-uH9BvL5U.js"),[]),"../../assets/icons/svg/download.svg":()=>e(()=>import("./download-DeIzgQWH.js"),[]),"../../assets/icons/svg/drag.svg":()=>e(()=>import("./drag-BG1_I1vT.js"),[]),"../../assets/icons/svg/druid.svg":()=>e(()=>import("./druid-BybW_S_B.js"),[]),"../../assets/icons/svg/edit.svg":()=>e(()=>import("./edit-D0DI9pAq.js"),[]),"../../assets/icons/svg/education.svg":()=>e(()=>import("./education-47KsSYIl.js"),[]),"../../assets/icons/svg/email.svg":()=>e(()=>import("./email-Dig28Vt2.js"),[]),"../../assets/icons/svg/enter.svg":()=>e(()=>import("./enter-KOZ0bgqJ.js"),[]),"../../assets/icons/svg/example.svg":()=>e(()=>import("./example-CnLLAFb9.js"),[]),"../../assets/icons/svg/excel.svg":()=>e(()=>import("./excel-D3hj5F35.js"),[]),"../../assets/icons/svg/exception-order.svg":()=>e(()=>import("./exception-order-zXWsEIMw.js"),[]),"../../assets/icons/svg/exit-fullscreen.svg":()=>e(()=>import("./exit-fullscreen-dXhGKlQm.js"),[]),"../../assets/icons/svg/eye-open.svg":()=>e(()=>import("./eye-open-BxlshWqB.js"),[]),"../../assets/icons/svg/eye.svg":()=>e(()=>import("./eye-DqRz4sMZ.js"),[]),"../../assets/icons/svg/form.svg":()=>e(()=>import("./form-BDTA_i-I.js"),[]),"../../assets/icons/svg/fullscreen.svg":()=>e(()=>import("./fullscreen-0JHt5yWX.js"),[]),"../../assets/icons/svg/gift.svg":()=>e(()=>import("./gift-BAJIo7Yy.js"),[]),"../../assets/icons/svg/github.svg":()=>e(()=>import("./github-AJ0WQBa2.js"),[]),"../../assets/icons/svg/guide.svg":()=>e(()=>import("./guide-DZWUPi2j.js"),[]),"../../assets/icons/svg/icon.svg":()=>e(()=>import("./icon-BtMv6Od8.js"),[]),"../../assets/icons/svg/input.svg":()=>e(()=>import("./input-BJoPMnBW.js"),[]),"../../assets/icons/svg/international.svg":()=>e(()=>import("./international-CmzG1OHg.js"),[]),"../../assets/icons/svg/job.svg":()=>e(()=>import("./job-BcmuINx7.js"),[]),"../../assets/icons/svg/key.svg":()=>e(()=>import("./key-CFEplPii.js"),[]),"../../assets/icons/svg/language.svg":()=>e(()=>import("./language-CaW1LMEk.js"),[]),"../../assets/icons/svg/link.svg":()=>e(()=>import("./link-C93f4PgI.js"),[]),"../../assets/icons/svg/list.svg":()=>e(()=>import("./list-C7O8B4zW.js"),[]),"../../assets/icons/svg/lock.svg":()=>e(()=>import("./lock-Bexeb9hp.js"),[]),"../../assets/icons/svg/log.svg":()=>e(()=>import("./log-CF2F-nSs.js"),[]),"../../assets/icons/svg/logininfor.svg":()=>e(()=>import("./logininfor-Bm9ZYYR7.js"),[]),"../../assets/icons/svg/member-management.svg":()=>e(()=>import("./member-management-CY7ADhmH.js"),[]),"../../assets/icons/svg/merchant-info.svg":()=>e(()=>import("./merchant-info-Ci_fxEXP.js"),[]),"../../assets/icons/svg/merchant-management.svg":()=>e(()=>import("./merchant-management-DV0mSbU5.js"),[]),"../../assets/icons/svg/merchant-member.svg":()=>e(()=>import("./merchant-member-C20wLrr-.js"),[]),"../../assets/icons/svg/merchant-package.svg":()=>e(()=>import("./merchant-package-Bp35SGPB.js"),[]),"../../assets/icons/svg/merchant-voucher.svg":()=>e(()=>import("./merchant-voucher-CG5cemV2.js"),[]),"../../assets/icons/svg/message.svg":()=>e(()=>import("./message-UkR-VIBB.js"),[]),"../../assets/icons/svg/money.svg":()=>e(()=>import("./money-B1qqPuhn.js"),[]),"../../assets/icons/svg/monitor.svg":()=>e(()=>import("./monitor-gwnnVq4l.js"),[]),"../../assets/icons/svg/moon.svg":()=>e(()=>import("./moon-BOcjHwCq.js"),[]),"../../assets/icons/svg/more-up.svg":()=>e(()=>import("./more-up-u2qZwiNm.js"),[]),"../../assets/icons/svg/nacos.svg":()=>e(()=>import("./nacos-CmARyran.js"),[]),"../../assets/icons/svg/nested.svg":()=>e(()=>import("./nested-B4d5u3hW.js"),[]),"../../assets/icons/svg/number.svg":()=>e(()=>import("./number-D4hB_nHC.js"),[]),"../../assets/icons/svg/online.svg":()=>e(()=>import("./online-C2ZP8pdY.js"),[]),"../../assets/icons/svg/operation-management.svg":()=>e(()=>import("./operation-management-vmX6v810.js"),[]),"../../assets/icons/svg/operation.svg":()=>e(()=>import("./operation-CpwrIl8X.js"),[]),"../../assets/icons/svg/parking-order.svg":()=>e(()=>import("./parking-order-Cp77BstI.js"),[]),"../../assets/icons/svg/parking.svg":()=>e(()=>import("./parking-DNhccjqY.js"),[]),"../../assets/icons/svg/password.svg":()=>e(()=>import("./password-DfGvqQpB.js"),[]),"../../assets/icons/svg/pdf.svg":()=>e(()=>import("./pdf-CD9mOGjJ.js"),[]),"../../assets/icons/svg/people.svg":()=>e(()=>import("./people-CdGMHN63.js"),[]),"../../assets/icons/svg/peoples.svg":()=>e(()=>import("./peoples-BRYsIqmI.js"),[]),"../../assets/icons/svg/phone.svg":()=>e(()=>import("./phone-BpAUIz0g.js"),[]),"../../assets/icons/svg/platform-management.svg":()=>e(()=>import("./platform-management-nmL8XvoT.js"),[]),"../../assets/icons/svg/platform.svg":()=>e(()=>import("./platform-KqjQTeZS.js"),[]),"../../assets/icons/svg/post.svg":()=>e(()=>import("./post-DrLDyPY9.js"),[]),"../../assets/icons/svg/qq.svg":()=>e(()=>import("./qq-D8j4O83Y.js"),[]),"../../assets/icons/svg/question.svg":()=>e(()=>import("./question-CvYWQbyW.js"),[]),"../../assets/icons/svg/radio.svg":()=>e(()=>import("./radio-B0t9wPBQ.js"),[]),"../../assets/icons/svg/rate.svg":()=>e(()=>import("./rate-CgnHQvKS.js"),[]),"../../assets/icons/svg/redis.svg":()=>e(()=>import("./redis-D4ECyT6a.js"),[]),"../../assets/icons/svg/row.svg":()=>e(()=>import("./row-CRXKIHjm.js"),[]),"../../assets/icons/svg/scan-voucher.svg":()=>e(()=>import("./scan-voucher-f9gOHCek.js"),[]),"../../assets/icons/svg/search.svg":()=>e(()=>import("./search-CUfclCsR.js"),[]),"../../assets/icons/svg/select.svg":()=>e(()=>import("./select-DhuHHMxz.js"),[]),"../../assets/icons/svg/sentinel.svg":()=>e(()=>import("./sentinel-sIU9HpHS.js"),[]),"../../assets/icons/svg/server.svg":()=>e(()=>import("./server-unS7EyF7.js"),[]),"../../assets/icons/svg/shopping.svg":()=>e(()=>import("./shopping-CU1IRvxM.js"),[]),"../../assets/icons/svg/size.svg":()=>e(()=>import("./size-Cj9fB5Rp.js"),[]),"../../assets/icons/svg/skill.svg":()=>e(()=>import("./skill-B8f_I4m_.js"),[]),"../../assets/icons/svg/slider.svg":()=>e(()=>import("./slider-BGfehM6X.js"),[]),"../../assets/icons/svg/star.svg":()=>e(()=>import("./star-kST8a72V.js"),[]),"../../assets/icons/svg/sunny.svg":()=>e(()=>import("./sunny-DvkHW8g8.js"),[]),"../../assets/icons/svg/swagger.svg":()=>e(()=>import("./swagger-BHGXZ2Jt.js"),[]),"../../assets/icons/svg/switch.svg":()=>e(()=>import("./switch-CvaargRJ.js"),[]),"../../assets/icons/svg/system.svg":()=>e(()=>import("./system-DcNSH_Fq.js"),[]),"../../assets/icons/svg/tab.svg":()=>e(()=>import("./tab-nA3f0aBt.js"),[]),"../../assets/icons/svg/table.svg":()=>e(()=>import("./table-5PRh60AQ.js"),[]),"../../assets/icons/svg/textarea.svg":()=>e(()=>import("./textarea-CJWXlgbJ.js"),[]),"../../assets/icons/svg/theme.svg":()=>e(()=>import("./theme-CyGq941x.js"),[]),"../../assets/icons/svg/ticket.svg":()=>e(()=>import("./ticket-C1dCS6Da.js"),[]),"../../assets/icons/svg/time-range.svg":()=>e(()=>import("./time-range-D3dxgtLj.js"),[]),"../../assets/icons/svg/time.svg":()=>e(()=>import("./time-BVERp0sU.js"),[]),"../../assets/icons/svg/tool.svg":()=>e(()=>import("./tool-D8kXk1l-.js"),[]),"../../assets/icons/svg/tree-table.svg":()=>e(()=>import("./tree-table-CnOS99I9.js"),[]),"../../assets/icons/svg/tree.svg":()=>e(()=>import("./tree-BCtS3oPD.js"),[]),"../../assets/icons/svg/upload.svg":()=>e(()=>import("./upload-BueI-Il1.js"),[]),"../../assets/icons/svg/user.svg":()=>e(()=>import("./user-DqMuW5cU.js"),[]),"../../assets/icons/svg/validCode.svg":()=>e(()=>import("./validCode-COB1iLxa.js"),[]),"../../assets/icons/svg/vip.svg":()=>e(()=>import("./vip-BB-mzhvw.js"),[]),"../../assets/icons/svg/warning.svg":()=>e(()=>import("./warning-BZS1AYD0.js"),[]),"../../assets/icons/svg/wechat.svg":()=>e(()=>import("./wechat-lmQOcPZu.js"),[]),"../../assets/icons/svg/whitelist.svg":()=>e(()=>import("./whitelist-DKmYcy8m.js"),[]),"../../assets/icons/svg/zip.svg":()=>e(()=>import("./zip-DIOSZc69.js"),[])});for(const $ in Fe){const c=$.split("assets/icons/svg/")[1].split(".svg")[0];F.push(c)}const $e={class:"icon-body"},Me={class:"icon-list"},je={class:"list-container"},Be=["onClick"],ze={__name:"index",props:{activeIcon:{type:String}},emits:["selected"],setup($,{expose:c,emit:K}){const f=E(""),y=E(F),V=K;function k(){y.value=F,f.value&&(y.value=F.filter(O=>O.indexOf(f.value)!==-1))}function w(O){V("selected",O),document.body.click()}function U(){f.value="",y.value=F}return c({reset:U}),(O,x)=>{const S=u("el-icon"),M=u("el-input"),G=u("svg-icon");return i(),b("div",$e,[s(M,{modelValue:o(f),"onUpdate:modelValue":x[0]||(x[0]=g=>W(f)?f.value=g:null),class:"icon-search",clearable:"",placeholder:"请输入图标名称",onClear:k,onInput:k},{suffix:t(()=>[s(S,null,{default:t(()=>[s(o(Oe))]),_:1})]),_:1},8,["modelValue"]),p("div",Me,[p("div",je,[(i(!0),b(B,null,z(o(y),(g,l)=>(i(),b("div",{class:"icon-item-wrapper",key:l,onClick:X=>w(g)},[p("div",{class:Ae(["icon-item",{active:$.activeIcon===g}])},[s(G,{"icon-class":g,"class-name":"icon",style:{height:"25px",width:"16px"}},null,8,["icon-class"]),p("span",null,Q(g),1)],2)],8,Be))),128))])])])}}},Qe=Le(ze,[["__scopeId","data-v-bf9f5a16"]]),Ke={class:"app-container"},Ge={class:"dialog-footer"},He=he({name:"Menu"}),Xe=Object.assign(He,{setup($){const{proxy:c}=ye(),{sys_show_hide:K,sys_normal_disable:f}=c.useDict("sys_show_hide","sys_normal_disable"),y=E([]),V=E(!1),k=E(!0),w=E(!0),U=E(""),O=E([]),x=E(!1),S=E(!0),M=E(null),G=be({form:{},queryParams:{menuName:void 0,visible:void 0},rules:{menuName:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"菜单顺序不能为空",trigger:"blur"}],path:[{required:!0,message:"路由地址不能为空",trigger:"blur"}]}}),{queryParams:g,form:l,rules:X}=ke(G);function C(){k.value=!0,le(g.value).then(d=>{y.value=c.handleTree(d.data,"menuId"),k.value=!1})}function Y(){O.value=[],le().then(d=>{const n={menuId:0,menuName:"主类目",children:[]};n.children=c.handleTree(d.data,"menuId"),O.value.push(n)})}function ne(){V.value=!1,H()}function H(){l.value={menuId:void 0,parentId:0,menuName:void 0,icon:void 0,menuType:"M",orderNum:void 0,isFrame:"1",isCache:"0",visible:"0",status:"0"},c.resetForm("menuRef")}function ae(){M.value.reset()}function ie(d){l.value.icon=d}function J(){C()}function ue(){c.resetForm("queryRef"),J()}function Z(d){H(),Y(),d!=null&&d.menuId?l.value.parentId=d.menuId:l.value.parentId=0,V.value=!0,U.value="添加菜单"}function re(){S.value=!1,x.value=!x.value,Ce(()=>{S.value=!0})}async function de(d){H(),await Y(),Ne(d.menuId).then(n=>{l.value=n.data,V.value=!0,U.value="修改菜单"})}function ve(){c.$refs.menuRef.validate(d=>{d&&(l.value.menuId!=null?Se(l.value).then(n=>{c.$modal.msgSuccess("修改成功"),V.value=!1,C()}):qe(l.value).then(n=>{c.$modal.msgSuccess("新增成功"),V.value=!1,C()}))})}function me(d){c.$modal.confirm('是否确认删除名称为"'+d.menuName+'"的数据项?').then(function(){return Ue(d.menuId)}).then(()=>{C(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}return C(),(d,n)=>{const A=u("el-input"),v=u("el-form-item"),pe=u("el-option"),ce=u("el-select"),R=u("el-button"),ee=u("el-form"),m=u("el-col"),ge=u("right-toolbar"),se=u("el-row"),h=u("el-table-column"),Ee=u("dict-tag"),fe=u("el-table"),Ve=u("el-tree-select"),P=u("el-radio"),q=u("el-radio-group"),Ie=u("search"),I=u("el-icon"),Te=u("el-popover"),Re=u("el-input-number"),D=u("question-filled"),L=u("el-tooltip"),Pe=u("el-dialog"),j=oe("hasPermi"),De=oe("loading");return i(),b("div",Ke,[N(s(ee,{model:o(g),ref:"queryRef",inline:!0},{default:t(()=>[s(v,{label:"菜单名称",prop:"menuName"},{default:t(()=>[s(A,{modelValue:o(g).menuName,"onUpdate:modelValue":n[0]||(n[0]=_=>o(g).menuName=_),placeholder:"请输入菜单名称",clearable:"",style:{width:"200px"},onKeyup:xe(J,["enter"])},null,8,["modelValue"])]),_:1}),s(v,{label:"状态",prop:"status"},{default:t(()=>[s(ce,{modelValue:o(g).status,"onUpdate:modelValue":n[1]||(n[1]=_=>o(g).status=_),placeholder:"菜单状态",clearable:"",style:{width:"200px"}},{default:t(()=>[(i(!0),b(B,null,z(o(f),_=>(i(),r(pe,{key:_.value,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(v,null,{default:t(()=>[s(R,{type:"primary",icon:"Search",onClick:J},{default:t(()=>[a("搜索")]),_:1}),s(R,{icon:"Refresh",onClick:ue},{default:t(()=>[a("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[we,o(w)]]),s(se,{gutter:10,class:"mb8"},{default:t(()=>[s(m,{span:1.5},{default:t(()=>[N((i(),r(R,{type:"primary",plain:"",icon:"Plus",onClick:Z},{default:t(()=>[a("新增")]),_:1})),[[j,["system:menu:add"]]])]),_:1}),s(m,{span:1.5},{default:t(()=>[s(R,{type:"info",plain:"",icon:"Sort",onClick:re},{default:t(()=>[a("展开/折叠")]),_:1})]),_:1}),s(ge,{showSearch:o(w),"onUpdate:showSearch":n[2]||(n[2]=_=>W(w)?w.value=_:null),onQueryTable:C},null,8,["showSearch"])]),_:1}),o(S)?N((i(),r(fe,{key:0,data:o(y),"row-key":"menuId","default-expand-all":o(x),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:t(()=>[s(h,{prop:"menuName",label:"菜单名称","show-overflow-tooltip":!0,width:"160"}),s(h,{prop:"icon",label:"图标",align:"center",width:"100"},{default:t(_=>[s(o(_e),{"icon-class":_.row.icon},null,8,["icon-class"])]),_:1}),s(h,{prop:"orderNum",label:"排序",width:"60"}),s(h,{prop:"perms",label:"权限标识","show-overflow-tooltip":!0}),s(h,{prop:"component",label:"组件路径","show-overflow-tooltip":!0}),s(h,{prop:"status",label:"状态",width:"80"},{default:t(_=>[s(Ee,{options:o(f),value:_.row.status},null,8,["options","value"])]),_:1}),s(h,{label:"创建时间",align:"center",width:"160",prop:"createTime"},{default:t(_=>[p("span",null,Q(d.parseTime(_.row.createTime)),1)]),_:1}),s(h,{label:"操作",align:"center",width:"210","class-name":"small-padding fixed-width"},{default:t(_=>[N((i(),r(R,{link:"",type:"primary",icon:"Edit",onClick:te=>de(_.row)},{default:t(()=>[a("修改")]),_:2},1032,["onClick"])),[[j,["system:menu:edit"]]]),N((i(),r(R,{link:"",type:"primary",icon:"Plus",onClick:te=>Z(_.row)},{default:t(()=>[a("新增")]),_:2},1032,["onClick"])),[[j,["system:menu:add"]]]),N((i(),r(R,{link:"",type:"primary",icon:"Delete",onClick:te=>me(_.row)},{default:t(()=>[a("删除")]),_:2},1032,["onClick"])),[[j,["system:menu:remove"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[De,o(k)]]):T("",!0),s(Pe,{title:o(U),modelValue:o(V),"onUpdate:modelValue":n[17]||(n[17]=_=>W(V)?V.value=_:null),width:"680px","append-to-body":""},{footer:t(()=>[p("div",Ge,[s(R,{type:"primary",onClick:ve},{default:t(()=>[a("确 定")]),_:1}),s(R,{onClick:ne},{default:t(()=>[a("取 消")]),_:1})])]),default:t(()=>[s(ee,{ref:"menuRef",model:o(l),rules:o(X),"label-width":"100px"},{default:t(()=>[s(se,null,{default:t(()=>[s(m,{span:24},{default:t(()=>[s(v,{label:"上级菜单"},{default:t(()=>[s(Ve,{modelValue:o(l).parentId,"onUpdate:modelValue":n[3]||(n[3]=_=>o(l).parentId=_),data:o(O),props:{value:"menuId",label:"menuName",children:"children"},"value-key":"menuId",placeholder:"选择上级菜单","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1}),s(m,{span:24},{default:t(()=>[s(v,{label:"菜单类型",prop:"menuType"},{default:t(()=>[s(q,{modelValue:o(l).menuType,"onUpdate:modelValue":n[4]||(n[4]=_=>o(l).menuType=_)},{default:t(()=>[s(P,{value:"M"},{default:t(()=>[a("目录")]),_:1}),s(P,{value:"C"},{default:t(()=>[a("菜单")]),_:1}),s(P,{value:"F"},{default:t(()=>[a("按钮")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(l).menuType!="F"?(i(),r(m,{key:0,span:12},{default:t(()=>[s(v,{label:"菜单图标",prop:"icon"},{default:t(()=>[s(Te,{placement:"bottom-start",width:540,trigger:"click"},{reference:t(()=>[s(A,{modelValue:o(l).icon,"onUpdate:modelValue":n[5]||(n[5]=_=>o(l).icon=_),placeholder:"点击选择图标",onBlur:ae,readonly:""},{prefix:t(()=>[o(l).icon?(i(),r(o(_e),{key:0,"icon-class":o(l).icon,class:"el-input__icon",style:{height:"32px",width:"16px"}},null,8,["icon-class"])):(i(),r(I,{key:1,style:{height:"32px",width:"16px"}},{default:t(()=>[s(Ie)]),_:1}))]),_:1},8,["modelValue"])]),default:t(()=>[s(o(Qe),{ref_key:"iconSelectRef",ref:M,onSelected:ie,"active-icon":o(l).icon},null,8,["active-icon"])]),_:1})]),_:1})]),_:1})):T("",!0),s(m,{span:12},{default:t(()=>[s(v,{label:"显示排序",prop:"orderNum"},{default:t(()=>[s(Re,{modelValue:o(l).orderNum,"onUpdate:modelValue":n[6]||(n[6]=_=>o(l).orderNum=_),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),s(m,{span:12},{default:t(()=>[s(v,{label:"菜单名称",prop:"menuName"},{default:t(()=>[s(A,{modelValue:o(l).menuName,"onUpdate:modelValue":n[7]||(n[7]=_=>o(l).menuName=_),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1})]),_:1}),o(l).menuType=="C"?(i(),r(m,{key:1,span:12},{default:t(()=>[s(v,{prop:"routeName"},{label:t(()=>[p("span",null,[s(L,{content:"默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 路由名称 ")])]),default:t(()=>[s(A,{modelValue:o(l).routeName,"onUpdate:modelValue":n[8]||(n[8]=_=>o(l).routeName=_),placeholder:"请输入路由名称"},null,8,["modelValue"])]),_:1})]),_:1})):T("",!0),o(l).menuType!="F"?(i(),r(m,{key:2,span:12},{default:t(()=>[s(v,null,{label:t(()=>[p("span",null,[s(L,{content:"选择是外链则路由地址需要以`http(s)://`开头",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a("是否外链 ")])]),default:t(()=>[s(q,{modelValue:o(l).isFrame,"onUpdate:modelValue":n[9]||(n[9]=_=>o(l).isFrame=_)},{default:t(()=>[s(P,{value:"0"},{default:t(()=>[a("是")]),_:1}),s(P,{value:"1"},{default:t(()=>[a("否")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})):T("",!0),o(l).menuType!="F"?(i(),r(m,{key:3,span:12},{default:t(()=>[s(v,{prop:"path"},{label:t(()=>[p("span",null,[s(L,{content:"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 路由地址 ")])]),default:t(()=>[s(A,{modelValue:o(l).path,"onUpdate:modelValue":n[10]||(n[10]=_=>o(l).path=_),placeholder:"请输入路由地址"},null,8,["modelValue"])]),_:1})]),_:1})):T("",!0),o(l).menuType=="C"?(i(),r(m,{key:4,span:12},{default:t(()=>[s(v,{prop:"component"},{label:t(()=>[p("span",null,[s(L,{content:"访问的组件路径，如：`system/user/index`，默认在`views`目录下",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 组件路径 ")])]),default:t(()=>[s(A,{modelValue:o(l).component,"onUpdate:modelValue":n[11]||(n[11]=_=>o(l).component=_),placeholder:"请输入组件路径"},null,8,["modelValue"])]),_:1})]),_:1})):T("",!0),o(l).menuType!="M"?(i(),r(m,{key:5,span:12},{default:t(()=>[s(v,null,{label:t(()=>[p("span",null,[s(L,{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 权限字符 ")])]),default:t(()=>[s(A,{modelValue:o(l).perms,"onUpdate:modelValue":n[12]||(n[12]=_=>o(l).perms=_),placeholder:"请输入权限标识",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})):T("",!0),o(l).menuType=="C"?(i(),r(m,{key:6,span:12},{default:t(()=>[s(v,null,{label:t(()=>[p("span",null,[s(L,{content:'访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 路由参数 ")])]),default:t(()=>[s(A,{modelValue:o(l).query,"onUpdate:modelValue":n[13]||(n[13]=_=>o(l).query=_),placeholder:"请输入路由参数",maxlength:"255"},null,8,["modelValue"])]),_:1})]),_:1})):T("",!0),o(l).menuType=="C"?(i(),r(m,{key:7,span:12},{default:t(()=>[s(v,null,{label:t(()=>[p("span",null,[s(L,{content:"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 是否缓存 ")])]),default:t(()=>[s(q,{modelValue:o(l).isCache,"onUpdate:modelValue":n[14]||(n[14]=_=>o(l).isCache=_)},{default:t(()=>[s(P,{value:"0"},{default:t(()=>[a("缓存")]),_:1}),s(P,{value:"1"},{default:t(()=>[a("不缓存")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})):T("",!0),o(l).menuType!="F"?(i(),r(m,{key:8,span:12},{default:t(()=>[s(v,null,{label:t(()=>[p("span",null,[s(L,{content:"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 显示状态 ")])]),default:t(()=>[s(q,{modelValue:o(l).visible,"onUpdate:modelValue":n[15]||(n[15]=_=>o(l).visible=_)},{default:t(()=>[(i(!0),b(B,null,z(o(K),_=>(i(),r(P,{key:_.value,value:_.value},{default:t(()=>[a(Q(_.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})):T("",!0),s(m,{span:12},{default:t(()=>[s(v,null,{label:t(()=>[p("span",null,[s(L,{content:"选择停用则路由将不会出现在侧边栏，也不能被访问",placement:"top"},{default:t(()=>[s(I,null,{default:t(()=>[s(D)]),_:1})]),_:1}),a(" 菜单状态 ")])]),default:t(()=>[s(q,{modelValue:o(l).status,"onUpdate:modelValue":n[16]||(n[16]=_=>o(l).status=_)},{default:t(()=>[(i(!0),b(B,null,z(o(f),_=>(i(),r(P,{key:_.value,value:_.value},{default:t(()=>[a(Q(_.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Xe as default};
