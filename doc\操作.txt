Lgjy2025testadmin!


部署说明:
1. 将压缩包上传到Linux服务器
2. 解压: unzip park-backend-deploy-3.6.6.zip
3. 进入目录: cd build-output
4. 设置执行权限: chmod +x *.sh
5. 构建镜像选项:
   - 构建所有: ./build-docker.sh --all
   - 构建指定: ./build-docker.sh --service wx
   - 查看服务: ./build-docker.sh --list
1. 将压缩包上传到Linux服务器
2. 解压: unzip park-backend-deploy-3.6.6.zip
3. 进入目录: cd build-output
4. 设置执行权限: chmod +x *.sh
5. 构建镜像选项:
   - 构建所有: ./build-docker.sh --all
   - 构建指定: ./build-docker.sh --service wx
   - 查看服务: ./build-docker.sh --list
4. 设置执行权限: chmod +x *.sh
5. 构建镜像选项:
   - 构建所有: ./build-docker.sh --all
   - 构建指定: ./build-docker.sh --service wx
   - 查看服务: ./build-docker.sh --list
5. 构建镜像选项:
   - 构建所有: ./build-docker.sh --all
   - 构建指定: ./build-docker.sh --service wx
   - 查看服务: ./build-docker.sh --list
   - 快速部署: ./deploy.sh
6. 启动服务: docker-compose up -d
   - 构建指定: ./build-docker.sh --service wx
   - 查看服务: ./build-docker.sh --list
   - 快速部署: ./deploy.sh
6. 启动服务: docker-compose up -d

# 停止旧的微服务容器（保留emqx和jenkins）
docker stop park-auth park-wx-auth park-nacos park-redis park-mysql

# 删除旧的微服务容器
docker rm park-auth park-wx-auth park-nacos park-redis park-mysql

# 删除旧的镜像（可选，释放磁盘空间）
docker rmi park/lgjy-auth:3.6.6 park/lgjy-wx-auth:3.6.6 nacos/nacos-server:v2.2.3 redis:7-alpine mysql:8.0
# 检查当前占用的端口
netstat -tlnp | grep -E ':(8080|8848|3306|6379|9200|9201|9202|9203|9204|9300)'

# 在park-api目录下执行
.\build-jars.ps1


# 删除旧版本的镜像（5天前的那些）
docker rmi park/lgjy-modules-gate:3.6.6
docker rmi park/lgjy-modules-wx:3.6.6
docker rmi park/lgjy-modules-file:3.6.6
docker rmi park/lgjy-modules-system:3.6.6

# 清理无用的镜像
docker image prune -f


# 先启动基础设施（MySQL, Redis, Nacos等）
docker-compose up -d mysql
docker-compose up -d redis
docker-compose up -d nacos

# 等待基础服务启动完成（约30-60秒）
sleep 60

# 检查基础服务状态
docker-compose ps

chmod +x build-docker.sh
# 给脚本执行权限
chmod +x deploy.sh

# 构建所有服务的Docker镜像
./deploy.sh

cd /root/parknew
unzip park-backend-deploy-3.6.6.zip


# 连接MySQL检查数据库
docker exec -it park-mysql mysql -u root -p5Y#y8XJj!6n@zBq$$
# 执行：SHOW DATABASES;
# 应该能看到nacos和parknew两个数据库

docker exec -it park-mysql mysql -uroot -p1234

# 查看MySQL容器运行状态
docker ps -a --filter name=park-mysql

# 查看容器详细信息
docker inspect park-mysql

[root@pile-charge-test parknew]# docker-compose ps
NAME                IMAGE                       COMMAND                  SERVICE             CREATED             STATUS                      PORTS
park-auth           park/lgjy-auth:3.6.6        "sh -c 'java $JAVA_O…"   park-auth           32 minutes ago      Up 32 minutes (unhealthy)   8080/tcp, 0.0.0.0:9204->9204/tcp, :::9204->9204/tcp
park-gateway        park/lgjy-gateway:3.6.6     "sh -c 'java $JAVA_O…"   park-gateway        33 minutes ago      Up 33 minutes (unhealthy)   0.0.0.0:8080->8080/tcp, :::8080->8080/tcp
park-mysql          mysql:8.0                   "docker-entrypoint.s…"   mysql               37 minutes ago      Up 37 minutes (healthy)     0.0.0.0:3306->3306/tcp, :::3306->3306/tcp, 33060/tcp
park-nacos          nacos/nacos-server:v2.2.3   "bin/docker-startup.…"   nacos               35 minutes ago      Up 35 minutes (unhealthy)   0.0.0.0:8848->8848/tcp, :::8848->8848/tcp, 0.0.0.0:9848->9848/tcp, :::9848->9848/tcp
park-redis          redis:7-alpine              "docker-entrypoint.s…"   redis               36 minutes ago      Up 36 minutes (healthy)     0.0.0.0:6380->6379/tcp, :::6380->6379/tcp
[root@pile-charge-test parknew]#       





# 1. 停止并删除容器
docker-compose stop mysql
docker-compose rm -f mysql

# 2. 删除MySQL数据卷（这会清除所有数据）
docker volume rm parknew_mysql_data

# 3. 确保SQL文件存在
mkdir -p /opt/parknew/sql
# 如果没有SQL文件，需要从源码复制

# 4. 重新启动MySQL服务
docker-compose up -d mysql

# 5. 等待初始化完成（约30-60秒）
docker-compose logs -f mysql


# 停止所有park相关的容器
docker stop $(docker ps -aq --filter "name=park")

# 删除所有park相关的容器
docker rm $(docker ps -aq --filter "name=park")

# 或者根据容器ID直接删除
docker rm 1b7a6360493d 481fe7737b80 632ac2f04899 ac0851b304e9












# 测试8848端口（Web控制台）
curl -f http://localhost:8848/nacos/v1/console/health/readiness

# 测试从容器内部访问
docker exec -it park-nacos curl -f http://localhost:8848/nacos/v1/console/health/readiness

# 测试网络连通性
docker exec -it park-nacos ping mysql
docker exec -it park-nacos ping park-redis

docker exec -it park-gateway ping mysql






# 在park-api目录下执行
docker build -t park/lgjy-gateway:3.6.6 -f lgjy-gateway/Dockerfile .
docker build -t park/lgjy-auth:3.6.6 -f lgjy-auth/Dockerfile .
docker build -t park/lgjy-modules-system:3.6.6 -f lgjy-modules/lgjy-system/Dockerfile .
docker build -t park/lgjy-modules-file:3.6.6 -f lgjy-modules/lgjy-file/Dockerfile .
docker build -t park/lgjy-modules-wx:3.6.6 -f lgjy-modules/lgjy-wx/Dockerfile .
docker build -t park/lgjy-modules-gate:3.6.6 -f lgjy-modules/lgjy-gate/Dockerfile .
cd park-api



{
  "mcpServers": {
    "mcp-jenkins": {
      "command": "uvx",
      "args": [
        "mcp-jenkins",
        "--jenkins-url=https://test-parknew.lgfw24hours.com:3443/jenkins",
        "--jenkins-username=amin",
        "--jenkins-password=Lgjy2025testadmin!"
      ]
    }
  }
}



bash<(curl -Ls https://raw.githubusercontent.com/mhsanaei/3x-ui/master/install.sh)


sudo apt update && sudo apt install -y curl  # Debian/Ubuntu

# 下载脚本到本地
curl -Ls https://raw.githubusercontent.com/mhsanaei/3x-ui/master/install.sh -o install.sh
# 赋予执行权限
chmod +x install.sh
# 执行脚本
sudo ./install.sh

Would you like to customize the Panel Port settings? (If not, a random port will be applied) [y/n]: y
Please set up the panel port: 8888
Your Panel Port is: 8888
Port set successfully: 8888
Username and password updated successfully
Base URI path set successfully
This is a fresh installation, generating random login info for security concerns:
###############################################
Username: vUdiOsx1QY
Password: AL1yyPq8IP
Port: 8888
WebBasePath: amBrnRsuB19RWXU
Access URL: http://**********:8888/amBrnRsuB19RWXU
###############################################
Start migrating database...
Migration done!
Created symlink /etc/systemd/system/multi-user.target.wants/x-ui.service → /etc/systemd/system/x-ui.service.
x-ui v2.6.2 installation finished, it is running now...

┌───────────────────────────────────────────────────────┐
│  x-ui control menu usages (subcommands):              │
│                                                       │
│  x-ui              - Admin Management Script          │
│  x-ui start        - Start                            │
│  x-ui stop         - Stop                             │
│  x-ui restart      - Restart                          │
│  x-ui status       - Current Status                   │
│  x-ui settings     - Current Settings                 │
│  x-ui enable       - Enable Autostart on OS Startup   │
│  x-ui disable      - Disable Autostart on OS Startup  │
│  x-ui log          - Check logs                       │
│  x-ui banlog       - Check Fail2ban ban logs          │
│  x-ui update       - Update                           │
│  x-ui legacy       - legacy version                   │
│  x-ui install      - Install                          │
│  x-ui uninstall    - Uninstall                        │
└───────────────────────────────────────────────────────┘
ubuntu@ip-172-31-31-57:~$ ^C






# 清理jenkins-park容器的日志
sudo truncate -s 0 $(docker inspect --format='{{.LogPath}}' jenkins-park)

# 或者使用这个命令
echo "" | sudo tee $(docker inspect --format='{{.LogPath}}' jenkins-park)

curl -k -u amin:4c1735db49c3e9243472caf6c7a9be79 https://test-parknew.lgfw24hours.com:3443/jenkins/api/json

curl -k -u amin:4c1735db49c3e9243472caf6c7a9be79 https://test-parknew.lgfw24hours.com:3443/jenkins/crumbIssuer/api/json\


docker exec -it park-mysql mysql -u admin -pK!7Pm?dL9Rf2Q
# 使用新的root密码连接（应该成功）
docker exec -it park-mysql mysql -u root -p"5Yy8XJj!6nzBq?"

# 使用admin密码连接
docker exec -it park-mysql mysql -u admin -p1234"K!7Pm?dL9Rf2Q"

# 查看当前数据卷
docker volume ls

# 删除MySQL相关数据卷
docker volume rm parknew_mysql_data
docker volume rm park-api_mysql_logs

# 或者删除所有未使用的数据卷
docker volume prune -f


# 停止Docker Compose服务
docker-compose down

# 强制删除MySQL容器（如果还在运行）
docker rm -f park-mysql

# 删除所有停止的容器
docker container prune -f
# 先测试是否真的是空密码
docker exec -it park-mysql mysql -u root

# 如果能连接，说明确实是空密码问题
# 查看MySQL容器的环境变量
docker exec park-mysql env | grep MYSQL

# 查看所有环境变量
docker exec park-mysql printenv


# 查看MySQL配置
docker exec park-mysql cat /etc/mysql/my.cnf

# 查看MySQL配置目录
docker exec park-mysql ls -la /etc/mysql/conf.d/
# 1. 检查Docker卷是否创建
docker volume ls | grep mysql

# 2. 查看MySQL卷的详细信息
docker volume inspect parknew_mysql_data
docker volume inspect park-api_mysql_logs
# 1. 检查MySQL数据卷的内容
ls -la /home/<USER>/volumes/parknew_mysql_data/_data

# 2. 检查MySQL日志卷的内容
ls -la /home/<USER>/volumes/parknew_mysql_logs/_data

# 3. 检查MySQL容器状态
docker ps | grep mysql

# 4. 查看MySQL容器日志
docker logs park-mysql

# 5. 检查MySQL容器的实际挂载
docker inspect park-mysql | grep -A 20 "Mounts"

# 3. 检查MySQL容器的挂载状态
docker inspect park-mysql | grep -A 10 "Mounts"

# 4. 查看容器状态
docker ps | grep mysql


TEST环境:
mysql
管理员账号：root
管理员密码：5Yy8XJj6nzBq
parknew管理员账号：admin
parknew管理员密码：K7PmdL9Rf2Q
redis
密码：qR6bW9kFzT3Zv
nacos
密码：Tp9Vc2JxY6HdM
超级管理员：1TRTp9VM23SD4fs

docker exec -it park-mysql mysql -u admin -pK7PmdL9Rf2Q
docker exec -it park-mysql mysql -u root -p5Yy8XJj6nzBq

# 停止容器并删除数据卷

docker-compose down
docker volume rm parknew_mysql_data parknew_mysql_logs
docker-compose up -d mysql

[root@pile-charge-test parknew]# # 生成32字节随机密钥并Base64编码
[root@pile-charge-test parknew]# KEY=$(openssl rand -base64 32 | tr -d '\n')
[root@pile-charge-test parknew]# echo "生成的安全密钥: ${KEY}"
生成的安全密钥: zoSizp5FB4owkEXOTHAhFsDTpR5Vp0MukBrB6Rbiglg=
[root@pile-charge-test parknew]#

# 查看帮助
./build-docker.sh --help

chmod +x build-docker.sh

# 列出所有可用服务
./build-docker.sh --list

# 构建指定服务
./build-docker.sh --service gateway
./build-docker.sh --service wx

# 构建所有服务
./build-docker.sh --all


# 搜索数据源相关错误
docker-compose logs park-wx | grep -E "(DataSource|datasource|mysql|jdbc)"

# 搜索Druid相关错误
docker-compose logs park-wx | grep -i druid



# 从park-wx容器测试Nacos连接
docker exec -it park-wx ping nacos
docker exec -it park-wx curl http://nacos:8848/nacos/v1/console/health/readiness

# 从park-wx容器测试Redis连接
docker exec -it park-wx ping park-redis

# 查看Docker网络
docker network ls

# 检查park-network网络详情
docker network inspect park-network

# 查看容器网络配置
docker inspect park-wx | grep -A 10 "NetworkSettings"


# 查看park-wx容器内的bootstrap配置
docker run --rm -v $(pwd)/jars:/jars openjdk:8-jre-alpine jar -tf /jars/lgjy-wx-3.6.6.jar | grep bootstrap

# 提取并查看bootstrap.yml内容
docker run --rm -v $(pwd)/jars:/jars openjdk:8-jre-alpine sh -c "
cd /tmp && 
jar -xf /jars/lgjy-wx-3.6.6.jar BOOT-INF/classes/bootstrap.yml && 
cat BOOT-INF/classes/bootstrap.yml
"

# 查看park-wx启动时的Nacos连接日志
docker-compose logs park-wx | grep -E "(nacos|bootstrap|config)"

# 查看具体的Nacos客户端连接信息
docker-compose logs park-wx | grep -E "(NacosPropertySource|NacosConfigService)"

# 检查Docker网络配置
docker network inspect parknew_park-network

# 查看park-wx的网络配置
docker inspect park-wx | grep -A 20 "Networks"

# 查看nacos的网络配置  
docker inspect park-nacos | grep -A 20 "Networks"


# 从park-wx容器网络测试Nacos连接
docker run --rm --network parknew_park-network alpine sh -c "
  apk add --no-cache curl && 
  curl -v http://park-nacos:8848/nacos/v1/console/health/readiness
"

# 测试配置获取API
docker run --rm --network parknew_park-network alpine sh -c "
  apk add --no-cache curl && 
  curl -v 'http://park-nacos:8848/nacos/v1/cs/configs?dataId=park-wx-test.yml&group=DEFAULT_GROUP'
"
# 1. 进入MySQL容器
docker exec -it park-mysql mysql -u root -p5Yy8XJj6nzBq

# 2. 检查用户是否存在
SELECT User, Host FROM mysql.user WHERE User = 'admin';

# 3. 检查数据库是否存在
SHOW DATABASES;

# 1. 检查当前镜像中是否包含MySQL驱动
docker run --rm park/lgjy-wx:3.6.6 find /app -name "*mysql*" -type f

# 2. 检查JAR包内容
docker run --rm park/lgjy-wx:3.6.6 sh -c "cd /app && jar -tf application.jar | grep mysql"

# 3. 检查类路径
docker run --rm park/lgjy-wx:3.6.6 sh -c "java -cp /app/application.jar -verbose:class com.lgjy.wx.LgjyWxApplication" 2>&1 | grep mysql




./build-docker.sh --all


# 测试主机是否可达
ping **************

# 测试SSH端口是否开放
telnet ************** 22





























