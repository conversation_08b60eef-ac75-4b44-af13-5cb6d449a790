pipeline {
    agent any

    environment {
        // 项目配置
        PROJECT_NAME = 'park-new'
        PROJECT_ROOT = '/opt/park-new'
        PROJECT_VERSION = '3.6.6'

        // Git配置 - 云效仓库
        BACKEND_REPOSITORY = "${params.BACKEND_REPOSITORY ?: 'https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-api.git'}"
        FRONTEND_REPOSITORY = "${params.FRONTEND_REPOSITORY ?: 'https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-ui.git'}"
        BACKEND_BRANCH = "${params.BACKEND_BRANCH ?: 'park'}"
        FRONTEND_BRANCH = "${params.FRONTEND_BRANCH ?: 'test'}"
        GIT_CREDENTIALS_ID = "${params.GIT_CREDENTIALS_ID ?: 'codeup-credentials'}"

        // 服务器配置
        SERVER_HOST = '*************'
        SSH_PORT = '1224'

        // 网络架构配置
        EXTERNAL_PORT = '3443'           // 外部访问端口
        SYSTEM_NGINX_PORT = '443'        // 系统nginx端口
        JENKINS_SYSTEM_PORT = '9001'     // 系统Jenkins端口
        JENKINS_CONTAINER_PORT = '8080'  // Jenkins容器内部端口
        FRONTEND_SYSTEM_PORT = '9443'    // 系统前端端口（修正）
        BACKEND_CONTAINER_PORT = '8080'  // 后端容器端口

        // 访问地址配置
        BASE_DOMAIN = 'test-parknew.lgfw24hours.com'
        EXTERNAL_URL = "https://${BASE_DOMAIN}:${EXTERNAL_PORT}"
        JENKINS_URL = "${EXTERNAL_URL}/jenkins"
        FRONTEND_URL = "${EXTERNAL_URL}/"
        API_URL = "${EXTERNAL_URL}/prod-api"

        // Docker容器名称
        JENKINS_CONTAINER = 'jenkins-park'
        NGINX_CONTAINER = 'park-nginx'
        BACKEND_CONTAINER = 'park-backend'

        // 构建配置
        MAVEN_OPTS = '-Xmx2g -Xms1g'
        NODE_VERSION = '18'
        JAVA_VERSION = '11'

        // Docker配置
        DOCKER_REGISTRY = 'local'
        DOCKER_NETWORK = 'park-network'

        // 部署环境
        DEPLOY_ENV = "${params.DEPLOY_ENV ?: 'test'}"
        BUILD_NUMBER_TAG = "${env.BUILD_NUMBER}"

        // 脚本路径
        SCRIPTS_DIR = "${PROJECT_ROOT}/scripts"

        // 质量检查配置
        SONAR_PROJECT_KEY = "${PROJECT_NAME}"
        SONAR_HOST_URL = "${params.SONAR_HOST_URL ?: 'http://localhost:9000'}"
    }
    
    parameters {
        string(
            name: 'BACKEND_REPOSITORY',
            defaultValue: 'https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-api.git',
            description: '后端Git仓库地址'
        )
        string(
            name: 'FRONTEND_REPOSITORY',
            defaultValue: 'https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-ui.git',
            description: '前端Git仓库地址'
        )
        string(
            name: 'BACKEND_BRANCH',
            defaultValue: 'park',
            description: '后端Git分支名称'
        )
        string(
            name: 'FRONTEND_BRANCH',
            defaultValue: 'test',
            description: '前端Git分支名称'
        )
        string(
            name: 'GIT_CREDENTIALS_ID',
            defaultValue: 'codeup-credentials',
            description: '云效Git凭据ID'
        )
        choice(
            name: 'DEPLOY_ENV',
            choices: ['test', 'staging', 'prod'],
            description: '选择部署环境'
        )
        choice(
            name: 'BUILD_TYPE',
            choices: ['all', 'backend-only', 'frontend-only'],
            description: '选择构建类型'
        )
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: '是否跳过测试'
        )
        booleanParam(
            name: 'CLEAN_BUILD',
            defaultValue: false,
            description: '是否清理构建'
        )
        booleanParam(
            name: 'FORCE_DEPLOY',
            defaultValue: false,
            description: '是否强制部署'
        )
        booleanParam(
            name: 'ENABLE_SONAR',
            defaultValue: true,
            description: '是否启用代码质量检查'
        )
        booleanParam(
            name: 'ENABLE_SECURITY_SCAN',
            defaultValue: true,
            description: '是否启用安全扫描'
        )
        string(
            name: 'SONAR_HOST_URL',
            defaultValue: 'http://localhost:9000',
            description: 'SonarQube服务器地址'
        )
    }
    
    stages {
        stage('代码拉取') {
            parallel {
                stage('拉取后端代码') {
                    when {
                        expression {
                            params.BUILD_TYPE == 'all' || params.BUILD_TYPE == 'backend-only'
                        }
                    }
                    steps {
                        script {
                            echo "=== 拉取后端代码 ==="
                            echo "后端仓库: ${BACKEND_REPOSITORY}"
                            echo "后端分支: ${BACKEND_BRANCH}"

                            // 创建后端代码目录
                            dir('park-api') {
                                // 清理工作空间
                                if (params.CLEAN_BUILD) {
                                    echo "清理后端工作空间..."
                                    deleteDir()
                                }

                                // 检出后端代码
                                checkout scmGit(
                                    branches: [[name: "*/${BACKEND_BRANCH}"]],
                                    extensions: [
                                        cleanBeforeCheckout(deleteUntrackedNestedRepositories: true),
                                        cloneOption(
                                            shallow: false,
                                            noTags: false,
                                            timeout: 10
                                        )
                                    ],
                                    userRemoteConfigs: [[
                                        credentialsId: "${GIT_CREDENTIALS_ID}",
                                        url: "${BACKEND_REPOSITORY}"
                                    ]]
                                )

                                // 设置后端Git环境变量
                                env.BACKEND_GIT_COMMIT = sh(
                                    script: 'git rev-parse HEAD',
                                    returnStdout: true
                                ).trim()

                                env.BACKEND_GIT_COMMIT_SHORT = sh(
                                    script: 'git rev-parse --short HEAD',
                                    returnStdout: true
                                ).trim()

                                echo "后端Git提交: ${env.BACKEND_GIT_COMMIT_SHORT}"
                            }
                        }
                    }
                }

                stage('拉取前端代码') {
                    when {
                        expression {
                            params.BUILD_TYPE == 'all' || params.BUILD_TYPE == 'frontend-only'
                        }
                    }
                    steps {
                        script {
                            echo "=== 拉取前端代码 ==="
                            echo "前端仓库: ${FRONTEND_REPOSITORY}"
                            echo "前端分支: ${FRONTEND_BRANCH}"

                            // 创建前端代码目录
                            dir('park-ui') {
                                // 清理工作空间
                                if (params.CLEAN_BUILD) {
                                    echo "清理前端工作空间..."
                                    deleteDir()
                                }

                                // 检出前端代码
                                checkout scmGit(
                                    branches: [[name: "*/${FRONTEND_BRANCH}"]],
                                    extensions: [
                                        cleanBeforeCheckout(deleteUntrackedNestedRepositories: true),
                                        cloneOption(
                                            shallow: false,
                                            noTags: false,
                                            timeout: 10
                                        )
                                    ],
                                    userRemoteConfigs: [[
                                        credentialsId: "${GIT_CREDENTIALS_ID}",
                                        url: "${FRONTEND_REPOSITORY}"
                                    ]]
                                )

                                // 设置前端Git环境变量
                                env.FRONTEND_GIT_COMMIT = sh(
                                    script: 'git rev-parse HEAD',
                                    returnStdout: true
                                ).trim()

                                env.FRONTEND_GIT_COMMIT_SHORT = sh(
                                    script: 'git rev-parse --short HEAD',
                                    returnStdout: true
                                ).trim()

                                echo "前端Git提交: ${env.FRONTEND_GIT_COMMIT_SHORT}"
                            }
                        }
                    }
                }

                stage('拉取脚本') {
                    steps {
                        script {
                            echo "=== 拉取部署脚本 ==="

                            // 从后端仓库拉取scripts目录
                            dir('scripts-temp') {
                                checkout scmGit(
                                    branches: [[name: "*/${BACKEND_BRANCH}"]],
                                    extensions: [
                                        sparseCheckout(sparseCheckoutPaths: [[path: 'scripts']])
                                    ],
                                    userRemoteConfigs: [[
                                        credentialsId: "${GIT_CREDENTIALS_ID}",
                                        url: "${BACKEND_REPOSITORY}"
                                    ]]
                                )

                                // 复制scripts到工作空间根目录
                                sh """
                                    if [ -d "scripts" ]; then
                                        cp -r scripts ../
                                        echo "脚本目录复制完成"
                                    else
                                        echo "警告: 未找到scripts目录"
                                    fi
                                """
                            }

                            // 清理临时目录
                            sh "rm -rf scripts-temp"
                        }
                    }
                }
            }
            post {
                success {
                    script {
                        echo "代码拉取完成"
                        echo "构建类型: ${params.BUILD_TYPE}"
                        echo "部署环境: ${DEPLOY_ENV}"
                        echo "构建号: ${BUILD_NUMBER_TAG}"

                        // 设置通用Git环境变量
                        if (env.BACKEND_GIT_COMMIT) {
                            env.GIT_COMMIT = env.BACKEND_GIT_COMMIT
                            env.GIT_COMMIT_SHORT = env.BACKEND_GIT_COMMIT_SHORT
                            env.GIT_BRANCH_CURRENT = BACKEND_BRANCH
                        } else if (env.FRONTEND_GIT_COMMIT) {
                            env.GIT_COMMIT = env.FRONTEND_GIT_COMMIT
                            env.GIT_COMMIT_SHORT = env.FRONTEND_GIT_COMMIT_SHORT
                            env.GIT_BRANCH_CURRENT = FRONTEND_BRANCH
                        }
                    }
                }
            }
        }

        stage('环境准备') {
            steps {
                script {
                    echo "=== 环境准备阶段 ==="
                    echo "服务器: ${SERVER_HOST}:${SSH_PORT}"
                    echo "项目目录: ${PROJECT_ROOT}"
                    echo "Jenkins端口: ${JENKINS_SYSTEM_PORT}"

                    // 创建项目目录结构
                    sh """
                        # 创建必要的目录
                        mkdir -p ${PROJECT_ROOT}
                        mkdir -p ${PROJECT_ROOT}/logs
                        mkdir -p ${PROJECT_ROOT}/deploy-package
                        mkdir -p ${PROJECT_ROOT}/data
                        mkdir -p ${PROJECT_ROOT}/data/mysql
                        mkdir -p ${PROJECT_ROOT}/data/redis
                        mkdir -p ${PROJECT_ROOT}/data/uploads

                        # 复制代码到项目目录
                        echo "复制代码到项目目录..."
                        if [ -d "park-api" ]; then
                            rsync -av --exclude='.git' park-api/ ${PROJECT_ROOT}/park-api/
                            echo "后端代码复制完成"
                        fi

                        if [ -d "park-ui" ]; then
                            rsync -av --exclude='.git' park-ui/ ${PROJECT_ROOT}/park-ui/
                            echo "前端代码复制完成"
                        fi

                        if [ -d "scripts" ]; then
                            rsync -av scripts/ ${PROJECT_ROOT}/scripts/
                            echo "脚本复制完成"
                        fi

                        cd ${PROJECT_ROOT}

                        # 检查脚本目录
                        if [ -d "scripts" ]; then
                            echo "设置脚本执行权限..."
                            find scripts -name "*.sh" -exec chmod +x {} \\;
                        else
                            echo "警告: 脚本目录不存在，创建基本脚本目录结构"
                            mkdir -p scripts/{build,deploy,test,utils}
                        fi

                        # 检查必要的工具
                        echo "检查环境工具..."
                        echo "Java版本:"
                        java -version || echo "Java未安装"

                        echo "Maven版本:"
                        mvn --version || echo "Maven未安装"

                        echo "Node.js版本:"
                        node --version || echo "Node.js未安装"

                        echo "NPM版本:"
                        npm --version || echo "NPM未安装"

                        echo "Docker版本:"
                        docker --version || echo "Docker未安装"

                        echo "Docker Compose版本:"
                        docker-compose --version || echo "Docker Compose未安装"

                        # 检查关键端口占用情况
                        echo "检查关键端口占用情况..."
                        echo "系统nginx端口 ${SYSTEM_NGINX_PORT}:"
                        netstat -tlnp | grep ":${SYSTEM_NGINX_PORT}" || echo "端口 ${SYSTEM_NGINX_PORT} 未被占用"

                        echo "Jenkins系统端口 ${JENKINS_SYSTEM_PORT}:"
                        netstat -tlnp | grep ":${JENKINS_SYSTEM_PORT}" || echo "端口 ${JENKINS_SYSTEM_PORT} 未被占用"

                        echo "前端系统端口 ${FRONTEND_SYSTEM_PORT}:"
                        netstat -tlnp | grep ":${FRONTEND_SYSTEM_PORT}" || echo "端口 ${FRONTEND_SYSTEM_PORT} 未被占用"

                        # 检查磁盘空间
                        echo "磁盘空间检查:"
                        df -h ${PROJECT_ROOT}

                        # 检查Docker服务状态
                        echo "Docker服务状态:"
                        systemctl is-active docker || echo "Docker服务未运行"

                        # 显示当前运行的容器
                        echo "当前运行的容器:"
                        docker ps --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}" || echo "无法获取容器信息"

                        # 检查关键容器状态
                        echo "检查关键容器状态..."
                        docker ps --filter "name=${JENKINS_CONTAINER}" --format "{{.Names}}: {{.Status}}" || echo "Jenkins容器未运行"
                        docker ps --filter "name=${NGINX_CONTAINER}" --format "{{.Names}}: {{.Status}}" || echo "Nginx容器未运行"

                        # 检查nginx配置
                        echo "检查nginx配置..."
                        nginx -t || echo "nginx配置检查失败"

                        # 检查nginx服务状态
                        systemctl is-active nginx || echo "nginx服务未运行"

                        echo "环境准备完成"
                    """
                }
            }
        }
        
        stage('代码验证') {
            parallel {
                stage('代码质量检查') {
                    when {
                        expression { params.ENABLE_SONAR }
                    }
                    steps {
                        script {
                            echo "=== 代码质量检查 ==="

                            sh """
                                cd ${PROJECT_ROOT}

                                # SonarQube代码质量检查
                                if [ "${params.BUILD_TYPE}" = "all" ] || [ "${params.BUILD_TYPE}" = "backend-only" ]; then
                                    echo "执行后端代码质量检查..."
                                    cd park-api
                                    mvn sonar:sonar \\
                                        -Dsonar.projectKey=${SONAR_PROJECT_KEY} \\
                                        -Dsonar.host.url=${SONAR_HOST_URL} \\
                                        -Dsonar.login=\${SONAR_TOKEN} || echo "SonarQube检查失败，继续构建"
                                    cd ..
                                fi

                                # 前端代码检查
                                if [ "${params.BUILD_TYPE}" = "all" ] || [ "${params.BUILD_TYPE}" = "frontend-only" ]; then
                                    echo "执行前端代码检查..."
                                    if [ -d "park-ui" ]; then
                                        cd park-ui
                                        if [ -f "package.json" ]; then
                                            npm install --silent
                                            npm run lint || echo "前端代码检查失败，继续构建"
                                        fi
                                        cd ..
                                    fi
                                fi
                            """
                        }
                    }
                }

                stage('安全扫描') {
                    when {
                        expression { params.ENABLE_SECURITY_SCAN }
                    }
                    steps {
                        script {
                            echo "=== 安全扫描 ==="

                            sh """
                                cd ${PROJECT_ROOT}

                                # 依赖安全扫描
                                if [ "${params.BUILD_TYPE}" = "all" ] || [ "${params.BUILD_TYPE}" = "backend-only" ]; then
                                    echo "执行后端依赖安全扫描..."
                                    cd park-api
                                    mvn org.owasp:dependency-check-maven:check || echo "安全扫描失败，继续构建"
                                    cd ..
                                fi

                                # 前端依赖安全扫描
                                if [ "${params.BUILD_TYPE}" = "all" ] || [ "${params.BUILD_TYPE}" = "frontend-only" ]; then
                                    echo "执行前端依赖安全扫描..."
                                    if [ -d "park-ui" ] && [ -f "park-ui/package.json" ]; then
                                        cd park-ui
                                        npm audit --audit-level=high || echo "前端安全扫描发现问题，请检查"
                                        cd ..
                                    fi
                                fi
                            """
                        }
                    }
                }

                stage('脚本语法检查') {
                    steps {
                        script {
                            echo "=== 脚本语法检查 ==="

                            sh """
                                cd ${PROJECT_ROOT}

                                # 检查Shell脚本语法
                                echo "检查Shell脚本语法..."
                                find ${SCRIPTS_DIR} -name "*.sh" -exec bash -n {} \\;

                                # 检查Docker文件
                                echo "检查Dockerfile语法..."
                                find . -name "Dockerfile*" -exec docker run --rm -i hadolint/hadolint < {} \\; || echo "Dockerfile检查完成"

                                # 检查YAML文件
                                echo "检查YAML文件语法..."
                                find . -name "*.yml" -o -name "*.yaml" | xargs -I {} sh -c 'python3 -c "import yaml; yaml.safe_load(open(\\"{}\\"))" || echo "YAML文件 {} 语法错误"'

                                echo "脚本语法检查完成"
                            """
                        }
                    }
                }
            }
        }
        
        stage('构建阶段') {
            steps {
                script {
                    echo "=== 构建阶段 ==="

                    def buildArgs = ""

                    if (params.SKIP_TESTS) {
                        buildArgs += " --skip-tests"
                    }

                    if (params.CLEAN_BUILD) {
                        buildArgs += " --clean"
                    }

                    buildArgs += " --env=${DEPLOY_ENV}"

                    // 设置构建环境变量
                    withEnv([
                        "BUILD_NUMBER=${BUILD_NUMBER}",
                        "GIT_COMMIT=${env.GIT_COMMIT}",
                        "GIT_BRANCH=${env.GIT_BRANCH_CURRENT}",
                        "BUILD_TIMESTAMP=${new Date().format('yyyy-MM-dd HH:mm:ss')}"
                    ]) {
                        sh """
                            cd ${PROJECT_ROOT}

                            # 创建构建日志目录
                            mkdir -p logs

                            # 记录构建开始时间
                            echo "构建开始时间: \$(date)" > logs/build.log

                            case "${params.BUILD_TYPE}" in
                                "all")
                                    echo "执行完整构建..."
                                    ${SCRIPTS_DIR}/build/build-all.sh ${buildArgs} 2>&1 | tee -a logs/build.log
                                    ;;
                                "backend-only")
                                    echo "执行后端构建..."
                                    ${SCRIPTS_DIR}/build/build-backend.sh ${buildArgs} 2>&1 | tee -a logs/build.log
                                    ;;
                                "frontend-only")
                                    echo "执行前端构建..."
                                    ${SCRIPTS_DIR}/build/build-frontend.sh ${buildArgs} 2>&1 | tee -a logs/build.log
                                    ;;
                                *)
                                    echo "未知构建类型: ${params.BUILD_TYPE}"
                                    exit 1
                                    ;;
                            esac

                            # 记录构建结束时间
                            echo "构建结束时间: \$(date)" >> logs/build.log

                            # 生成构建摘要
                            echo "=== 构建摘要 ===" >> logs/build.log
                            echo "项目: ${PROJECT_NAME}" >> logs/build.log
                            echo "版本: ${PROJECT_VERSION}" >> logs/build.log
                            echo "构建号: ${BUILD_NUMBER}" >> logs/build.log
                            echo "Git提交: ${env.GIT_COMMIT_SHORT}" >> logs/build.log
                            echo "Git分支: ${env.GIT_BRANCH_CURRENT}" >> logs/build.log
                            echo "构建类型: ${params.BUILD_TYPE}" >> logs/build.log
                            echo "部署环境: ${DEPLOY_ENV}" >> logs/build.log

                            echo "构建阶段完成"
                        """
                    }
                }
            }
            post {
                always {
                    // 归档构建产物
                    archiveArtifacts artifacts: 'logs/build.log', fingerprint: true, allowEmptyArchive: true
                }
                failure {
                    script {
                        echo "构建失败，收集错误信息..."
                        sh """
                            cd ${PROJECT_ROOT}
                            if [ -f logs/build.log ]; then
                                echo "=== 构建失败日志 ==="
                                tail -50 logs/build.log
                            fi
                        """
                    }
                }
            }
        }
        
        stage('测试阶段') {
            when {
                not { params.SKIP_TESTS }
            }
            parallel {
                stage('后端测试') {
                    when {
                        expression {
                            params.BUILD_TYPE == 'all' || params.BUILD_TYPE == 'backend-only'
                        }
                    }
                    steps {
                        script {
                            echo "=== 后端测试 ==="

                            sh """
                                cd ${PROJECT_ROOT}/park-api

                                # 创建测试报告目录
                                mkdir -p target/surefire-reports
                                mkdir -p target/site/jacoco

                                # 运行单元测试
                                echo "运行单元测试..."
                                mvn test -Dmaven.test.failure.ignore=true

                                # 运行集成测试
                                echo "运行集成测试..."
                                mvn verify -Dmaven.test.failure.ignore=true

                                # 生成测试覆盖率报告
                                echo "生成测试覆盖率报告..."
                                mvn jacoco:report || echo "覆盖率报告生成失败"

                                echo "后端测试完成"
                            """
                        }
                    }
                    post {
                        always {
                            // 发布测试结果
                            publishTestResults testResultsPattern: 'park-api/target/surefire-reports/*.xml'

                            // 发布覆盖率报告
                            publishCoverage adapters: [
                                jacocoAdapter('park-api/target/site/jacoco/jacoco.xml')
                            ], sourceFileResolver: sourceFiles('STORE_LAST_BUILD')
                        }
                    }
                }

                stage('前端测试') {
                    when {
                        expression {
                            params.BUILD_TYPE == 'all' || params.BUILD_TYPE == 'frontend-only'
                        }
                    }
                    steps {
                        script {
                            echo "=== 前端测试 ==="

                            sh """
                                cd ${PROJECT_ROOT}

                                if [ -d "park-ui" ] && [ -f "park-ui/package.json" ]; then
                                    cd park-ui

                                    # 安装依赖
                                    echo "安装前端依赖..."
                                    npm ci --silent

                                    # 运行单元测试
                                    echo "运行前端单元测试..."
                                    npm run test:unit || echo "前端单元测试失败"

                                    # 运行端到端测试
                                    echo "运行端到端测试..."
                                    npm run test:e2e || echo "端到端测试失败"

                                    # 生成测试覆盖率报告
                                    echo "生成前端测试覆盖率报告..."
                                    npm run test:coverage || echo "前端覆盖率报告生成失败"

                                    cd ..
                                fi

                                echo "前端测试完成"
                            """
                        }
                    }
                    post {
                        always {
                            // 发布前端测试结果
                            publishTestResults testResultsPattern: 'park-ui/test-results/*.xml'
                        }
                    }
                }

                stage('API测试') {
                    steps {
                        script {
                            echo "=== API测试 ==="

                            sh """
                                cd ${PROJECT_ROOT}

                                # 等待服务启动
                                echo "等待服务启动..."
                                sleep 30

                                # 运行API测试
                                if [ -f "scripts/test/api-test.sh" ]; then
                                    echo "运行API测试..."
                                    scripts/test/api-test.sh --env=${DEPLOY_ENV} || echo "API测试失败"
                                fi

                                # 运行性能测试
                                if [ -f "scripts/test/performance-test.sh" ]; then
                                    echo "运行性能测试..."
                                    scripts/test/performance-test.sh --env=${DEPLOY_ENV} || echo "性能测试失败"
                                fi

                                echo "API测试完成"
                            """
                        }
                    }
                }
            }
        }
        
        stage('镜像构建') {
            when {
                expression {
                    params.BUILD_TYPE == 'all' || params.BUILD_TYPE == 'backend-only'
                }
            }
            steps {
                script {
                    echo "=== Docker镜像构建 ==="

                    sh """
                        cd ${PROJECT_ROOT}

                        # 构建后端镜像
                        if [ "${params.BUILD_TYPE}" = "all" ] || [ "${params.BUILD_TYPE}" = "backend-only" ]; then
                            echo "构建后端Docker镜像..."
                            docker build -t ${PROJECT_NAME}-backend:${BUILD_NUMBER} \\
                                -t ${PROJECT_NAME}-backend:latest \\
                                --build-arg BUILD_NUMBER=${BUILD_NUMBER} \\
                                --build-arg GIT_COMMIT=${env.GIT_COMMIT_SHORT} \\
                                --build-arg BUILD_DATE="\$(date -u +%Y-%m-%dT%H:%M:%SZ)" \\
                                -f park-api/Dockerfile park-api/
                        fi

                        # 构建前端镜像
                        if [ "${params.BUILD_TYPE}" = "all" ] || [ "${params.BUILD_TYPE}" = "frontend-only" ]; then
                            if [ -d "park-ui" ]; then
                                echo "构建前端Docker镜像..."
                                docker build -t ${PROJECT_NAME}-frontend:${BUILD_NUMBER} \\
                                    -t ${PROJECT_NAME}-frontend:latest \\
                                    --build-arg BUILD_NUMBER=${BUILD_NUMBER} \\
                                    --build-arg GIT_COMMIT=${env.GIT_COMMIT_SHORT} \\
                                    --build-arg BUILD_DATE="\$(date -u +%Y-%m-%dT%H:%M:%SZ)" \\
                                    -f park-ui/Dockerfile park-ui/
                            fi
                        fi

                        # 显示构建的镜像
                        echo "构建完成的镜像:"
                        docker images | grep ${PROJECT_NAME}

                        echo "Docker镜像构建完成"
                    """
                }
            }
        }

        stage('部署到测试环境') {
            when {
                expression { params.DEPLOY_ENV == 'test' }
            }
            steps {
                script {
                    echo "=== 部署到测试环境 ==="

                    def deployArgs = "--env=test --build-number=${BUILD_NUMBER}"

                    if (params.FORCE_DEPLOY) {
                        deployArgs += " --force"
                    }

                    sh """
                        cd ${PROJECT_ROOT}

                        # 创建部署日志
                        mkdir -p logs
                        echo "部署开始时间: \$(date)" > logs/deploy.log

                        # 备份当前部署
                        echo "备份当前部署..."
                        ${SCRIPTS_DIR}/deploy/backup.sh --env=test 2>&1 | tee -a logs/deploy.log || echo "备份失败，继续部署"

                        # 执行部署
                        echo "执行部署..."
                        ${SCRIPTS_DIR}/deploy/deploy.sh ${deployArgs} 2>&1 | tee -a logs/deploy.log

                        # 记录部署信息
                        echo "部署结束时间: \$(date)" >> logs/deploy.log
                        echo "部署版本: ${BUILD_NUMBER}" >> logs/deploy.log
                        echo "Git提交: ${env.GIT_COMMIT_SHORT}" >> logs/deploy.log

                        echo "测试环境部署完成"
                    """
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'logs/deploy.log', fingerprint: true, allowEmptyArchive: true
                }
                failure {
                    script {
                        echo "部署失败，尝试回滚..."
                        sh """
                            cd ${PROJECT_ROOT}
                            ${SCRIPTS_DIR}/deploy/rollback.sh --env=test || echo "回滚失败"
                        """
                    }
                }
            }
        }

        stage('部署到预发布环境') {
            when {
                expression { params.DEPLOY_ENV == 'staging' }
            }
            steps {
                script {
                    echo "=== 部署到预发布环境 ==="

                    def deployArgs = "--env=staging --build-number=${BUILD_NUMBER}"

                    if (params.FORCE_DEPLOY) {
                        deployArgs += " --force"
                    }

                    sh """
                        cd ${PROJECT_ROOT}

                        # 创建部署日志
                        mkdir -p logs
                        echo "预发布部署开始时间: \$(date)" > logs/deploy-staging.log

                        # 备份当前部署
                        echo "备份当前预发布环境..."
                        ${SCRIPTS_DIR}/deploy/backup.sh --env=staging 2>&1 | tee -a logs/deploy-staging.log || echo "备份失败，继续部署"

                        # 执行部署
                        echo "执行预发布部署..."
                        ${SCRIPTS_DIR}/deploy/deploy.sh ${deployArgs} 2>&1 | tee -a logs/deploy-staging.log

                        echo "预发布环境部署完成"
                    """
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'logs/deploy-staging.log', fingerprint: true, allowEmptyArchive: true
                }
                failure {
                    script {
                        echo "预发布部署失败，尝试回滚..."
                        sh """
                            cd ${PROJECT_ROOT}
                            ${SCRIPTS_DIR}/deploy/rollback.sh --env=staging || echo "回滚失败"
                        """
                    }
                }
            }
        }
        
        stage('健康检查') {
            steps {
                script {
                    echo "=== 健康检查阶段 ==="

                    sh """
                        cd ${PROJECT_ROOT}

                        # 创建健康检查日志
                        mkdir -p logs
                        echo "健康检查开始时间: \$(date)" > logs/health-check.log

                        # 等待服务启动
                        echo "等待服务启动..."
                        sleep 30

                        # 执行网络架构验证
                        echo "执行网络架构验证..."
                        if [ -f "scripts/test/network-architecture-test.sh" ]; then
                            scripts/test/network-architecture-test.sh --verbose 2>&1 | tee -a logs/health-check.log || echo "网络架构验证失败"
                        fi

                        # 执行基础健康检查
                        echo "执行基础健康检查..."
                        ${SCRIPTS_DIR}/deploy/health-check.sh --env=${DEPLOY_ENV} --verbose 2>&1 | tee -a logs/health-check.log

                        # 检查服务状态
                        echo "检查服务状态..."
                        docker-compose -f deploy-package/docker-compose.yml ps 2>&1 | tee -a logs/health-check.log

                        # 检查服务端口
                        echo "检查服务端口..."
                        netstat -tlnp | grep -E ':(8080|8081|3000|80|443)' 2>&1 | tee -a logs/health-check.log || echo "端口检查完成"

                        # 执行网络架构验证
                        echo "验证网络架构..."
                        echo "检查系统nginx配置:" >> logs/health-check.log
                        nginx -t >> logs/health-check.log 2>&1 || echo "nginx配置检查失败" >> logs/health-check.log

                        echo "检查nginx配置:" >> logs/health-check.log
                        nginx -t >> logs/health-check.log 2>&1 || echo "nginx配置检查失败" >> logs/health-check.log

                        echo "检查关键容器状态:" >> logs/health-check.log
                        docker ps --filter "name=${JENKINS_CONTAINER}" --format "{{.Names}}: {{.Status}}" >> logs/health-check.log
                        docker ps --filter "name=${NGINX_CONTAINER}" --format "{{.Names}}: {{.Status}}" >> logs/health-check.log
                        docker ps --filter "name=${BACKEND_CONTAINER}" --format "{{.Names}}: {{.Status}}" >> logs/health-check.log

                        # 执行API健康检查（通过nginx转发）
                        echo "执行API健康检查..."
                        if [ -f "scripts/test/health-api-test.sh" ]; then
                            scripts/test/health-api-test.sh --env=${DEPLOY_ENV} 2>&1 | tee -a logs/health-check.log || echo "API健康检查失败"
                        fi

                        # 验证外部访问
                        echo "验证外部访问..."
                        curl -f -s --max-time 30 "${FRONTEND_URL}" > /dev/null && echo "前端外部访问正常" >> logs/health-check.log || echo "前端外部访问失败" >> logs/health-check.log
                        curl -f -s --max-time 30 "${JENKINS_URL}" > /dev/null && echo "Jenkins外部访问正常" >> logs/health-check.log || echo "Jenkins外部访问失败" >> logs/health-check.log

                        # 检查数据库连接
                        echo "检查数据库连接..."
                        if [ -f "scripts/test/db-connection-test.sh" ]; then
                            scripts/test/db-connection-test.sh --env=${DEPLOY_ENV} 2>&1 | tee -a logs/health-check.log || echo "数据库连接检查失败"
                        fi

                        # 检查Redis连接
                        echo "检查Redis连接..."
                        if [ -f "scripts/test/redis-connection-test.sh" ]; then
                            scripts/test/redis-connection-test.sh --env=${DEPLOY_ENV} 2>&1 | tee -a logs/health-check.log || echo "Redis连接检查失败"
                        fi

                        echo "健康检查结束时间: \$(date)" >> logs/health-check.log
                        echo "健康检查完成"
                    """
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'logs/health-check.log', fingerprint: true, allowEmptyArchive: true
                }
                failure {
                    script {
                        echo "健康检查失败，收集诊断信息..."
                        sh """
                            cd ${PROJECT_ROOT}

                            echo "=== 诊断信息收集 ===" >> logs/health-check.log
                            echo "Docker容器状态:" >> logs/health-check.log
                            docker ps -a >> logs/health-check.log

                            echo "Docker容器日志:" >> logs/health-check.log
                            docker-compose -f deploy-package/docker-compose.yml logs --tail=50 >> logs/health-check.log || true

                            echo "系统资源使用:" >> logs/health-check.log
                            df -h >> logs/health-check.log
                            free -h >> logs/health-check.log

                            echo "网络连接:" >> logs/health-check.log
                            netstat -tlnp >> logs/health-check.log
                        """
                    }
                }
            }
        }
        
        stage('生产环境部署审批') {
            when {
                expression { params.DEPLOY_ENV == 'prod' }
            }
            steps {
                script {
                    echo "=== 生产环境部署审批 ==="

                    // 显示部署信息
                    echo """
                    准备部署到生产环境:
                    - 项目: ${PROJECT_NAME}
                    - 版本: ${PROJECT_VERSION}
                    - 构建号: ${BUILD_NUMBER}
                    - Git提交: ${env.GIT_COMMIT_SHORT}
                    - Git分支: ${env.GIT_BRANCH_CURRENT}
                    - 构建类型: ${params.BUILD_TYPE}
                    """

                    def deployApproval = input(
                        message: '是否确认部署到生产环境？请仔细检查上述信息。',
                        ok: '确认部署',
                        submitterParameter: 'APPROVER',
                        parameters: [
                            choice(
                                name: 'DEPLOY_STRATEGY',
                                choices: ['rolling', 'blue-green', 'canary'],
                                description: '选择部署策略'
                            ),
                            booleanParam(
                                name: 'ENABLE_ROLLBACK_PLAN',
                                defaultValue: true,
                                description: '是否启用自动回滚计划'
                            ),
                            string(
                                name: 'MAINTENANCE_WINDOW',
                                defaultValue: '30',
                                description: '维护窗口时间（分钟）'
                            )
                        ]
                    )

                    env.DEPLOY_STRATEGY = deployApproval.DEPLOY_STRATEGY
                    env.ENABLE_ROLLBACK_PLAN = deployApproval.ENABLE_ROLLBACK_PLAN
                    env.MAINTENANCE_WINDOW = deployApproval.MAINTENANCE_WINDOW
                    env.APPROVER = deployApproval.APPROVER

                    echo "部署策略: ${env.DEPLOY_STRATEGY}"
                    echo "审批人: ${env.APPROVER}"
                    echo "维护窗口: ${env.MAINTENANCE_WINDOW}分钟"
                }
            }
        }

        stage('部署到生产环境') {
            when {
                expression { params.DEPLOY_ENV == 'prod' }
            }
            steps {
                script {
                    echo "=== 部署到生产环境 ==="

                    def deployArgs = "--env=prod --build-number=${BUILD_NUMBER} --strategy=${env.DEPLOY_STRATEGY}"

                    if (params.FORCE_DEPLOY) {
                        deployArgs += " --force"
                    }

                    if (env.ENABLE_ROLLBACK_PLAN == 'true') {
                        deployArgs += " --enable-rollback"
                    }

                    sh """
                        cd ${PROJECT_ROOT}

                        # 创建生产部署日志
                        mkdir -p logs
                        echo "生产环境部署开始时间: \$(date)" > logs/deploy-prod.log
                        echo "审批人: ${env.APPROVER}" >> logs/deploy-prod.log
                        echo "部署策略: ${env.DEPLOY_STRATEGY}" >> logs/deploy-prod.log
                        echo "构建号: ${BUILD_NUMBER}" >> logs/deploy-prod.log
                        echo "Git提交: ${env.GIT_COMMIT_SHORT}" >> logs/deploy-prod.log

                        # 生产环境预检查
                        echo "执行生产环境预检查..."
                        if [ -f "scripts/deploy/prod-pre-check.sh" ]; then
                            scripts/deploy/prod-pre-check.sh 2>&1 | tee -a logs/deploy-prod.log
                        fi

                        # 备份生产环境
                        echo "备份生产环境..."
                        ${SCRIPTS_DIR}/deploy/backup.sh --env=prod --full-backup 2>&1 | tee -a logs/deploy-prod.log

                        # 执行生产环境部署
                        echo "执行生产环境部署..."
                        ${SCRIPTS_DIR}/deploy/deploy.sh ${deployArgs} 2>&1 | tee -a logs/deploy-prod.log

                        # 生产环境部署后检查
                        echo "执行生产环境部署后检查..."
                        if [ -f "scripts/deploy/prod-post-check.sh" ]; then
                            scripts/deploy/prod-post-check.sh 2>&1 | tee -a logs/deploy-prod.log
                        fi

                        echo "生产环境部署结束时间: \$(date)" >> logs/deploy-prod.log
                        echo "生产环境部署完成"
                    """
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'logs/deploy-prod.log', fingerprint: true, allowEmptyArchive: true
                }
                success {
                    script {
                        echo "生产环境部署成功，发送通知..."
                        // 这里可以添加成功通知逻辑
                    }
                }
                failure {
                    script {
                        echo "生产环境部署失败，执行紧急回滚..."
                        sh """
                            cd ${PROJECT_ROOT}
                            if [ "${env.ENABLE_ROLLBACK_PLAN}" = "true" ]; then
                                echo "执行自动回滚..."
                                ${SCRIPTS_DIR}/deploy/rollback.sh --env=prod --emergency 2>&1 | tee -a logs/deploy-prod.log
                            fi
                        """
                        // 这里可以添加失败通知逻辑
                    }
                }
            }
        }
        
        stage('部署后验证') {
            steps {
                script {
                    echo "=== 部署后验证 ==="

                    sh """
                        cd ${PROJECT_ROOT}

                        # 创建验证日志
                        mkdir -p logs
                        echo "部署后验证开始时间: \$(date)" > logs/post-deploy-verification.log

                        # 等待服务稳定
                        echo "等待服务稳定..."
                        sleep 60

                        # 执行全面健康检查
                        echo "执行全面健康检查..."
                        ${SCRIPTS_DIR}/deploy/health-check.sh --env=${DEPLOY_ENV} --verbose --comprehensive 2>&1 | tee -a logs/post-deploy-verification.log

                        # 检查关键服务状态
                        echo "检查关键服务状态..."
                        docker-compose -f deploy-package/docker-compose.yml ps 2>&1 | tee -a logs/post-deploy-verification.log

                        # 执行综合部署验证
                        echo "执行综合部署验证..."
                        if [ -f "scripts/test/deployment-verification.sh" ]; then
                            scripts/test/deployment-verification.sh --env=${DEPLOY_ENV} --comprehensive --verbose 2>&1 | tee -a logs/post-deploy-verification.log
                        fi

                        # 执行冒烟测试
                        echo "执行冒烟测试..."
                        if [ -f "scripts/test/smoke-test.sh" ]; then
                            scripts/test/smoke-test.sh --env=${DEPLOY_ENV} 2>&1 | tee -a logs/post-deploy-verification.log
                        fi

                        # 检查关键业务功能
                        echo "检查关键业务功能..."
                        if [ -f "scripts/test/business-function-test.sh" ]; then
                            scripts/test/business-function-test.sh --env=${DEPLOY_ENV} 2>&1 | tee -a logs/post-deploy-verification.log
                        fi

                        # 性能基准测试
                        echo "执行性能基准测试..."
                        if [ -f "scripts/test/performance-baseline-test.sh" ]; then
                            scripts/test/performance-baseline-test.sh --env=${DEPLOY_ENV} 2>&1 | tee -a logs/post-deploy-verification.log || echo "性能基准测试失败"
                        fi

                        # 检查日志错误
                        echo "检查应用日志错误..."
                        if [ -f "scripts/test/log-error-check.sh" ]; then
                            scripts/test/log-error-check.sh --env=${DEPLOY_ENV} 2>&1 | tee -a logs/post-deploy-verification.log || echo "日志检查完成"
                        fi

                        # 生成验证报告
                        echo "生成验证报告..."
                        cat > logs/verification-summary.json << EOF
{
    "deployment_info": {
        "project": "${PROJECT_NAME}",
        "version": "${PROJECT_VERSION}",
        "build_number": "${BUILD_NUMBER}",
        "git_commit": "${env.GIT_COMMIT_SHORT}",
        "git_branch": "${env.GIT_BRANCH_CURRENT}",
        "deploy_env": "${DEPLOY_ENV}",
        "deploy_time": "\$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "approver": "${env.APPROVER ?: 'N/A'}"
    },
    "verification_status": "completed",
    "verification_time": "\$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

                        echo "部署后验证结束时间: \$(date)" >> logs/post-deploy-verification.log
                        echo "部署后验证完成"
                    """
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'logs/post-deploy-verification.log, logs/verification-summary.json', fingerprint: true, allowEmptyArchive: true
                }
                failure {
                    script {
                        echo "部署后验证失败，可能需要回滚..."
                        sh """
                            cd ${PROJECT_ROOT}
                            echo "=== 验证失败诊断 ===" >> logs/post-deploy-verification.log
                            echo "验证失败时间: \$(date)" >> logs/post-deploy-verification.log

                            # 收集失败诊断信息
                            echo "Docker容器状态:" >> logs/post-deploy-verification.log
                            docker ps -a >> logs/post-deploy-verification.log

                            echo "最近的应用日志:" >> logs/post-deploy-verification.log
                            docker-compose -f deploy-package/docker-compose.yml logs --tail=100 >> logs/post-deploy-verification.log || true
                        """
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "=== 构建后清理 ==="

                // 计算总执行时间
                def buildDuration = currentBuild.duration
                def buildDurationMinutes = buildDuration ? (buildDuration / 1000 / 60).round(2) : 0

                echo "总执行时间: ${buildDurationMinutes} 分钟"

                // 归档所有日志文件
                archiveArtifacts artifacts: 'logs/*.log, logs/*.json', fingerprint: true, allowEmptyArchive: true

                // 生成构建摘要报告
                sh """
                    cd ${PROJECT_ROOT}
                    mkdir -p logs

                    # 生成最终构建报告
                    cat > logs/build-summary.json << EOF
{
    "build_info": {
        "project": "${PROJECT_NAME}",
        "version": "${PROJECT_VERSION}",
        "build_number": "${BUILD_NUMBER}",
        "build_url": "${BUILD_URL}",
        "git_commit": "${env.GIT_COMMIT ?: 'N/A'}",
        "git_commit_short": "${env.GIT_COMMIT_SHORT ?: 'N/A'}",
        "git_branch": "${env.GIT_BRANCH_CURRENT ?: 'N/A'}",
        "git_author": "${env.GIT_AUTHOR ?: 'N/A'}",
        "build_type": "${params.BUILD_TYPE}",
        "deploy_env": "${DEPLOY_ENV}",
        "build_status": "${currentBuild.currentResult}",
        "build_duration_minutes": ${buildDurationMinutes},
        "build_start_time": "${new Date(currentBuild.startTimeInMillis).format('yyyy-MM-dd HH:mm:ss')}",
        "build_end_time": "\$(date '+%Y-%m-%d %H:%M:%S')",
        "approver": "${env.APPROVER ?: 'N/A'}",
        "deploy_strategy": "${env.DEPLOY_STRATEGY ?: 'N/A'}"
    },
    "build_parameters": {
        "skip_tests": ${params.SKIP_TESTS},
        "clean_build": ${params.CLEAN_BUILD},
        "force_deploy": ${params.FORCE_DEPLOY},
        "enable_sonar": ${params.ENABLE_SONAR},
        "enable_security_scan": ${params.ENABLE_SECURITY_SCAN}
    }
}
EOF

                    echo "构建摘要报告已生成"
                """

                // 清理Docker资源
                sh """
                    cd ${PROJECT_ROOT}

                    # 清理未使用的Docker镜像（保留最近的）
                    docker image prune -f --filter "until=24h" || true

                    # 清理构建缓存
                    docker builder prune -f --keep-storage=2GB || true

                    # 清理临时文件
                    find . -name "*.tmp" -type f -delete || true
                    find . -name "*.temp" -type f -delete || true

                    echo "清理完成"
                """

                // 归档最终报告
                archiveArtifacts artifacts: 'logs/build-summary.json', fingerprint: true, allowEmptyArchive: true
            }
        }

        success {
            script {
                echo "=== 构建成功 ==="

                def buildDuration = currentBuild.duration
                def buildDurationMinutes = buildDuration ? (buildDuration / 1000 / 60).round(2) : 0

                // 发送成功通知
                sh """
                    echo "=== 构建部署成功通知 ==="
                    echo "项目: ${PROJECT_NAME}"
                    echo "版本: ${PROJECT_VERSION}"
                    echo "环境: ${DEPLOY_ENV}"
                    echo "构建号: ${BUILD_NUMBER_TAG}"
                    echo "Git提交: ${env.GIT_COMMIT_SHORT ?: 'N/A'}"
                    echo "Git分支: ${env.GIT_BRANCH_CURRENT ?: 'N/A'}"
                    echo "构建类型: ${params.BUILD_TYPE}"
                    echo "执行时间: ${buildDurationMinutes} 分钟"
                    echo "完成时间: \$(date)"
                    echo "构建URL: ${BUILD_URL}"

                    # 如果是生产环境，记录部署信息
                    if [ "${DEPLOY_ENV}" = "prod" ]; then
                        echo "审批人: ${env.APPROVER ?: 'N/A'}"
                        echo "部署策略: ${env.DEPLOY_STRATEGY ?: 'N/A'}"
                    fi
                """

                // 这里可以添加邮件、Slack、钉钉等通知逻辑
                // emailext (
                //     subject: "✅ ${PROJECT_NAME} 构建成功 - ${DEPLOY_ENV} 环境",
                //     body: "构建详情请查看: ${BUILD_URL}",
                //     to: "${env.NOTIFICATION_EMAIL}"
                // )
            }
        }

        failure {
            script {
                echo "=== 构建失败 ==="

                def buildDuration = currentBuild.duration
                def buildDurationMinutes = buildDuration ? (buildDuration / 1000 / 60).round(2) : 0

                // 发送失败通知
                sh """
                    echo "=== 构建部署失败通知 ==="
                    echo "项目: ${PROJECT_NAME}"
                    echo "版本: ${PROJECT_VERSION}"
                    echo "环境: ${DEPLOY_ENV}"
                    echo "构建号: ${BUILD_NUMBER_TAG}"
                    echo "Git提交: ${env.GIT_COMMIT_SHORT ?: 'N/A'}"
                    echo "Git分支: ${env.GIT_BRANCH_CURRENT ?: 'N/A'}"
                    echo "构建类型: ${params.BUILD_TYPE}"
                    echo "失败阶段: ${env.STAGE_NAME ?: '未知'}"
                    echo "执行时间: ${buildDurationMinutes} 分钟"
                    echo "失败时间: \$(date)"
                    echo "构建URL: ${BUILD_URL}"

                    # 收集错误日志
                    echo "=== 错误日志摘要 ==="
                    if [ -f "${PROJECT_ROOT}/logs/build.log" ]; then
                        echo "构建日志最后50行:"
                        tail -50 ${PROJECT_ROOT}/logs/build.log
                    fi

                    if [ -f "${PROJECT_ROOT}/logs/deploy.log" ]; then
                        echo "部署日志最后50行:"
                        tail -50 ${PROJECT_ROOT}/logs/deploy.log
                    fi

                    if [ -f "${PROJECT_ROOT}/logs/health-check.log" ]; then
                        echo "健康检查日志最后20行:"
                        tail -20 ${PROJECT_ROOT}/logs/health-check.log
                    fi
                """

                // 这里可以添加失败通知逻辑
                // emailext (
                //     subject: "❌ ${PROJECT_NAME} 构建失败 - ${DEPLOY_ENV} 环境",
                //     body: "构建失败详情请查看: ${BUILD_URL}console",
                //     to: "${env.NOTIFICATION_EMAIL}"
                // )
            }
        }

        unstable {
            script {
                echo "=== 构建不稳定 ==="

                // 发送不稳定通知
                sh """
                    echo "=== 构建不稳定通知 ==="
                    echo "项目: ${PROJECT_NAME}"
                    echo "环境: ${DEPLOY_ENV}"
                    echo "构建号: ${BUILD_NUMBER_TAG}"
                    echo "状态: 不稳定（可能测试失败或质量检查未通过）"
                    echo "时间: \$(date)"
                    echo "构建URL: ${BUILD_URL}"
                """
            }
        }

        aborted {
            script {
                echo "=== 构建被中止 ==="

                // 清理可能的部分部署
                sh """
                    cd ${PROJECT_ROOT}

                    echo "=== 构建中止清理 ==="
                    echo "项目: ${PROJECT_NAME}"
                    echo "环境: ${DEPLOY_ENV}"
                    echo "构建号: ${BUILD_NUMBER_TAG}"
                    echo "中止时间: \$(date)"

                    # 如果部署过程中被中止，尝试清理
                    if [ -f "logs/deploy.log" ]; then
                        echo "检测到部署过程被中止，执行清理..."
                        ${SCRIPTS_DIR}/deploy/cleanup.sh --env=${DEPLOY_ENV} || echo "清理脚本执行失败"
                    fi
                """
            }
        }
    }
}
