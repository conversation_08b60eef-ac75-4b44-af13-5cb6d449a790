<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxWareHouseMapper">

    <sql id="selectMiniWarehouseVo">
        select id, project_name, warehouse_name, operator_id, smarts_type, total_parking,
        province_code, city_code, area_code, address, longitude, latitude, remark, status,
        user_id, delete_flag, carousel_images, lease_property_no, lease_address, lease_detail_address
        from mini_warehouse
    </sql>

    <select id="selectWxWarehouseList" resultType="com.lgjy.wx.domain.WxWareHouse">
        SELECT
            w.id,
            w.project_name,
            w.warehouse_name,
            w.operator_id,
            w.smarts_type,
            w.total_parking,
            w.province_code,
            p.area_name AS province_name,
            w.city_code,
            c.area_name AS city_name,
            w.area_code,
            a.area_name AS area_name,
            w.address,
            w.longitude,
            w.latitude,
            w.remark,
            w.status,
            w.user_id,
            w.delete_flag,
            w.carousel_images,
            m.manager_phone
        FROM
            mini_warehouse w
                LEFT JOIN
            sys_area p ON w.province_code = p.area_code
                LEFT JOIN
            sys_area c ON w.city_code = c.area_code
                LEFT JOIN
            sys_area a ON w.area_code = a.area_code
                LEFT JOIN
            mini_warehouse_manager m ON w.id = m.warehouse_id
        where w.delete_flag = 0 And w.status = 1 and m.delete_flag = 0
    </select>

    <select id="getWarehouseById" resultType="com.lgjy.wx.domain.WxWareHouse">
        SELECT
            w.id,
            w.project_name,
            w.warehouse_name,
            w.operator_id,
            w.smarts_type,
            w.total_parking,
            w.province_code,
            p.area_name AS province_name,
            w.city_code,
            c.area_name AS city_name,
            w.area_code,
            a.area_name AS area_name,
            w.address,
            w.longitude,
            w.latitude,
            w.remark,
            w.status,
            w.user_id,
            w.delete_flag,
            w.carousel_images,
            m.manager_phone
        FROM
            mini_warehouse w
                LEFT JOIN
            sys_area p ON w.province_code = p.area_code
                LEFT JOIN
            sys_area c ON w.city_code = c.area_code
                LEFT JOIN
            sys_area a ON w.area_code = a.area_code
                LEFT JOIN
            mini_warehouse_manager m ON w.id = m.warehouse_id
        where w.delete_flag = 0 And w.status = 1 and m.delete_flag = 0
            and w.id = #{id}
    </select>

    <!-- 根据ID查询场库信息（包含租赁信息） -->
    <select id="selectMiniWarehouseById" parameterType="Long" resultType="com.lgjy.wx.domain.WxWareHouse">
        <include refid="selectMiniWarehouseVo"/>
        where id = #{id} and delete_flag = 0
    </select>
</mapper>