import{i as ee}from"./index-o0oVz-rl.js";import{v as R,_ as ye,r as y,C as te,x as be,D as we,w as ae,E as xe,e as D,c as T,i as e,f as l,h as r,l as b,F as Se,t as d,G,H as $e,I as Ce,J as ke,K as Le,k as Ae,o as I,n as W,L as se,M as oe,N as w,A as De,B as ze}from"./index-Cox2ohSR.js";function Me(p="week"){return R({url:"/system/dashboard/system-statistics",method:"get",params:{period:p}})}function We(p="week",h=null){return R({url:"/system/dashboard/revenue-trend",method:"get",params:{period:p,warehouseId:h}})}function Ye(p="week",h=null){return R({url:"/system/dashboard/user-trend",method:"get",params:{period:p,warehouseId:h}})}function Fe(){return R({url:"/system/dashboard/warehouses",method:"get"})}function Ee(p){return R({url:`/system/dashboard/warehouse/${p}`,method:"get"})}const f=p=>(De("data-v-bbbd95e6"),p=p(),ze(),p),Ge={class:"dashboard-container"},Re={class:"stats-grid"},Ue={class:"stat-content"},Ne={class:"stat-icon parking-lot"},Ve={class:"stat-info"},Te=f(()=>e("div",{class:"stat-label"},"总场库数量",-1)),Ie={class:"stat-value"},Oe={class:"stat-change positive"},Pe={class:"stat-content"},Be={class:"stat-icon parking-space"},je={class:"stat-info"},He=f(()=>e("div",{class:"stat-label"},"总车位数量",-1)),Je={class:"stat-value"},Ke={class:"stat-change positive"},qe={class:"stat-content"},Qe={class:"stat-icon members"},Xe={class:"stat-info"},Ze=f(()=>e("div",{class:"stat-label"},"会员总数量",-1)),et={class:"stat-value"},tt={class:"stat-change positive"},at={class:"stat-content"},st={class:"stat-icon app-users"},ot={class:"stat-info"},lt=f(()=>e("div",{class:"stat-label"},"小程序用户总数",-1)),nt={class:"stat-value"},rt={class:"stat-change positive"},it={class:"stat-content"},ct={class:"stat-icon revenue"},dt={class:"stat-info"},ut={class:"stat-label"},ht={class:"stat-value"},pt={class:"stat-change positive"},ft={class:"controls-container"},_t={class:"warehouse-selector control-item"},vt=f(()=>e("span",{class:"selector-label"},"选择场库：",-1)),mt={key:0,class:"warehouse-detail-wrapper control-item"},gt={class:"warehouse-title"},yt={class:"warehouse-stats"},bt={class:"warehouse-stat-item"},wt=f(()=>e("span",{class:"label"},"车位数量:",-1)),xt={class:"value"},St={class:"warehouse-stat-item"},$t=f(()=>e("span",{class:"label"},"会员数量:",-1)),Ct={class:"value"},kt={class:"warehouse-stat-item"},Lt=f(()=>e("span",{class:"label"},"今日收入:",-1)),At={class:"value"},Dt={class:"time-selector control-item"},zt={class:"charts-section"},Mt={class:"chart-header"},Wt=f(()=>e("span",{class:"chart-title"},"收入趋势",-1)),Yt={class:"chart-controls"},Ft=f(()=>e("span",{class:"control-label"},"Y轴刻度：",-1)),Et={class:"chart-content"},Gt={class:"chart-header"},Rt=f(()=>e("span",{class:"chart-title"},"用户增长趋势",-1)),Ut={class:"chart-controls"},Nt=f(()=>e("span",{class:"control-label"},"Y轴刻度：",-1)),Vt={class:"chart-content"},Tt={__name:"index",setup(p){const h=y("week"),x=y(""),O=y(null),P=y(null),le=[20,50,100],U=y(20),ne=[{value:1e3,label:"千"},{value:1e4,label:"万"},{value:1e5,label:"十万"},{value:1e6,label:"百万"}],N=y(1e4),re={week:"近7天",month:"近一月",halfYear:"近半年",year:"近一年"},_=te({totalParkingLots:0,parkingLotsGrowth:0,totalParkingSpaces:0,parkingSpacesGrowth:0,totalMembers:0,membersGrowth:0,totalRevenue:0,revenueGrowth:0,totalAppUsers:0,appUsersGrowth:0}),H=y([]),J=y([]),K=y(!1),q=y(!1),Y=te({appUsers:0,members:0});let $=null,C=null;const B=async()=>{try{K.value=!0;const t=await Me(h.value);t.code===200?Object.assign(_,t.data):w.error("获取系统统计数据失败："+t.msg)}catch(t){console.error("获取系统统计数据失败:",t),w.error("获取系统统计数据失败，请稍后重试")}finally{K.value=!1}},ie=async()=>{try{const t=await Fe();t.code===200?(H.value=t.data,J.value=de(t.data)):w.error("获取场库列表失败："+t.msg)}catch(t){console.error("获取场库列表失败:",t),w.error("获取场库列表失败，请稍后重试")}},Q=async t=>{if(!t){Object.assign(Y,{appUsers:0,members:0});return}try{const o=await Ee(t);o.code===200?Object.assign(Y,o.data):w.error("获取场库统计失败："+o.msg)}catch(o){console.error("获取场库统计失败:",o),w.error("获取场库统计失败，请稍后重试")}},ce=t=>t>=1e4?(t/1e4).toFixed(1)+"万":t.toLocaleString(),de=t=>{if(!t||t.length===0)return[];const o=t.filter(a=>a.parentId===0||a.parentId==="0"),s=t.filter(a=>a.parentId!==0&&a.parentId!=="0");return o.map(a=>{const c=s.filter(i=>i.parentId==a.id).map(i=>({value:i.id,label:i.warehouseName,isLeaf:!0}));return{value:a.id,label:a.warehouseName,children:c.length>0?c:void 0}})},ue=t=>{const o=H.value.find(s=>s.id===t);return o?o.warehouseName:""},he=()=>{O.value&&($=ee(O.value),k())},k=async()=>{if($)try{q.value=!0;const t=await We(h.value,x.value);if(t.code===200){const{labels:o,data:s}=t.data,a=s&&s.length>0&&s.some(n=>n>0),c=o&&o.length>0?o:["暂无数据"],i=a?s:new Array(c.length).fill(0),v={title:{text:"收入趋势分析",textStyle:{fontSize:16,fontWeight:"bold",color:"#333"},left:"center",top:10},tooltip:{trigger:"axis",backgroundColor:"rgba(50, 50, 50, 0.95)",borderColor:"#409EFF",borderWidth:2,borderRadius:8,textStyle:{color:"#fff",fontSize:14},formatter:n=>{const g=n[0].value;return`
              <div style="padding: 8px;">
                <div style="margin-bottom: 6px; font-weight: bold;">${n[0].axisValue}</div>
                <div style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 10px; height: 10px; background: #409EFF; border-radius: 50%; margin-right: 8px;"></span>
                  收入金额: <span style="color: #67C23A; font-weight: bold;">¥${g.toLocaleString()}</span>
                </div>
              </div>
            `}},legend:{data:["收入趋势"],top:40,textStyle:{color:"#666",fontSize:12}},grid:{left:"8%",right:"8%",bottom:"20%",top:"20%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:c,axisLine:{lineStyle:{color:"#d0d0d0",width:2}},axisLabel:{color:"#666",fontSize:11,margin:10,rotate:a&&o.length>10?45:0},axisTick:{alignWithLabel:!0,lineStyle:{color:"#d0d0d0"}}},yAxis:{type:"value",name:"收入金额 (元)",nameLocation:"middle",nameGap:50,nameTextStyle:{color:"#666",fontSize:12,fontWeight:"bold"},axisLine:{show:!0,lineStyle:{color:"#d0d0d0",width:2}},axisLabel:{show:!0,color:"#666",fontSize:11,formatter:n=>n>=1e4?`¥${(n/1e4).toFixed(1)}万`:n>=1e3?`¥${(n/1e3).toFixed(1)}k`:`¥${n}`},splitLine:{show:!0,lineStyle:{color:"#f5f5f5",type:"dashed"}},axisTick:{show:!0},min:0,max:N.value,interval:N.value/5,scale:!1},series:[{name:"收入趋势",type:"line",smooth:!0,symbol:"circle",symbolSize:a?8:6,lineStyle:{color:a?"#409EFF":"#E6A23C",width:a?3:2,type:a?"solid":"dashed"},itemStyle:{color:a?"#409EFF":"#E6A23C",borderWidth:2,borderColor:"#fff"},areaStyle:a?{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.4)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}:void 0,data:i}]},m=[{type:"text",right:20,bottom:20,style:{text:X(h.value),fontSize:12,fill:"#666",textAlign:"right"}}];a||m.push({type:"text",left:"center",top:"middle",style:{text:`暂无收入数据
请等待数据更新`,fontSize:16,fontWeight:"bold",fill:"#999",textAlign:"center"}}),v.graphic=m,$.setOption(v)}else w.error("获取收入趋势数据失败："+t.msg)}catch(t){console.error("获取收入趋势数据失败:",t),w.error("获取收入趋势数据失败，请稍后重试")}finally{q.value=!1}},pe=()=>{P.value&&(C=ee(P.value),L())},L=async()=>{if(C)try{const t=await Ye(h.value,x.value);if(t.code===200){const{labels:o,membersData:s,appUsersData:a}=t.data,c=s&&s.length>0&&s.some(u=>u>0),i=a&&a.length>0&&a.some(u=>u>0),v=c||i,S=o||[],m=c?s.map(u=>Math.round(u||0)):new Array(o.length).fill(0),n=i?a.map(u=>Math.round(u||0)):new Array(o.length).fill(0),g={title:{text:"用户增长趋势",textStyle:{fontSize:16,fontWeight:"bold",color:"#333"},left:"center",top:10},tooltip:{trigger:"axis",backgroundColor:"rgba(50, 50, 50, 0.95)",borderColor:"#409EFF",borderWidth:2,borderRadius:8,textStyle:{color:"#fff",fontSize:14},formatter:u=>{let M=`<div style="padding: 8px;"><div style="margin-bottom: 6px; font-weight: bold;">${u[0].axisValue}</div>`;return u.forEach(A=>{const E=A.color,V=A.seriesName,ge=A.value;M+=`
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                  <span style="display: inline-block; width: 10px; height: 10px; background: ${E}; border-radius: 50%; margin-right: 8px;"></span>
                  ${V}: <span style="color: ${E}; font-weight: bold;">${ge}人</span>
                </div>
              `}),M+="</div>",M}},legend:{data:["会员数量","小程序用户"],top:40,textStyle:{color:"#666",fontSize:12},itemGap:20},grid:{left:"8%",right:"8%",bottom:"20%",top:"20%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:S,axisLine:{lineStyle:{color:"#d0d0d0",width:2}},axisLabel:{color:"#666",fontSize:11,margin:10,rotate:v&&o&&o.length>10?45:0},axisTick:{alignWithLabel:!0,lineStyle:{color:"#d0d0d0"}}},yAxis:{type:"value",name:"用户数量 (人)",nameLocation:"middle",nameGap:50,nameTextStyle:{color:"#666",fontSize:12,fontWeight:"bold"},axisLine:{lineStyle:{color:"#d0d0d0",width:2}},axisLabel:{color:"#666",fontSize:11,formatter:u=>`${Math.round(u)}人`},splitLine:{lineStyle:{color:"#f5f5f5",type:"dashed"}},min:0,max:U.value,interval:U.value/5},series:[{name:"会员数量",type:"line",smooth:!0,symbol:"circle",symbolSize:c?8:6,lineStyle:{color:c?"#67C23A":"#C0C4CC",width:c?3:2,type:c?"solid":"dashed"},itemStyle:{color:c?"#67C23A":"#C0C4CC",borderWidth:2,borderColor:"#fff"},areaStyle:c?{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(103, 194, 58, 0.3)"},{offset:1,color:"rgba(103, 194, 58, 0.05)"}]}}:void 0,data:m},{name:"小程序用户",type:"line",smooth:!0,symbol:"circle",symbolSize:i?8:6,lineStyle:{color:i?"#E6A23C":"#C0C4CC",width:i?3:2,type:i?"solid":"dashed"},itemStyle:{color:i?"#E6A23C":"#C0C4CC",borderWidth:2,borderColor:"#fff"},areaStyle:i?{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(230, 162, 60, 0.3)"},{offset:1,color:"rgba(230, 162, 60, 0.05)"}]}}:void 0,data:n}]},z=[{type:"text",right:20,bottom:20,style:{text:X(h.value),fontSize:12,fill:"#666",textAlign:"right"}}];v||z.push({type:"text",left:"center",top:"middle",style:{text:`暂无用户增长数据
请等待数据更新`,fontSize:16,fontWeight:"bold",fill:"#999",textAlign:"center"}}),g.graphic=z,C.setOption(g)}else w.error("获取用户趋势数据失败："+t.msg)}catch(t){console.error("获取用户趋势数据失败:",t),w.error("获取用户趋势数据失败，请稍后重试")}},fe=async t=>{await B(),await k(),await L()},_e=async t=>{U.value=t,await L()},X=t=>{const o=new Date,s=o.getFullYear(),a=o.getMonth()+1,c=o.getDate();switch(t){case"week":const i=new Date(o);i.setDate(o.getDate()-6);const v=i.getMonth()+1,S=i.getDate(),m=i.getFullYear();return m===s&&v===a?`${s}年${a}月${S}-${c}日`:m===s?`${s}年${v}月${S}日-${a}月${c}日`:`${m}年${v}月${S}日-${s}年${a}月${c}日`;case"month":const n=new Date(o);n.setDate(o.getDate()-29);const g=n.getMonth()+1,F=n.getDate(),z=n.getFullYear();return z===s&&g===a?`${s}年${a}月${F}-${c}日`:z===s?`${s}年${g}月${F}日-${a}月${c}日`:`${z}年${g}月${F}日-${s}年${a}月${c}日`;case"halfYear":const u=new Date(o);u.setMonth(o.getMonth()-5);const j=u.getMonth()+1,M=u.getFullYear();return M===s?`${s}年${j}-${a}月`:`${M}年${j}月-${s}年${a}月`;case"year":const A=new Date(o);A.setMonth(o.getMonth()-11);const E=A.getMonth()+1,V=A.getFullYear();return V===s?`${s}年${E}-${a}月`:`${V}年${E}月-${s}年${a}月`;default:return`${s}年${a}月`}},ve=async t=>{N.value=t,await k()},me=async t=>{await Q(t),await k(),await L()},Z=()=>{$&&$.resize(),C&&C.resize()};return be(async()=>{await ie(),await B(),we(async()=>{he(),pe(),await k(),await L(),window.addEventListener("resize",Z)})}),ae(h,async()=>{await B(),await k(),await L()}),ae(x,async()=>{x.value&&await Q(x.value),await k(),await L()}),xe(()=>{$&&$.dispose(),C&&C.dispose(),window.removeEventListener("resize",Z)}),(t,o)=>{const s=D("el-icon"),a=D("el-card"),c=D("el-cascader"),i=D("el-radio-button"),v=D("el-radio-group"),S=D("el-button"),m=D("el-button-group");return I(),T("div",Ge,[e("div",Re,[l(a,{class:"stat-card",shadow:"hover"},{default:r(()=>[e("div",Ue,[e("div",Ne,[l(s,null,{default:r(()=>[l(b(Se))]),_:1})]),e("div",Ve,[Te,e("div",Ie,d(_.totalParkingLots),1),e("div",Oe,[l(s,null,{default:r(()=>[l(b(G))]),_:1}),e("span",null,d(_.parkingLotsGrowth)+"%",1)])])])]),_:1}),l(a,{class:"stat-card",shadow:"hover"},{default:r(()=>[e("div",Pe,[e("div",Be,[l(s,null,{default:r(()=>[l(b($e))]),_:1})]),e("div",je,[He,e("div",Je,d(_.totalParkingSpaces),1),e("div",Ke,[l(s,null,{default:r(()=>[l(b(G))]),_:1}),e("span",null,d(_.parkingSpacesGrowth)+"%",1)])])])]),_:1}),l(a,{class:"stat-card",shadow:"hover"},{default:r(()=>[e("div",qe,[e("div",Qe,[l(s,null,{default:r(()=>[l(b(Ce))]),_:1})]),e("div",Xe,[Ze,e("div",et,d(_.totalMembers),1),e("div",tt,[l(s,null,{default:r(()=>[l(b(G))]),_:1}),e("span",null,d(_.membersGrowth)+"%",1)])])])]),_:1}),l(a,{class:"stat-card",shadow:"hover"},{default:r(()=>[e("div",at,[e("div",st,[l(s,null,{default:r(()=>[l(b(ke))]),_:1})]),e("div",ot,[lt,e("div",nt,d(_.totalAppUsers),1),e("div",rt,[l(s,null,{default:r(()=>[l(b(G))]),_:1}),e("span",null,d(_.appUsersGrowth)+"%",1)])])])]),_:1}),l(a,{class:"stat-card revenue-card",shadow:"hover"},{default:r(()=>[e("div",it,[e("div",ct,[l(s,null,{default:r(()=>[l(b(Le))]),_:1})]),e("div",dt,[e("div",ut,d(re[h.value])+"总收入",1),e("div",ht,"¥"+d(ce(_.totalRevenue)),1),e("div",pt,[l(s,null,{default:r(()=>[l(b(G))]),_:1}),e("span",null,d(_.revenueGrowth)+"%",1)])])])]),_:1})]),l(a,{class:"control-panel",shadow:"never"},{default:r(()=>[e("div",ft,[e("div",_t,[vt,l(c,{modelValue:x.value,"onUpdate:modelValue":o[0]||(o[0]=n=>x.value=n),options:J.value,props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"查看特定场库数据",style:{width:"220px"},clearable:"",filterable:"","show-all-levels":!1,onChange:me},null,8,["modelValue","options"])]),x.value?(I(),T("div",mt,[e("div",gt,d(ue(x.value))+" 数据详情",1),e("div",yt,[e("div",bt,[wt,e("span",xt,d(Y.parkingSpaces),1)]),e("div",St,[$t,e("span",Ct,d(Y.members),1)]),e("div",kt,[Lt,e("span",At,"¥"+d(Y.todayRevenue),1)])])])):Ae("",!0),e("div",Dt,[l(v,{modelValue:h.value,"onUpdate:modelValue":o[1]||(o[1]=n=>h.value=n),onChange:fe},{default:r(()=>[l(i,{value:"week"},{default:r(()=>[W("近7天")]),_:1}),l(i,{value:"month"},{default:r(()=>[W("近一月")]),_:1}),l(i,{value:"halfYear"},{default:r(()=>[W("近半年")]),_:1}),l(i,{value:"year"},{default:r(()=>[W("近一年")]),_:1})]),_:1},8,["modelValue"])])])]),_:1}),e("div",zt,[l(a,{class:"chart-container",shadow:"never"},{header:r(()=>[e("div",Mt,[Wt,e("div",Yt,[Ft,l(m,{class:"y-axis-controls"},{default:r(()=>[(I(),T(se,null,oe(ne,n=>l(S,{key:n.value,type:Number(N.value)===Number(n.value)?"primary":"default",size:"small",onClick:g=>ve(n.value)},{default:r(()=>[W(d(n.label),1)]),_:2},1032,["type","onClick"])),64))]),_:1})])])]),default:r(()=>[e("div",Et,[e("div",{ref_key:"revenueChartRef",ref:O,class:"chart",style:{height:"350px"}},null,512)])]),_:1}),l(a,{class:"chart-container",shadow:"never"},{header:r(()=>[e("div",Gt,[Rt,e("div",Ut,[Nt,l(m,{class:"y-axis-controls"},{default:r(()=>[(I(),T(se,null,oe(le,n=>l(S,{key:n,type:U.value===n?"primary":"default",size:"small",onClick:g=>_e(n)},{default:r(()=>[W(d(n)+"人 ",1)]),_:2},1032,["type","onClick"])),64))]),_:1})])])]),default:r(()=>[e("div",Vt,[e("div",{ref_key:"usersChartRef",ref:P,class:"chart",style:{height:"350px"}},null,512)])]),_:1})])])}}},Pt=ye(Tt,[["__scopeId","data-v-bbbd95e6"]]);export{Pt as default};
