"""
日志工具模块
"""
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional
from rich.console import Console
from rich.logging import RichHandler
from rich.text import Text


class BuildLogger:
    """构建日志管理器"""
    
    def __init__(self, log_dir: Optional[str] = None):
        self.console = Console()
        self.log_dir = Path(log_dir) if log_dir else Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"build_{timestamp}.log"
        
        # 配置日志
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志配置"""
        # 创建logger
        self.logger = logging.getLogger("build_tool")
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(
            self.log_file, 
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # Rich控制台处理器
        console_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_path=False,
            rich_tracebacks=True
        )
        console_handler.setLevel(logging.INFO)
        self.logger.addHandler(console_handler)
    
    def info(self, message: str, extra_data: Optional[dict] = None):
        """记录信息日志"""
        self.logger.info(message, extra=extra_data or {})
    
    def warning(self, message: str, extra_data: Optional[dict] = None):
        """记录警告日志"""
        self.logger.warning(message, extra=extra_data or {})
    
    def error(self, message: str, extra_data: Optional[dict] = None):
        """记录错误日志"""
        self.logger.error(message, extra=extra_data or {})
    
    def debug(self, message: str, extra_data: Optional[dict] = None):
        """记录调试日志"""
        self.logger.debug(message, extra=extra_data or {})
    
    def success(self, message: str):
        """显示成功消息"""
        self.console.print(f"✅ {message}", style="bold green")
        self.logger.info(f"SUCCESS: {message}")
    
    def failure(self, message: str):
        """显示失败消息"""
        self.console.print(f"❌ {message}", style="bold red")
        self.logger.error(f"FAILURE: {message}")
    
    def step(self, message: str):
        """显示步骤消息"""
        self.console.print(f"🔄 {message}", style="bold blue")
        self.logger.info(f"STEP: {message}")
    
    def print_header(self, title: str):
        """打印标题头部"""
        self.console.rule(f"[bold blue]{title}[/bold blue]")
        self.logger.info(f"=== {title} ===")
    
    def print_separator(self):
        """打印分隔线"""
        self.console.print("-" * 60, style="dim")
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return str(self.log_file)


# 全局日志实例
logger = BuildLogger()
