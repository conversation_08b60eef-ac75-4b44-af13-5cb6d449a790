# Jenkins 流水线使用指南

## 概述

本文档描述了停车管理系统的完整Jenkins CI/CD流水线配置和使用方法。

## 流水线架构

### 主要阶段

1. **代码拉取** - 从Git仓库拉取最新代码
2. **环境准备** - 准备构建和部署环境
3. **代码验证** - 代码质量检查、安全扫描、语法检查
4. **构建阶段** - 编译和打包应用程序
5. **测试阶段** - 单元测试、集成测试、API测试
6. **镜像构建** - 构建Docker镜像
7. **部署** - 部署到目标环境
8. **健康检查** - 验证部署是否成功
9. **部署后验证** - 全面的功能和性能验证

### 支持的环境

- **test** - 测试环境
- **staging** - 预发布环境  
- **prod** - 生产环境

## 参数配置

### Git配置
- `GIT_REPOSITORY` - Git仓库地址
- `GIT_BRANCH` - Git分支名称
- `GIT_CREDENTIALS_ID` - Git凭据ID

### 构建配置
- `BUILD_TYPE` - 构建类型 (all/backend-only/frontend-only)
- `SKIP_TESTS` - 是否跳过测试
- `CLEAN_BUILD` - 是否清理构建
- `FORCE_DEPLOY` - 是否强制部署

### 质量检查
- `ENABLE_SONAR` - 是否启用SonarQube代码质量检查
- `ENABLE_SECURITY_SCAN` - 是否启用安全扫描
- `SONAR_HOST_URL` - SonarQube服务器地址

## 使用方法

### 1. 基本构建

```bash
# 构建并部署到测试环境
选择参数:
- DEPLOY_ENV: test
- BUILD_TYPE: all
- 其他参数保持默认
```

### 2. 生产环境部署

```bash
# 部署到生产环境需要审批
选择参数:
- DEPLOY_ENV: prod
- BUILD_TYPE: all
- FORCE_DEPLOY: false (推荐)
- ENABLE_SONAR: true
- ENABLE_SECURITY_SCAN: true
```

### 3. 快速修复部署

```bash
# 跳过测试的快速部署
选择参数:
- DEPLOY_ENV: test
- BUILD_TYPE: backend-only (或 frontend-only)
- SKIP_TESTS: true
- CLEAN_BUILD: false
```

## 环境要求

### Jenkins插件
- Git Plugin
- Pipeline Plugin
- Docker Pipeline Plugin
- Credentials Plugin
- Blue Ocean (推荐)

### 系统要求
- Docker & Docker Compose
- Java 11+
- Maven 3.6+
- Node.js 18+
- Git

### 凭据配置
需要在Jenkins中配置以下凭据:
- `git-credentials` - Git仓库访问凭据
- `sonar-token` - SonarQube访问令牌
- `docker-registry-credentials` - Docker镜像仓库凭据

## 监控和日志

### 构建日志
- 构建日志: `logs/build.log`
- 部署日志: `logs/deploy.log`
- 健康检查日志: `logs/health-check.log`
- 验证日志: `logs/post-deploy-verification.log`

### 构建产物
- 构建摘要: `logs/build-summary.json`
- 验证摘要: `logs/verification-summary.json`
- 测试报告: JUnit XML格式
- 覆盖率报告: JaCoCo格式

## 故障排除

### 常见问题

1. **代码拉取失败**
   - 检查Git凭据配置
   - 验证仓库地址和分支名称
   - 确认网络连接

2. **构建失败**
   - 查看构建日志
   - 检查依赖项和环境配置
   - 验证代码语法和编译错误

3. **测试失败**
   - 查看测试报告
   - 检查测试环境配置
   - 验证数据库和外部服务连接

4. **部署失败**
   - 查看部署日志
   - 检查Docker服务状态
   - 验证端口和网络配置

5. **健康检查失败**
   - 查看应用日志
   - 检查服务启动状态
   - 验证数据库和Redis连接

### 回滚操作

```bash
# 自动回滚（生产环境部署失败时）
流水线会自动执行回滚脚本

# 手动回滚
cd /opt/park
scripts/deploy/rollback.sh --env=prod
```

## 最佳实践

### 开发流程
1. 在feature分支开发
2. 提交代码并创建Pull Request
3. 代码审查通过后合并到main分支
4. 自动触发测试环境部署
5. 测试通过后部署到预发布环境
6. 最终部署到生产环境

### 部署策略
- **测试环境**: 自动部署，快速反馈
- **预发布环境**: 手动触发，全面测试
- **生产环境**: 审批部署，谨慎操作

### 安全考虑
- 启用代码质量检查
- 启用安全扫描
- 使用最小权限原则
- 定期更新依赖项
- 监控和日志记录

## 扩展配置

### 通知配置
可以在post部分添加邮件、Slack、钉钉等通知:

```groovy
// 邮件通知示例
emailext (
    subject: "构建结果: ${PROJECT_NAME} - ${currentBuild.currentResult}",
    body: "构建详情: ${BUILD_URL}",
    to: "${env.NOTIFICATION_EMAIL}"
)
```

### 自定义测试
在`scripts/test/`目录下添加自定义测试脚本:
- `api-test.sh` - API接口测试
- `performance-test.sh` - 性能测试
- `security-test.sh` - 安全测试

### 部署策略扩展
支持多种部署策略:
- **rolling** - 滚动部署
- **blue-green** - 蓝绿部署
- **canary** - 金丝雀部署

## 维护和更新

### 定期维护
- 清理旧的构建产物
- 更新Jenkins插件
- 检查和更新脚本
- 监控系统资源使用

### 版本升级
- 测试新版本兼容性
- 更新构建脚本
- 更新部署配置
- 验证所有环境
