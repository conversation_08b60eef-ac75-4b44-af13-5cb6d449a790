# Jenkins 环境配置指南

## 网络架构详解

### 双层转发架构
```
外部访问 → 防火墙转发 → 系统nginx → Docker容器
    ↓           ↓           ↓          ↓
  :3443  →   :433    →   路径分流   →  容器端口
                          ├─/jenkins → :9001 → jenkins-park(:8080)
                          └─/       → :9433 → park-nginx → 后端API
```

### 服务器配置
- **服务器IP**: *************
- **SSH端口**: 1224
- **外部访问端口**: 3443 (HTTPS)
- **防火墙内部端口**: 433
- **Jenkins系统端口**: 9001 → jenkins-park容器(9001:8080)
- **前端系统端口**: 9433 → park-nginx容器

### 访问地址
- **统一入口**: https://test-parknew.lgfw24hours.com:3443
- **Jenkins访问**: https://test-parknew.lgfw24hours.com:3443/jenkins
- **前端访问**: https://test-parknew.lgfw24hours.com:3443/
- **API访问**: https://test-parknew.lgfw24hours.com:3443/api (通过nginx转发)

### 关键容器
- **jenkins-park**: Jenkins服务 (9001:8080)
- **park-nginx**: 前端nginx + API转发
- **park-backend**: 后端API服务
- **park-mysql**: 数据库服务
- **park-redis**: 缓存服务

## Jenkins 凭据配置

### 1. 云效Git凭据配置

在Jenkins中创建凭据 `codeup-credentials`:

**方式一：使用个人令牌（推荐）**
```
凭据类型: Username with password
ID: codeup-credentials
用户名: Aqing2025
密码: pt-v533z24PYISoVysEavo2HJVl_e24f5865-4ff6-4899-b78b-9eea8ff36e01
描述: 云效代码仓库访问凭据
```

**方式二：使用用户名密码**
```
凭据类型: Username with password
ID: codeup-credentials-backup
用户名: Aqing2025
密码: Aqing12345678
描述: 云效代码仓库备用凭据
```

### 2. 服务器SSH凭据（如果需要）

如果需要SSH到其他服务器部署：
```
凭据类型: SSH Username with private key
ID: server-ssh-key
用户名: root (或其他用户)
私钥: [上传SSH私钥文件]
描述: 服务器SSH访问密钥
```

## 仓库信息

### 后端仓库
- **地址**: https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-api.git
- **默认分支**: park

### 前端仓库
- **地址**: https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-ui.git
- **默认分支**: test

## Jenkins Pipeline 配置步骤

### 1. 创建Pipeline任务

1. 登录Jenkins: https://*************:3433
2. 点击"新建任务"
3. 输入任务名称: `park-new-pipeline`
4. 选择"Pipeline"类型
5. 点击"确定"

### 2. 配置Pipeline

在Pipeline配置页面：

**Pipeline定义**:
```
Definition: Pipeline script from SCM
SCM: Git
Repository URL: https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-api.git
Credentials: codeup-credentials
Branch: park
Script Path: scripts/jenkins/Jenkinsfile
```

**✅ 脚本位置已优化**:
- scripts文件夹现在位于后端仓库中 (`park-api/scripts/`)
- 所有脚本都进行版本控制，确保部署一致性
- Jenkins可以直接从后端仓库拉取完整的CI/CD脚本

### 3. 配置构建触发器（可选）

**轮询SCM**:
```
Schedule: H/5 * * * *  # 每5分钟检查一次代码变更
```

**Webhook触发**（推荐）:
在云效仓库设置中添加Webhook:
```
URL: https://*************:3433/git/notifyCommit?url=https://codeup.aliyun.com/67bc0bb5b485a595fdcb7f20/park-new/park-api.git
```

## 环境变量配置

在Jenkins系统配置中添加全局环境变量：

```
JAVA_HOME=/usr/lib/jvm/java-11-openjdk
MAVEN_HOME=/opt/maven
NODE_HOME=/usr/local/node
DOCKER_HOST=unix:///var/run/docker.sock
```

## Docker配置

确保Jenkins用户有Docker权限：
```bash
# 将jenkins用户添加到docker组
sudo usermod -aG docker jenkins

# 重启Jenkins服务
sudo systemctl restart jenkins
```

## 网络配置验证

### 端口转发验证
```bash
# 检查防火墙转发规则
sudo iptables -t nat -L -n

# 检查nginx配置
sudo nginx -t
sudo systemctl status nginx

# 检查端口监听
netstat -tlnp | grep -E ':(3433|433|9001|8080|9433)'
```

### 服务状态检查
```bash
# 检查Jenkins状态
sudo systemctl status jenkins

# 检查Docker状态
sudo systemctl status docker

# 检查nginx状态
sudo systemctl status nginx
```

## 首次构建测试

### 1. 手动触发构建

1. 进入Pipeline任务页面
2. 点击"Build with Parameters"
3. 选择参数:
   - BUILD_TYPE: all
   - DEPLOY_ENV: test
   - 其他参数保持默认
4. 点击"开始构建"

### 2. 查看构建日志

1. 点击构建号进入构建详情
2. 点击"Console Output"查看详细日志
3. 检查每个阶段的执行情况

### 3. 验证部署结果

构建成功后验证：
```bash
# 检查容器状态
docker ps

# 检查应用日志
docker logs park-new-backend
docker logs park-new-frontend

# 访问应用
curl http://*************:8080/actuator/health
curl https://test-parknew.lgfw24hours.com:3443
```

## 故障排除

### 常见问题

1. **Git拉取失败**
   - 检查凭据配置是否正确
   - 验证网络连接到云效
   - 检查仓库地址和分支名称

2. **Docker权限问题**
   - 确保jenkins用户在docker组中
   - 重启Jenkins服务

3. **端口冲突**
   - 检查端口占用情况
   - 停止冲突的服务

4. **构建失败**
   - 查看详细的构建日志
   - 检查依赖项和环境配置

### 日志位置

- **Jenkins日志**: `/var/log/jenkins/jenkins.log`
- **构建日志**: Jenkins Web界面 → 构建详情 → Console Output
- **应用日志**: `/opt/park-new/logs/`

## 安全建议

1. **定期更新凭据**
   - 定期轮换Git访问令牌
   - 使用最小权限原则

2. **网络安全**
   - 限制Jenkins访问IP
   - 使用HTTPS访问
   - 定期更新防火墙规则

3. **备份策略**
   - 定期备份Jenkins配置
   - 备份构建产物和日志
   - 备份数据库和应用数据
