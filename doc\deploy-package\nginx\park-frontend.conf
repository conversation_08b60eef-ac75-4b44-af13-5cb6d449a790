# 上游后端API服务器配置 - Docker网络内部通信
upstream park_api_backend {
    server park-gateway:8080;  # Docker容器内部网络通信
    # 备用网关实例（如果有的话）
    # server park-gateway-2:8080 backup;
}

# Jenkins上游配置
upstream jenkins_backend {
    server jenkins-park:8080;
    keepalive 32;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name test-parknew.lgfw24hours.com localhost;

    # 重定向所有HTTP请求到HTTPS
    return 301 https://$host:9443$request_uri;
}

# HTTPS主配置
server {
    listen 443 ssl http2;
    server_name test-parknew.lgfw24hours.com localhost;
    
    # SSL证书配置 - Docker容器内路径
    ssl_certificate /etc/nginx/ssl/test-parknew.lgfw24hours.com.pem;
    ssl_certificate_key /etc/nginx/ssl/test-parknew.lgfw24hours.com.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头设置
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 客户端上传限制
    client_max_body_size 100m;

    # 前端静态文件根目录 - Docker容器内挂载路径
    root /usr/share/nginx/html;
    index index.html;
    
    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Jenkins代理配置 - 使用^~确保优先匹配
    location ^~ /jenkins/ {
        proxy_pass http://jenkins_backend/;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Jenkins特殊头
        proxy_set_header X-Forwarded-Ssl on;
        proxy_set_header Connection "";

        # 超时设置
        proxy_connect_timeout 150s;
        proxy_send_timeout 100s;
        proxy_read_timeout 100s;

        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Jenkins特殊配置
        proxy_redirect http:// https://;
        proxy_redirect https://jenkins-park:8080/ https://$host:9443/jenkins/;
    }

    # Jenkins CLI支持
    location /jenkins/cli {
        proxy_pass http://jenkins_backend/jenkins/cli;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Jenkins WebSocket支持
    location /jenkins/ws/ {
        proxy_pass http://jenkins_backend/jenkins/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API代理配置
    location /prod-api/ {
        proxy_pass http://park_api_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # CORS处理（如果需要）
        add_header 'Access-Control-Allow-Origin' '$http_origin' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        
        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    
    # 静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
    }
    
    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
    
    # Vue.js SPA路由支持 - 关键配置
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 访问日志
    access_log /var/log/nginx/park-frontend.access.log;
    error_log /var/log/nginx/park-frontend.error.log;
}
