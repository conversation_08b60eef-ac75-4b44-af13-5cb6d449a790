import{v as C,O as fe,d as _e,r as m,C as ye,T as ge,e as a,Q as G,c as N,o as r,R as w,f as t,S as ve,l as n,h as l,m as H,L as I,M as D,j as f,n as c,P as J,i as W,t as X}from"./index-D_FV2sri.js";import{C as Y}from"./index-C9zsUI-h.js";function be(d){return C({url:"/system/notice/list",method:"get",params:d})}function he(d){return C({url:"/system/notice/"+d,method:"get"})}function we(d){return C({url:"/system/notice",method:"post",data:d})}function Ve(d){return C({url:"/system/notice",method:"put",data:d})}function Te(d){return C({url:"/system/notice/"+d,method:"delete"})}const Ce={class:"app-container"},ke={class:"dialog-footer"},Se=fe({name:"Notice"}),xe=Object.assign(Se,{components:{CustomPagination:Y}},{setup(d){const{proxy:_}=_e(),{sys_notice_status:$,sys_notice_type:U}=_.useDict("sys_notice_status","sys_notice_type"),q=m([]),y=m(!1),x=m(!0),k=m(!0),B=m([]),z=m(!0),F=m(!0),L=m(0),P=m(""),Z=ye({form:{},queryParams:{pageNum:1,pageSize:10,noticeTitle:void 0,createBy:void 0,status:void 0},rules:{noticeTitle:[{required:!0,message:"公告标题不能为空",trigger:"blur"}],noticeType:[{required:!0,message:"公告类型不能为空",trigger:"change"}]}}),{queryParams:s,form:i,rules:ee}=ge(Z);function v(){x.value=!0,be(s.value).then(u=>{q.value=u.rows,L.value=u.total,x.value=!1})}function te(){y.value=!1,R()}function R(){i.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},_.resetForm("noticeRef")}function S(){s.value.pageNum=1,v()}function le(){_.resetForm("queryRef"),S()}function ne(u){B.value=u.map(o=>o.noticeId),z.value=u.length!=1,F.value=!u.length}function oe(){R(),y.value=!0,P.value="添加公告"}function Q(u){R();const o=u.noticeId||B.value;he(o).then(V=>{i.value=V.data,y.value=!0,P.value="修改公告"})}function ae(){_.$refs.noticeRef.validate(u=>{u&&(i.value.noticeId!=null?Ve(i.value).then(o=>{_.$modal.msgSuccess("修改成功"),y.value=!1,v()}):we(i.value).then(o=>{_.$modal.msgSuccess("新增成功"),y.value=!1,v()}))})}function E(u){const o=u.noticeId||B.value;_.$modal.confirm('是否确认删除公告编号为"'+o+'"的数据项？').then(function(){return Te(o)}).then(()=>{v(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}return v(),(u,o)=>{const V=a("el-input"),g=a("el-form-item"),K=a("el-option"),j=a("el-select"),p=a("el-button"),O=a("el-form"),b=a("el-col"),ie=a("right-toolbar"),A=a("el-row"),h=a("el-table-column"),M=a("dict-tag"),ue=a("el-table"),se=a("el-radio"),re=a("el-radio-group"),de=a("editor"),ce=a("el-dialog"),T=G("hasPermi"),pe=G("loading");return r(),N("div",Ce,[w(t(O,{model:n(s),ref:"queryRef",inline:!0},{default:l(()=>[t(g,{label:"公告标题",prop:"noticeTitle"},{default:l(()=>[t(V,{modelValue:n(s).noticeTitle,"onUpdate:modelValue":o[0]||(o[0]=e=>n(s).noticeTitle=e),placeholder:"请输入公告标题",clearable:"",style:{width:"200px"},onKeyup:H(S,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"操作人员",prop:"createBy"},{default:l(()=>[t(V,{modelValue:n(s).createBy,"onUpdate:modelValue":o[1]||(o[1]=e=>n(s).createBy=e),placeholder:"请输入操作人员",clearable:"",style:{width:"200px"},onKeyup:H(S,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"类型",prop:"noticeType"},{default:l(()=>[t(j,{modelValue:n(s).noticeType,"onUpdate:modelValue":o[2]||(o[2]=e=>n(s).noticeType=e),placeholder:"公告类型",clearable:"",style:{width:"200px"}},{default:l(()=>[(r(!0),N(I,null,D(n(U),e=>(r(),f(K,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,null,{default:l(()=>[t(p,{type:"primary",icon:"Search",onClick:S},{default:l(()=>[c("搜索")]),_:1}),t(p,{icon:"Refresh",onClick:le},{default:l(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,n(k)]]),t(A,{gutter:10,class:"mb8"},{default:l(()=>[t(b,{span:1.5},{default:l(()=>[w((r(),f(p,{type:"primary",plain:"",icon:"Plus",onClick:oe},{default:l(()=>[c("新增")]),_:1})),[[T,["system:notice:add"]]])]),_:1}),t(b,{span:1.5},{default:l(()=>[w((r(),f(p,{type:"success",plain:"",icon:"Edit",disabled:n(z),onClick:Q},{default:l(()=>[c("修改")]),_:1},8,["disabled"])),[[T,["system:notice:edit"]]])]),_:1}),t(b,{span:1.5},{default:l(()=>[w((r(),f(p,{type:"danger",plain:"",icon:"Delete",disabled:n(F),onClick:E},{default:l(()=>[c("删除")]),_:1},8,["disabled"])),[[T,["system:notice:remove"]]])]),_:1}),t(ie,{showSearch:n(k),"onUpdate:showSearch":o[3]||(o[3]=e=>J(k)?k.value=e:null),onQueryTable:v},null,8,["showSearch"])]),_:1}),w((r(),f(ue,{data:n(q),onSelectionChange:ne},{default:l(()=>[t(h,{type:"selection",width:"55",align:"center"}),t(h,{label:"公告标题",align:"center",prop:"noticeTitle","show-overflow-tooltip":!0}),t(h,{label:"公告类型",align:"center",prop:"noticeType",width:"100"},{default:l(e=>[t(M,{options:n(U),value:e.row.noticeType},null,8,["options","value"])]),_:1}),t(h,{label:"状态",align:"center",prop:"status",width:"100"},{default:l(e=>[t(M,{options:n($),value:e.row.status},null,8,["options","value"])]),_:1}),t(h,{label:"创建者",align:"center",prop:"createBy",width:"100"}),t(h,{label:"创建时间",align:"center",prop:"createTime",width:"100"},{default:l(e=>[W("span",null,X(u.parseTime(e.row.createTime,"{y}-{m}-{d}")),1)]),_:1}),t(h,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(e=>[w((r(),f(p,{link:"",type:"primary",icon:"Edit",onClick:me=>Q(e.row)},{default:l(()=>[c("修改")]),_:2},1032,["onClick"])),[[T,["system:notice:edit"]]]),w((r(),f(p,{link:"",type:"primary",icon:"Delete",onClick:me=>E(e.row)},{default:l(()=>[c("删除")]),_:2},1032,["onClick"])),[[T,["system:notice:remove"]]])]),_:1})]),_:1},8,["data"])),[[pe,n(x)]]),t(Y,{total:n(L),"current-page":n(s).pageNum,"onUpdate:currentPage":o[4]||(o[4]=e=>n(s).pageNum=e),"page-size":n(s).pageSize,"onUpdate:pageSize":o[5]||(o[5]=e=>n(s).pageSize=e),onPagination:v},null,8,["total","current-page","page-size"]),t(ce,{title:n(P),modelValue:n(y),"onUpdate:modelValue":o[10]||(o[10]=e=>J(y)?y.value=e:null),width:"780px","append-to-body":""},{footer:l(()=>[W("div",ke,[t(p,{type:"primary",onClick:ae},{default:l(()=>[c("确 定")]),_:1}),t(p,{onClick:te},{default:l(()=>[c("取 消")]),_:1})])]),default:l(()=>[t(O,{ref:"noticeRef",model:n(i),rules:n(ee),"label-width":"80px"},{default:l(()=>[t(A,null,{default:l(()=>[t(b,{span:12},{default:l(()=>[t(g,{label:"公告标题",prop:"noticeTitle"},{default:l(()=>[t(V,{modelValue:n(i).noticeTitle,"onUpdate:modelValue":o[6]||(o[6]=e=>n(i).noticeTitle=e),placeholder:"请输入公告标题"},null,8,["modelValue"])]),_:1})]),_:1}),t(b,{span:12},{default:l(()=>[t(g,{label:"公告类型",prop:"noticeType"},{default:l(()=>[t(j,{modelValue:n(i).noticeType,"onUpdate:modelValue":o[7]||(o[7]=e=>n(i).noticeType=e),placeholder:"请选择"},{default:l(()=>[(r(!0),N(I,null,D(n(U),e=>(r(),f(K,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:24},{default:l(()=>[t(g,{label:"状态"},{default:l(()=>[t(re,{modelValue:n(i).status,"onUpdate:modelValue":o[8]||(o[8]=e=>n(i).status=e)},{default:l(()=>[(r(!0),N(I,null,D(n($),e=>(r(),f(se,{key:e.value,value:e.value},{default:l(()=>[c(X(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:24},{default:l(()=>[t(g,{label:"内容"},{default:l(()=>[t(de,{modelValue:n(i).noticeContent,"onUpdate:modelValue":o[9]||(o[9]=e=>n(i).noticeContent=e),"min-height":192},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{xe as default};
