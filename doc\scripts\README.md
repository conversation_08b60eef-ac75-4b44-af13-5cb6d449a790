# 停车管理系统 CI/CD 脚本集合

## 📁 脚本目录结构

```
scripts/
├── README.md                    # 本文档
├── jenkins/
│   ├── Jenkinsfile             # Jenkins Pipeline 脚本
│   └── build-config.groovy     # 构建配置
├── build/
│   ├── build-all.sh            # 完整构建脚本
│   ├── build-backend.sh        # 后端构建脚本
│   ├── build-frontend.sh       # 前端构建脚本
│   └── docker-build.sh         # Docker镜像构建脚本
├── deploy/
│   ├── deploy.sh               # 主部署脚本
│   ├── health-check.sh         # 健康检查脚本
│   ├── rollback.sh             # 回滚脚本
│   └── backup.sh               # 备份脚本
├── utils/
│   ├── common.sh               # 通用函数库
│   ├── logger.sh               # 日志工具
│   └── notification.sh         # 通知工具
└── config/
    ├── services.conf           # 服务配置
    └── environment.conf        # 环境配置
```

## 🚀 快速开始

### 1. 设置执行权限
```bash
chmod +x scripts/**/*.sh
```

### 2. 配置环境变量
```bash
# 复制环境配置模板
cp scripts/config/environment.conf.example scripts/config/environment.conf

# 编辑配置文件
vim scripts/config/environment.conf
```

### 3. 执行构建部署
```bash
# 完整构建和部署
./scripts/build/build-all.sh

# 部署到测试环境
./scripts/deploy/deploy.sh test

# 健康检查
./scripts/deploy/health-check.sh
```

## 📋 脚本说明

### 构建脚本
- `build-all.sh`: 完整构建流程，包括后端和前端
- `build-backend.sh`: 只构建后端微服务
- `build-frontend.sh`: 只构建前端项目
- `docker-build.sh`: 构建Docker镜像

### 部署脚本
- `deploy.sh`: 主部署脚本，支持测试和生产环境
- `health-check.sh`: 服务健康检查
- `rollback.sh`: 快速回滚到上一个版本
- `backup.sh`: 数据和配置备份

### Jenkins集成
- `Jenkinsfile`: Jenkins Pipeline配置
- `build-config.groovy`: 构建参数配置

## 🔧 配置说明

### 服务配置 (services.conf)
定义了所有微服务的基本信息：
- 服务名称
- 端口配置
- 健康检查端点
- 依赖关系

### 环境配置 (environment.conf)
定义了不同环境的配置：
- 服务器地址
- 数据库连接
- Redis配置
- Nacos配置

## 📊 使用示例

### 构建特定服务
```bash
./scripts/build/build-backend.sh --service=park-gateway
```

### 部署到测试环境
```bash
./scripts/deploy/deploy.sh --env=test --service=all
```

### 回滚操作
```bash
./scripts/deploy/rollback.sh --env=test --version=previous
```

### 健康检查
```bash
./scripts/deploy/health-check.sh --env=test --service=park-gateway
```

## 🔍 故障排除

### 查看日志
```bash
# 查看构建日志
tail -f logs/build.log

# 查看部署日志
tail -f logs/deploy.log
```

### 常见问题
1. **构建失败**: 检查Maven配置和依赖
2. **部署失败**: 检查Docker服务和网络连接
3. **健康检查失败**: 检查服务启动状态和端口配置

## 📞 支持

如有问题，请查看日志文件或联系开发团队。
