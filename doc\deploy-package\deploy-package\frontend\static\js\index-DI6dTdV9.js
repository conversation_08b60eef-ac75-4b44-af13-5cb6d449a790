import{_ as M,r as v,e as o,c as m,o as h,i as s,f as t,n as i,h as e,L as S,M as q,j as I,t as c,k as C,l as k,a4 as O,a3 as L,A as R,B as j}from"./index-D_FV2sri.js";const n=p=>(R("data-v-38c92334"),p=p(),j(),p),A={class:"app-container"},D={class:"merchant-voucher-container"},E={class:"page-header"},F={class:"page-title"},U=n(()=>s("p",{class:"page-description"},"这是商户购劵页面 - 管理商户的券类产品购买",-1)),G={class:"voucher-stats"},H={class:"stat-content"},J={class:"stat-icon total"},K=n(()=>s("div",{class:"stat-info"},[s("div",{class:"stat-number"},"2,456"),s("div",{class:"stat-label"},"总购券数")],-1)),Q={class:"stat-content"},W={class:"stat-icon used"},X=n(()=>s("div",{class:"stat-info"},[s("div",{class:"stat-number"},"1,823"),s("div",{class:"stat-label"},"已使用券")],-1)),Y={class:"stat-content"},Z={class:"stat-icon revenue"},$=n(()=>s("div",{class:"stat-info"},[s("div",{class:"stat-number"},"¥45,680"),s("div",{class:"stat-label"},"购券收入")],-1)),ss={class:"stat-content"},ts={class:"stat-icon today"},es=n(()=>s("div",{class:"stat-info"},[s("div",{class:"stat-number"},"89"),s("div",{class:"stat-label"},"今日购券")],-1)),as={class:"card-header"},os=n(()=>s("span",null,"券类产品",-1)),cs={class:"voucher-grid"},ls={class:"voucher-header"},is={class:"voucher-type"},ns={class:"voucher-content"},ds={class:"voucher-title"},rs={class:"voucher-desc"},_s={class:"voucher-price"},us={class:"current-price"},ps={key:0,class:"original-price"},hs={class:"voucher-info"},vs={class:"info-item"},ms=n(()=>s("span",{class:"label"},"有效期:",-1)),fs={class:"value"},ys={class:"info-item"},gs=n(()=>s("span",{class:"label"},"已售:",-1)),ws={class:"value"},bs={class:"voucher-actions"},Ns={class:"card-header"},ks=n(()=>s("span",null,"购券记录",-1)),Ts={class:"header-actions"},Vs={class:"amount-text"},xs={__name:"index",setup(p){const f=v(""),T=v([{type:"停车券",title:"停车优惠券",description:"单次停车立减20元",price:15,originalPrice:20,validity:30,sold:456,status:"上架"},{type:"洗车券",title:"免费洗车券",description:"指定门店免费洗车一次",price:25,originalPrice:35,validity:60,sold:234,status:"上架"},{type:"服务券",title:"道路救援券",description:"24小时道路救援服务",price:50,originalPrice:null,validity:90,sold:89,status:"上架"},{type:"优惠券",title:"8折优惠券",description:"全场服务8折优惠",price:10,originalPrice:15,validity:15,sold:678,status:"下架"}]),V=v([{orderNo:"VO202401200001",customerName:"张三",voucherType:"停车券",quantity:5,amount:75,payMethod:"微信支付",status:"已支付",purchaseTime:"2024-01-20 14:30:00"},{orderNo:"VO202401200002",customerName:"李四",voucherType:"洗车券",quantity:2,amount:50,payMethod:"支付宝",status:"已支付",purchaseTime:"2024-01-20 15:45:00"},{orderNo:"VO202401200003",customerName:"王五",voucherType:"服务券",quantity:1,amount:50,payMethod:"银联支付",status:"待支付",purchaseTime:"2024-01-20 16:20:00"}]),x=y=>{switch(y){case"已支付":return"success";case"待支付":return"warning";case"已退款":return"info";case"已取消":return"danger";default:return"info"}};return(y,g)=>{const _=o("svg-icon"),d=o("el-card"),u=o("el-col"),w=o("el-row"),b=o("el-icon"),r=o("el-button"),N=o("el-tag"),z=o("el-date-picker"),l=o("el-table-column"),P=o("el-table");return h(),m("div",A,[s("div",D,[s("div",E,[s("h1",F,[t(_,{"icon-class":"merchant-voucher",class:"title-icon"}),i(" 商户购劵管理 ")]),U]),s("div",G,[t(w,{gutter:20},{default:e(()=>[t(u,{span:6},{default:e(()=>[t(d,{class:"stat-card",shadow:"hover"},{default:e(()=>[s("div",H,[s("div",J,[t(_,{"icon-class":"shopping"})]),K])]),_:1})]),_:1}),t(u,{span:6},{default:e(()=>[t(d,{class:"stat-card",shadow:"hover"},{default:e(()=>[s("div",Q,[s("div",W,[t(_,{"icon-class":"star"})]),X])]),_:1})]),_:1}),t(u,{span:6},{default:e(()=>[t(d,{class:"stat-card",shadow:"hover"},{default:e(()=>[s("div",Y,[s("div",Z,[t(_,{"icon-class":"money"})]),$])]),_:1})]),_:1}),t(u,{span:6},{default:e(()=>[t(d,{class:"stat-card",shadow:"hover"},{default:e(()=>[s("div",ss,[s("div",ts,[t(_,{"icon-class":"chart"})]),es])]),_:1})]),_:1})]),_:1})]),t(d,{class:"voucher-products-card",shadow:"never"},{header:e(()=>[s("div",as,[os,t(r,{type:"primary",size:"small"},{default:e(()=>[t(b,null,{default:e(()=>[t(k(O))]),_:1}),i(" 新增券类 ")]),_:1})])]),default:e(()=>[s("div",cs,[t(w,{gutter:20},{default:e(()=>[(h(!0),m(S,null,q(T.value,(a,B)=>(h(),I(u,{span:8,key:B},{default:e(()=>[t(d,{class:"voucher-card",shadow:"hover"},{default:e(()=>[s("div",ls,[s("div",is,c(a.type),1),t(N,{type:a.status==="上架"?"success":"info",size:"small"},{default:e(()=>[i(c(a.status),1)]),_:2},1032,["type"])]),s("div",ns,[s("div",ds,c(a.title),1),s("div",rs,c(a.description),1),s("div",_s,[s("span",us,"¥"+c(a.price),1),a.originalPrice?(h(),m("span",ps,"¥"+c(a.originalPrice),1)):C("",!0)]),s("div",hs,[s("div",vs,[ms,s("span",fs,c(a.validity)+"天",1)]),s("div",ys,[gs,s("span",ws,c(a.sold)+"张",1)])])]),s("div",bs,[t(r,{type:"primary",size:"small"},{default:e(()=>[i("立即购买")]),_:1}),t(r,{type:"info",size:"small",link:""},{default:e(()=>[i("详情")]),_:1})])]),_:2},1024)]),_:2},1024))),128))]),_:1})])]),_:1}),t(d,{class:"purchase-records-card",shadow:"never"},{header:e(()=>[s("div",Ns,[ks,s("div",Ts,[t(z,{modelValue:f.value,"onUpdate:modelValue":g[0]||(g[0]=a=>f.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small",style:{"margin-right":"10px"}},null,8,["modelValue"]),t(r,{type:"success",size:"small"},{default:e(()=>[t(b,null,{default:e(()=>[t(k(L))]),_:1}),i(" 导出 ")]),_:1})])])]),default:e(()=>[t(P,{data:V.value,style:{width:"100%"}},{default:e(()=>[t(l,{prop:"orderNo",label:"订单号",width:"180"}),t(l,{prop:"customerName",label:"购买人",width:"120"}),t(l,{prop:"voucherType",label:"券类型",width:"120"}),t(l,{prop:"quantity",label:"数量",width:"80"}),t(l,{prop:"amount",label:"金额",width:"100"},{default:e(a=>[s("span",Vs,"¥"+c(a.row.amount),1)]),_:1}),t(l,{prop:"payMethod",label:"支付方式",width:"120"}),t(l,{prop:"status",label:"状态",width:"100"},{default:e(a=>[t(N,{type:x(a.row.status),size:"small"},{default:e(()=>[i(c(a.row.status),1)]),_:2},1032,["type"])]),_:1}),t(l,{prop:"purchaseTime",label:"购买时间",width:"180"}),t(l,{label:"操作",width:"150"},{default:e(a=>[t(r,{type:"primary",size:"small",link:""},{default:e(()=>[i("查看")]),_:1}),t(r,{type:"warning",size:"small",link:""},{default:e(()=>[i("退款")]),_:1})]),_:1})]),_:1},8,["data"])]),_:1})])])}}},Ps=M(xs,[["__scopeId","data-v-38c92334"]]);export{Ps as default};
