<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniVipPackageMapper">

    <resultMap type="com.lgjy.system.domain.MiniVipPackage" id="MiniVipPackageResult">
        <result property="id" column="id" />
        <result property="warehouseId" column="warehouse_id" />
        <result property="warehouseName" column="warehouse_name" />
        <result property="parentWarehouseName" column="parent_warehouse_name" />
        <result property="packageType" column="package_type" />
        <result property="packageName" column="package_name" />
        <result property="packagePrice" column="package_price" />
        <result property="giveDays" column="give_days" />
        <result property="packageRule" column="package_rule" />
        <result property="parkType" column="park_type" />
        <result property="ruleType" column="rule_type" />
        <result property="preferBeginTime" column="prefer_begin_time" />
        <result property="preferEndTime" column="prefer_end_time" />
        <result property="remark" column="remark" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectMiniVipPackageVo">
        select vp.id, vp.warehouse_id, vp.package_type, vp.package_name, vp.package_price,
               vp.give_days, vp.package_rule, vp.park_type, vp.rule_type,
               vp.prefer_begin_time, vp.prefer_end_time, vp.remark, vp.delete_flag,
               vp.create_by, coalesce(cu.nick_name, cu.user_name, vp.create_by) as create_by_name, vp.create_time,
               vp.update_by, coalesce(uu.nick_name, uu.user_name, vp.update_by) as update_by_name, vp.update_time,
               w.warehouse_name,
               case
                   when w.parent_id = 0 then null
                   else pw.warehouse_name
               end as parent_warehouse_name
        from mini_vip_package vp
        left join sys_user cu on vp.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on vp.update_by = uu.user_id and uu.delete_flag = '0'
        left join mini_warehouse w on vp.warehouse_id = w.id and w.delete_flag = 0
        left join mini_warehouse pw on w.parent_id = pw.id and pw.delete_flag = 0 and pw.parent_id = 0
    </sql>

    <select id="selectMiniVipPackageList" parameterType="com.lgjy.system.domain.MiniVipPackage" resultMap="MiniVipPackageResult">
        <include refid="selectMiniVipPackageVo"/>
        <where>
            vp.delete_flag = 0
            <if test="warehouseId != null"> and vp.warehouse_id = #{warehouseId}</if>
            <if test="packageName != null and packageName != ''"> and vp.package_name like concat('%', #{packageName}, '%')</if>
            <if test="packageType != null"> and vp.package_type = #{packageType}</if>
        </where>
        order by vp.create_time desc
    </select>

    <select id="selectMiniVipPackageById" parameterType="Long" resultMap="MiniVipPackageResult">
        <include refid="selectMiniVipPackageVo"/>
        where vp.id = #{id} and vp.delete_flag = 0
    </select>

    <select id="selectPackagesByWarehouseId" parameterType="Long" resultMap="MiniVipPackageResult">
        <include refid="selectMiniVipPackageVo"/>
        where vp.warehouse_id = #{warehouseId} and vp.delete_flag = 0
        order by vp.create_time desc
    </select>

    <insert id="insertMiniVipPackage" parameterType="com.lgjy.system.domain.MiniVipPackage" useGeneratedKeys="true" keyProperty="id">
        insert into mini_vip_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="packageType != null">package_type,</if>
            <if test="packageName != null and packageName != ''">package_name,</if>
            <if test="packagePrice != null">package_price,</if>
            <if test="giveDays != null">give_days,</if>
            <if test="packageRule != null">package_rule,</if>
            <if test="parkType != null">park_type,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="preferBeginTime != null">prefer_begin_time,</if>
            <if test="preferEndTime != null">prefer_end_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="packageType != null">#{packageType},</if>
            <if test="packageName != null and packageName != ''">#{packageName},</if>
            <if test="packagePrice != null">#{packagePrice},</if>
            <if test="giveDays != null">#{giveDays},</if>
            <if test="packageRule != null">#{packageRule},</if>
            <if test="parkType != null">#{parkType},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="preferBeginTime != null">#{preferBeginTime},</if>
            <if test="preferEndTime != null">#{preferEndTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateMiniVipPackage" parameterType="com.lgjy.system.domain.MiniVipPackage">
        update mini_vip_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="packageType != null">package_type = #{packageType},</if>
            <if test="packageName != null and packageName != ''">package_name = #{packageName},</if>
            <if test="packagePrice != null">package_price = #{packagePrice},</if>
            <if test="giveDays != null">give_days = #{giveDays},</if>
            <if test="packageRule != null">package_rule = #{packageRule},</if>
            <if test="parkType != null">park_type = #{parkType},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="preferBeginTime != null">prefer_begin_time = #{preferBeginTime},</if>
            <if test="preferEndTime != null">prefer_end_time = #{preferEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniVipPackageById" parameterType="Long">
        update mini_vip_package set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniVipPackageByIds" parameterType="String">
        update mini_vip_package set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
