#!/bin/bash

# 配置验证脚本
# 验证nacos配置和代码修改是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} ${timestamp} - $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} ${timestamp} - $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
    esac
}

# 检查Docker Compose环境配置
check_docker_compose_env() {
    log "INFO" "检查Docker Compose环境配置..."
    
    local test_env_count=$(grep -c "SPRING_PROFILES_ACTIVE: test" park-api/docker-compose.yml 2>/dev/null || echo "0")
    local dev_env_count=$(grep -c "SPRING_PROFILES_ACTIVE: dev" park-api/docker-compose.yml 2>/dev/null || echo "0")
    
    if [ "$test_env_count" -gt 0 ] && [ "$dev_env_count" -eq 0 ]; then
        log "SUCCESS" "Docker Compose已切换到test环境 (找到 $test_env_count 个test配置)"
    elif [ "$dev_env_count" -gt 0 ]; then
        log "ERROR" "Docker Compose仍有dev环境配置 (找到 $dev_env_count 个dev配置)"
        return 1
    else
        log "WARNING" "未找到环境配置"
        return 1
    fi
}

# 检查启动类注解
check_application_annotations() {
    log "INFO" "检查微服务启动类@EnableDiscoveryClient注解..."
    
    local applications=(
        "park-api/lgjy-gateway/src/main/java/com/lgjy/gateway/LgjyGatewayApplication.java"
        "park-api/lgjy-auth/src/main/java/com/lgjy/auth/LgjyAuthApplication.java"
        "park-api/lgjy-modules/lgjy-system/src/main/java/com/lgjy/system/LgjySystemApplication.java"
        "park-api/lgjy-modules/lgjy-gate/src/main/java/com/lgjy/gate/LgjyGateApplication.java"
        "park-api/lgjy-wx-auth/src/main/java/com/lgjy/wxauth/LgjyWxAuthApplication.java"
        "park-api/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/LgjyWxApplication.java"
        "park-api/lgjy-modules/lgjy-file/src/main/java/com/lgjy/file/LgjyFileApplication.java"
    )
    
    local missing_annotations=()
    
    for app in "${applications[@]}"; do
        local service_name=$(basename $(dirname $(dirname $(dirname "$app"))))
        
        if [ -f "$app" ]; then
            if grep -q "@EnableDiscoveryClient" "$app"; then
                log "SUCCESS" "$service_name 启动类已添加@EnableDiscoveryClient注解"
            else
                log "ERROR" "$service_name 启动类缺少@EnableDiscoveryClient注解"
                missing_annotations+=("$service_name")
            fi
        else
            log "WARNING" "$service_name 启动类文件不存在: $app"
        fi
    done
    
    if [ ${#missing_annotations[@]} -eq 0 ]; then
        return 0
    else
        log "ERROR" "以下服务缺少@EnableDiscoveryClient注解: ${missing_annotations[*]}"
        return 1
    fi
}

# 检查Nacos配置
check_nacos_config() {
    log "INFO" "检查Nacos配置中的management配置..."
    
    # 检查Nacos是否可访问
    if ! curl -s "http://localhost:8848/nacos/v1/console/health/readiness" > /dev/null 2>&1; then
        log "ERROR" "Nacos服务不可访问，请确保Nacos已启动"
        return 1
    fi
    
    local test_configs=(
        "common-test.yml"
        "park-gateway-test.yml"
        "park-auth-test.yml"
        "park-system-test.yml"
        "park-file-test.yml"
        "park-wx-test.yml"
        "park-wxauth-test.yml"
        "park-gate-test.yml"
    )
    
    local missing_management=()
    
    for config in "${test_configs[@]}"; do
        local config_content=$(curl -s "http://localhost:8848/nacos/v1/cs/configs?dataId=$config&group=DEFAULT_GROUP" 2>/dev/null || echo "")
        
        if [ -n "$config_content" ]; then
            if echo "$config_content" | grep -q "management:"; then
                log "SUCCESS" "$config 包含management配置"
            else
                log "ERROR" "$config 缺少management配置"
                missing_management+=("$config")
            fi
        else
            log "WARNING" "$config 配置文件不存在或无法访问"
        fi
    done
    
    if [ ${#missing_management[@]} -eq 0 ]; then
        return 0
    else
        log "ERROR" "以下配置文件缺少management配置: ${missing_management[*]}"
        return 1
    fi
}

# 主验证函数
main() {
    log "INFO" "开始验证配置修改..."
    
    local validation_failed=false
    
    # 检查Docker Compose环境配置
    if ! check_docker_compose_env; then
        validation_failed=true
    fi
    
    echo ""
    
    # 检查启动类注解
    if ! check_application_annotations; then
        validation_failed=true
    fi
    
    echo ""
    
    # 检查Nacos配置
    if ! check_nacos_config; then
        validation_failed=true
    fi
    
    echo ""
    
    # 生成报告
    log "INFO" "========== 配置验证结果 =========="
    
    if [ "$validation_failed" = false ]; then
        log "SUCCESS" "所有配置验证通过！可以开始测试健康检查功能。"
        
        log "INFO" "建议执行以下命令进行完整测试："
        echo "  1. 重新构建并启动服务: docker-compose down && docker-compose up -d"
        echo "  2. 等待服务启动: sleep 60"
        echo "  3. 运行健康检查验证: ./scripts/test/health-check-validation.sh"
        
        exit 0
    else
        log "ERROR" "配置验证失败，请修复上述问题后重试。"
        exit 1
    fi
}

# 执行主函数
main "$@"
