#!/bin/bash

# 停车管理系统完整构建脚本
# 作者: DevOps Team
# 版本: 1.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 解析命令行参数
BUILD_ENV="test"
SKIP_TESTS="false"
CLEAN_BUILD="false"
BUILD_BACKEND="true"
BUILD_FRONTEND="true"
BUILD_DOCKER="true"

while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            BUILD_ENV="${1#*=}"
            shift
            ;;
        --skip-tests)
            SKIP_TESTS="true"
            shift
            ;;
        --clean)
            CLEAN_BUILD="true"
            shift
            ;;
        --backend-only)
            BUILD_FRONTEND="false"
            shift
            ;;
        --frontend-only)
            BUILD_BACKEND="false"
            shift
            ;;
        --no-docker)
            BUILD_DOCKER="false"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV            构建环境 (test|prod) 默认: test"
            echo "  --skip-tests         跳过测试"
            echo "  --clean              清理构建"
            echo "  --backend-only       只构建后端"
            echo "  --frontend-only      只构建前端"
            echo "  --no-docker          不构建Docker镜像"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 预构建检查
pre_build_check() {
    log "INFO" "执行预构建检查..."
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log "ERROR" "项目根目录不存在: $PROJECT_ROOT"
        return 1
    fi
    
    if [ "$BUILD_BACKEND" = "true" ] && [ ! -d "$BACKEND_DIR" ]; then
        log "ERROR" "后端目录不存在: $BACKEND_DIR"
        return 1
    fi
    
    if [ "$BUILD_FRONTEND" = "true" ] && [ ! -d "$FRONTEND_DIR" ]; then
        log "ERROR" "前端目录不存在: $FRONTEND_DIR"
        return 1
    fi
    
    # 检查Git状态
    if [ -d "$PROJECT_ROOT/.git" ]; then
        cd "$PROJECT_ROOT"
        
        # 检查是否有未提交的更改
        if ! git diff-index --quiet HEAD --; then
            log "WARN" "检测到未提交的更改"
            git status --porcelain
        fi
        
        # 检查当前分支
        if [ "$GIT_BRANCH_CURRENT" != "$GIT_BRANCH" ]; then
            log "WARN" "当前分支 ($GIT_BRANCH_CURRENT) 与配置分支 ($GIT_BRANCH) 不匹配"
        fi
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    local required_space=2097152  # 2GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        log "WARN" "磁盘空间可能不足，建议至少保留2GB空间"
    fi
    
    log "INFO" "预构建检查完成"
}

# 清理构建环境
clean_build_environment() {
    if [ "$CLEAN_BUILD" = "true" ]; then
        log "INFO" "清理构建环境..."
        
        # 清理Maven缓存
        if [ "$BUILD_BACKEND" = "true" ]; then
            cd "$BACKEND_DIR"
            mvn clean > /dev/null 2>&1 || true
        fi
        
        # 清理前端构建产物
        if [ "$BUILD_FRONTEND" = "true" ]; then
            [ -d "$FRONTEND_DIR/dist" ] && rm -rf "$FRONTEND_DIR/dist"
            [ -d "$FRONTEND_DIR/node_modules" ] && rm -rf "$FRONTEND_DIR/node_modules"
            
            if [ -d "$UNIAPP_DIR" ]; then
                [ -d "$UNIAPP_DIR/unpackage" ] && rm -rf "$UNIAPP_DIR/unpackage"
                [ -d "$UNIAPP_DIR/node_modules" ] && rm -rf "$UNIAPP_DIR/node_modules"
            fi
        fi
        
        # 清理Docker镜像（可选）
        if [ "$BUILD_DOCKER" = "true" ]; then
            log "INFO" "清理旧的Docker镜像..."
            docker image prune -f > /dev/null 2>&1 || true
        fi
        
        log "INFO" "构建环境清理完成"
    fi
}

# 构建后端
build_backend() {
    if [ "$BUILD_BACKEND" = "true" ]; then
        log "INFO" "开始构建后端..."
        
        local backend_args=""
        
        if [ "$SKIP_TESTS" = "true" ]; then
            backend_args="$backend_args --skip-tests"
        fi
        
        if [ "$CLEAN_BUILD" = "true" ]; then
            backend_args="$backend_args --clean"
        fi
        
        if [ "$BUILD_DOCKER" = "false" ]; then
            backend_args="$backend_args --no-docker"
        fi
        
        if ! "$SCRIPTS_ROOT/build/build-backend.sh" $backend_args; then
            log "ERROR" "后端构建失败"
            return 1
        fi
        
        log "INFO" "后端构建完成"
    else
        log "INFO" "跳过后端构建"
    fi
}

# 构建前端
build_frontend() {
    if [ "$BUILD_FRONTEND" = "true" ]; then
        log "INFO" "开始构建前端..."
        
        local frontend_args="--env=$BUILD_ENV"
        
        if [ "$CLEAN_BUILD" = "true" ]; then
            frontend_args="$frontend_args --clean"
        fi
        
        if [ "$BUILD_DOCKER" = "false" ]; then
            frontend_args="$frontend_args --no-docker"
        fi
        
        if ! "$SCRIPTS_ROOT/build/build-frontend.sh" $frontend_args; then
            log "ERROR" "前端构建失败"
            return 1
        fi
        
        log "INFO" "前端构建完成"
    else
        log "INFO" "跳过前端构建"
    fi
}

# 构建后处理
post_build_process() {
    log "INFO" "执行构建后处理..."
    
    # 生成构建信息文件
    local build_info_file="$DEPLOY_DIR/build-info.json"
    
    cat > "$build_info_file" << EOF
{
    "project": "$PROJECT_NAME",
    "version": "$PROJECT_VERSION",
    "build_time": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "build_env": "$BUILD_ENV",
    "git_branch": "$GIT_BRANCH_CURRENT",
    "git_commit": "$GIT_COMMIT",
    "git_commit_short": "$GIT_COMMIT_SHORT",
    "build_backend": $BUILD_BACKEND,
    "build_frontend": $BUILD_FRONTEND,
    "build_docker": $BUILD_DOCKER,
    "skip_tests": $SKIP_TESTS
}
EOF
    
    log "INFO" "构建信息已保存到: $build_info_file"
    
    # 显示构建统计
    log "INFO" "构建统计:"
    
    if [ "$BUILD_BACKEND" = "true" ] && [ "$BUILD_DOCKER" = "true" ]; then
        log "INFO" "后端Docker镜像:"
        docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep "park/"
    fi
    
    if [ "$BUILD_FRONTEND" = "true" ]; then
        if [ -d "$FRONTEND_DIR/dist" ]; then
            local frontend_size=$(du -sh "$FRONTEND_DIR/dist" | cut -f1)
            log "INFO" "前端构建产物大小: $frontend_size"
        fi
    fi
    
    # 计算总构建时间
    local build_end_time=$(date +%s)
    local build_duration=$((build_end_time - BUILD_START_TIME))
    log "INFO" "总构建时间: ${build_duration}秒"
}

# 主构建函数
main() {
    BUILD_START_TIME=$(date +%s)
    
    log "INFO" "开始完整构建..."
    log "INFO" "构建环境: $BUILD_ENV"
    log "INFO" "项目版本: $PROJECT_VERSION"
    log "INFO" "Git分支: $GIT_BRANCH_CURRENT"
    log "INFO" "Git提交: $GIT_COMMIT_SHORT"
    log "INFO" "构建配置: 后端=$BUILD_BACKEND, 前端=$BUILD_FRONTEND, Docker=$BUILD_DOCKER"
    
    # 预构建检查
    if ! pre_build_check; then
        log "ERROR" "预构建检查失败"
        return 1
    fi
    
    # 清理构建环境
    clean_build_environment
    
    # 构建后端
    if ! build_backend; then
        log "ERROR" "后端构建失败"
        return 1
    fi
    
    # 构建前端
    if ! build_frontend; then
        log "ERROR" "前端构建失败"
        return 1
    fi
    
    # 构建后处理
    post_build_process
    
    log "INFO" "完整构建成功完成！"
    return 0
}

# 执行主函数
main "$@"
