# 场库层级管理功能说明

## 功能概述

在场库管理页面中实现了场库层级管理功能，允许用户直接在场库列表中查看、新增、编辑和删除该场库下的子场库信息。

## 功能特性

### 1. 子场库查看
- 在场库列表的操作列中新增"子场库"按钮
- 点击后弹出子场库管理弹窗，显示该场库下的所有子场库
- 支持查看子场库的详细信息：ID、名称、类型、楼层、车位数等

### 2. 子场库新增
- 在子场库管理弹窗中点击"新增子场库"按钮
- 弹出子场库编辑表单，包含完整的子场库信息字段
- 自动关联当前父场库ID
- 支持设置楼层信息、车位配置、设施信息等

### 3. 子场库编辑
- 在子场库列表中点击"修改"按钮
- 弹出编辑表单，预填充当前子场库信息
- 支持修改所有子场库属性

### 4. 子场库删除
- 在子场库列表中点击"删除"按钮
- 确认删除后移除子场库记录
- 自动更新场库的统计信息

## 界面设计

### 场库列表页面
- 在操作列新增"子场库"按钮，位于"修改"按钮前
- 按钮使用蓝色主题色，图标为"View"

### 子场库管理弹窗
- 宽度：1200px，适应大屏显示
- 标题：显示场库名称 + "子场库管理"
- 包含新增按钮和子场库列表表格

### 子场库编辑弹窗
- 宽度：1000px
- 表单采用响应式布局，合理分组显示字段
- 包含完整的子场库信息字段

## 字段说明

### 基本信息
- **子场库名称**：必填，子场库的显示名称
- **楼层信息**：选填，如B1、B2、1F、2F等
- **所属运营商**：必填，继承父场库的运营商信息

### 车位配置
- **总车位数**：总的停车位数量
- **剩余车位**：当前可用的停车位数量
- **预留车位**：预留给特定用途的车位数量
- **充电车位**：配备充电设施的车位数量
- **VIP车位**：VIP专用车位数量
- **无障碍车位**：无障碍专用车位数量

### 设施信息
- **入口限高**：车辆入口的高度限制（米）
- **是否有顶棚**：是否有遮挡设施
- **是否有监控**：是否配备监控设备
- **是否有照明**：是否有照明设施

### 运营信息
- **开放时间**：停车场的营业时间
- **出入口控制**：出入口管理说明
- **状态**：正常使用(1) / 暂停使用(2) / 维护中(3)
- **备注**：其他说明信息

## 权限控制

功能使用了场库管理权限：
- `platform:warehouse:add` - 新增子场库权限
- `platform:warehouse:edit` - 编辑子场库权限
- `platform:warehouse:remove` - 删除子场库权限

## 数据联动

### 自动更新统计
- 新增/删除子场库后，自动刷新场库列表
- 确保场库的子场库数量统计准确

### 关联关系
- 子场库自动关联当前父场库的ID
- 删除场库时会检查是否有关联的子场库

## 技术实现

### 前端组件
- 使用 Vue 3 + Element Plus
- 响应式表单设计
- 弹窗嵌套管理

### API接口
- 复用现有的场库管理接口
- 支持按父场库ID查询子场库列表

### 数据验证
- 前端表单验证
- 后端业务逻辑验证

## 使用流程

1. 进入场库管理页面
2. 找到需要管理子场库的场库记录
3. 点击该记录操作列中的"子场库"按钮
4. 在弹出的子场库管理窗口中：
   - 查看现有子场库列表
   - 点击"新增子场库"添加新的子场库
   - 点击子场库记录的"修改"按钮编辑信息
   - 点击"删除"按钮移除子场库
5. 完成操作后关闭弹窗

## 注意事项

1. 子场库名称在同一父场库下必须唯一
2. 删除子场库前请确认没有相关的业务数据
3. 修改子场库信息可能影响相关的收费和统计功能
4. 建议在业务低峰期进行子场库的新增和删除操作
