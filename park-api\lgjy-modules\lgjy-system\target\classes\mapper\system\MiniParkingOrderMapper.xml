<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniParkingOrderMapper">

    <resultMap type="MiniParkingOrder" id="MiniParkingOrderResult">
        <result property="id" column="id" />
        <result property="warehouseId" column="warehouse_id" />
        <result property="warehouseName" column="warehouse_name" />
        <result property="parkingManageId" column="parking_manage_id" />
        <result property="userId" column="user_id" />
        <result property="plateNo" column="plate_no" />
        <result property="beginParkingTime" column="begin_parking_time" />
        <result property="endParkingTime" column="end_parking_time" />
        <result property="parkingDuration" column="parking_duration" />
        <result property="paymentAmount" column="payment_amount" />
        <result property="discountAmount" column="discount_amount" />
        <result property="actualPayment" column="actual_payment" />
        <result property="payType" column="pay_type" />
        <result property="parkingReservationId" column="parking_reservation_id" />
        <result property="invoiceId" column="invoice_id" />
        <result property="tradeId" column="trade_id" />
        <result property="payStatus" column="pay_status" />
        <result property="paymentTime" column="payment_time" />
        <result property="openId" column="open_id" />
        <result property="carType" column="car_type" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectMiniParkingOrderVo">
        select o.id, o.warehouse_id, w.warehouse_name, o.parking_manage_id, o.user_id, o.plate_no,
               o.begin_parking_time, o.end_parking_time, o.parking_duration, o.payment_amount,
               o.discount_amount, o.actual_payment, o.pay_type, o.parking_reservation_id, o.invoice_id,
               o.trade_id, o.pay_status, o.payment_time, o.open_id, o.car_type, o.delete_flag,
               coalesce(cu.nick_name, cu.user_name, o.create_by) as create_by, o.create_time,
               coalesce(uu.nick_name, uu.user_name, o.update_by) as update_by, o.update_time
        from mini_parking_order o
        left join mini_warehouse w on o.warehouse_id = w.id and w.delete_flag = 0
        left join sys_user cu on o.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on o.update_by = uu.user_id and uu.delete_flag = '0'
    </sql>

    <select id="selectMiniParkingOrderList" parameterType="MiniParkingOrder" resultMap="MiniParkingOrderResult">
        <include refid="selectMiniParkingOrderVo"/>
        <where>
            o.delete_flag = 0
            <if test="warehouseId != null">
                and o.warehouse_id = #{warehouseId}
            </if>
            <if test="plateNo != null and plateNo != ''">
                and o.plate_no like concat('%', #{plateNo}, '%')
            </if>
            <if test="payStatus != null">
                and o.pay_status = #{payStatus}
            </if>
            <if test="payType != null">
                and o.pay_type = #{payType}
            </if>
            <if test="carType != null and carType != ''">
                and o.car_type = #{carType}
            </if>
            <if test="beginTime != null">
                and o.begin_parking_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                and o.begin_parking_time &lt;= #{endTime}
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''">
                and date_format(o.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''">
                and date_format(o.create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by o.create_time desc
    </select>

    <select id="selectMiniParkingOrderById" parameterType="Long" resultMap="MiniParkingOrderResult">
        <include refid="selectMiniParkingOrderVo"/>
        where o.id = #{id} and o.delete_flag = 0
    </select>

    <select id="selectMiniParkingOrderByPlateNo" parameterType="String" resultMap="MiniParkingOrderResult">
        <include refid="selectMiniParkingOrderVo"/>
        where o.plate_no = #{plateNo} and o.delete_flag = 0
        order by o.create_time desc
    </select>

    <select id="selectMiniParkingOrderByWarehouseId" parameterType="Long" resultMap="MiniParkingOrderResult">
        <include refid="selectMiniParkingOrderVo"/>
        where o.warehouse_id = #{warehouseId} and o.delete_flag = 0
        order by o.create_time desc
    </select>

    <select id="selectMiniParkingOrderByPayStatus" parameterType="Integer" resultMap="MiniParkingOrderResult">
        <include refid="selectMiniParkingOrderVo"/>
        where o.pay_status = #{payStatus} and o.delete_flag = 0
        order by o.create_time desc
    </select>

    <select id="countMiniParkingOrder" parameterType="MiniParkingOrder" resultType="int">
        select count(*) from mini_parking_order o
        <where>
            o.delete_flag = 0
            <if test="warehouseId != null">
                and o.warehouse_id = #{warehouseId}
            </if>
            <if test="payStatus != null">
                and o.pay_status = #{payStatus}
            </if>
            <if test="beginTime != null">
                and o.begin_parking_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                and o.begin_parking_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="sumMiniParkingOrderAmount" parameterType="MiniParkingOrder" resultType="java.math.BigDecimal">
        select IFNULL(sum(o.actual_payment), 0) from mini_parking_order o
        <where>
            o.delete_flag = 0 and o.pay_status = 1
            <if test="warehouseId != null">
                and o.warehouse_id = #{warehouseId}
            </if>
            <if test="beginTime != null">
                and o.begin_parking_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                and o.begin_parking_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <insert id="insertMiniParkingOrder" parameterType="MiniParkingOrder">
        insert into mini_parking_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="parkingManageId != null">parking_manage_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="plateNo != null and plateNo != ''">plate_no,</if>
            <if test="beginParkingTime != null">begin_parking_time,</if>
            <if test="endParkingTime != null">end_parking_time,</if>
            <if test="parkingDuration != null">parking_duration,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="actualPayment != null">actual_payment,</if>
            <if test="payType != null">pay_type,</if>
            <if test="parkingReservationId != null">parking_reservation_id,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="tradeId != null and tradeId != ''">trade_id,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="carType != null and carType != ''">car_type,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="parkingManageId != null">#{parkingManageId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="plateNo != null and plateNo != ''">#{plateNo},</if>
            <if test="beginParkingTime != null">#{beginParkingTime},</if>
            <if test="endParkingTime != null">#{endParkingTime},</if>
            <if test="parkingDuration != null">#{parkingDuration},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="discountAmount != null">#{discountAmount},</if>
            <if test="actualPayment != null">#{actualPayment},</if>
            <if test="payType != null">#{payType},</if>
            <if test="parkingReservationId != null">#{parkingReservationId},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="tradeId != null and tradeId != ''">#{tradeId},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="carType != null and carType != ''">#{carType},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateMiniParkingOrder" parameterType="MiniParkingOrder">
        update mini_parking_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="parkingManageId != null">parking_manage_id = #{parkingManageId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="plateNo != null and plateNo != ''">plate_no = #{plateNo},</if>
            <if test="beginParkingTime != null">begin_parking_time = #{beginParkingTime},</if>
            <if test="endParkingTime != null">end_parking_time = #{endParkingTime},</if>
            <if test="parkingDuration != null">parking_duration = #{parkingDuration},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="actualPayment != null">actual_payment = #{actualPayment},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="parkingReservationId != null">parking_reservation_id = #{parkingReservationId},</if>
            <if test="invoiceId != null">invoice_id = #{invoiceId},</if>
            <if test="tradeId != null and tradeId != ''">trade_id = #{tradeId},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="openId != null and openId != ''">open_id = #{openId},</if>
            <if test="carType != null and carType != ''">car_type = #{carType},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniParkingOrderById" parameterType="Long">
        update mini_parking_order set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniParkingOrderByIds" parameterType="String">
        update mini_parking_order set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取数据库中现有的车辆类型选项 -->
    <select id="selectCarTypeOptions" resultType="String">
        select distinct car_type from mini_parking_order
        where car_type is not null and car_type != '' and delete_flag = 0
        order by car_type
    </select>

    <!-- 根据车牌号和场库ID查询出入场记录 -->
    <select id="selectGateParkingInfo" resultType="java.util.Map">
        select
            id,
            inChannelName,
            inPic,
            outChannelName,
            outPic,
            inTime,
            outTime,
            status,
            lastUpdate
        from gate_parking_info
        where plateNum = #{plateNo}
        and parkingId = #{warehouseId}
        order by lastUpdate desc
        limit 10
    </select>

    <!-- 根据场库ID、车牌号和停车开始时间查询出入场记录 -->
    <select id="selectGateParkingInfoByOrderInfo" resultType="java.util.Map">
        select
            id,
            inChannelName,
            inPic,
            outChannelName,
            outPic,
            inTime,
            outTime,
            status,
            lastUpdate
        from gate_parking_info
        where parkingId = #{warehouseId}
        and plateNum = #{plateNo}
        and CAST(inTime AS UNSIGNED) = UNIX_TIMESTAMP(#{beginParkingTime})
        order by lastUpdate desc
        limit 1
    </select>

    <!-- 根据场库ID、车牌号和时间戳查询出入场记录 -->
    <select id="selectGateParkingInfoByTimestamp" resultType="java.util.Map">
        select
            id,
            inChannelName,
            inPic,
            outChannelName,
            outPic,
            inTime,
            outTime,
            status,
            lastUpdate
        from gate_parking_info
        where parkingId = #{warehouseId}
        and plateNum = #{plateNo}
        and inTime = #{timestamp}
        order by lastUpdate desc
        limit 1
    </select>

    <select id="selectMiniParkingOrderByTradeId" parameterType="String" resultMap="MiniParkingOrderResult">
        <include refid="selectMiniParkingOrderVo"/>
        where trade_id = #{tradeId}
    </select>
</mapper>
