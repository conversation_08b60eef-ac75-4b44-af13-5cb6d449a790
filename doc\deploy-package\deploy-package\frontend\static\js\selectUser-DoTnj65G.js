import{O as D,d as O,r as f,C as Q,e as s,j as A,o as E,h as l,f as e,l as a,m as v,n as _,i as C,t as F,P as G}from"./index-D_FV2sri.js";import{u as H,d as J}from"./role-lQ-ziB3_.js";import{C as N}from"./index-C9zsUI-h.js";const M={class:"dialog-footer"},W=D({name:"SelectUser"}),ee=Object.assign(W,{components:{CustomPagination:N}},{props:{roleId:{type:[Number,String]}},emits:["ok"],setup(S,{expose:I,emit:V}){const k=S,{proxy:p}=O(),{sys_normal_disable:x}=p.useDict("sys_normal_disable"),w=f([]),i=f(!1),h=f(0),y=f([]),n=Q({pageNum:1,pageSize:10,roleId:void 0,userName:void 0,phonenumber:void 0});function U(){n.roleId=k.roleId,g(),i.value=!0}function R(r){p.$refs.refTable.toggleRowSelection(r)}function z(r){y.value=r.map(o=>o.userId)}function g(){H(n).then(r=>{w.value=r.rows,h.value=r.total})}function d(){n.pageNum=1,g()}function T(){p.resetForm("queryRef"),d()}const P=V;function $(){const r=n.roleId,o=y.value.join(",");if(o==""){p.$modal.msgError("请选择要分配的用户");return}J({roleId:r,userIds:o}).then(m=>{p.$modal.msgSuccess(m.msg),i.value=!1,P("ok")})}return I({show:U}),(r,o)=>{const m=s("el-input"),b=s("el-form-item"),c=s("el-button"),j=s("el-form"),u=s("el-table-column"),q=s("dict-tag"),B=s("el-table"),K=s("el-row"),L=s("el-dialog");return E(),A(L,{title:"选择用户",modelValue:a(i),"onUpdate:modelValue":o[5]||(o[5]=t=>G(i)?i.value=t:null),width:"800px",top:"5vh","append-to-body":""},{footer:l(()=>[C("div",M,[e(c,{type:"primary",onClick:$},{default:l(()=>[_("确 定")]),_:1}),e(c,{onClick:o[4]||(o[4]=t=>i.value=!1)},{default:l(()=>[_("取 消")]),_:1})])]),default:l(()=>[e(j,{model:a(n),ref:"queryRef",inline:!0},{default:l(()=>[e(b,{label:"用户名称",prop:"userName"},{default:l(()=>[e(m,{modelValue:a(n).userName,"onUpdate:modelValue":o[0]||(o[0]=t=>a(n).userName=t),placeholder:"请输入用户名称",clearable:"",style:{width:"180px"},onKeyup:v(d,["enter"])},null,8,["modelValue"])]),_:1}),e(b,{label:"手机号码",prop:"phonenumber"},{default:l(()=>[e(m,{modelValue:a(n).phonenumber,"onUpdate:modelValue":o[1]||(o[1]=t=>a(n).phonenumber=t),placeholder:"请输入手机号码",clearable:"",style:{width:"180px"},onKeyup:v(d,["enter"])},null,8,["modelValue"])]),_:1}),e(b,null,{default:l(()=>[e(c,{type:"primary",icon:"Search",onClick:d},{default:l(()=>[_("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:T},{default:l(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(K,null,{default:l(()=>[e(B,{onRowClick:R,ref:"refTable",data:a(w),onSelectionChange:z,height:"260px"},{default:l(()=>[e(u,{type:"selection",width:"55"}),e(u,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),e(u,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),e(u,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),e(u,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),e(u,{label:"状态",align:"center",prop:"status"},{default:l(t=>[e(q,{options:a(x),value:t.row.status},null,8,["options","value"])]),_:1}),e(u,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(t=>[C("span",null,F(r.parseTime(t.row.createTime)),1)]),_:1})]),_:1},8,["data"]),e(N,{total:a(h),"current-page":a(n).pageNum,"onUpdate:currentPage":o[2]||(o[2]=t=>a(n).pageNum=t),"page-size":a(n).pageSize,"onUpdate:pageSize":o[3]||(o[3]=t=>a(n).pageSize=t),onPagination:g},null,8,["total","current-page","page-size"])]),_:1})]),_:1},8,["modelValue"])}}});export{ee as default};
