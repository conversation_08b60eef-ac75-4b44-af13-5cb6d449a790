import{_ as ze,O as Ae,d as Oe,r as b,C as Ee,T as qe,e as m,Q as de,c as Y,o as c,R as C,f as e,S as He,l as o,h as l,m as se,n as d,j as v,P as B,t as f,i as V,aa as Re,k as pe,L as me,M as ce,D as fe,A as Fe,B as Be}from"./index-D_FV2sri.js";import{l as je,g as _e,a as ge,d as Le,u as Qe,b as Ke}from"./member-Ddkf0SYw.js";import{g as he}from"./package-DAIZfc43.js";import{C as be}from"./index-C9zsUI-h.js";const O=W=>(Fe("data-v-9b3f9323"),W=W(),Be(),W),Ge={class:"app-container"},Je=O(()=>V("i",{class:"el-icon-phone",style:{"margin-right":"4px"}},null,-1)),Xe=O(()=>V("i",{class:"el-icon-box",style:{"margin-right":"4px"}},null,-1)),Ze={key:1,style:{color:"#909399"}},el={style:{display:"flex","align-items":"center","justify-content":"center"}},ll=O(()=>V("i",{class:"el-icon-time",style:{color:"#67C23A","margin-right":"4px"}},null,-1)),al={style:{color:"#606266"}},tl={style:{display:"flex","align-items":"center","justify-content":"center"}},ol=O(()=>V("i",{class:"el-icon-time",style:{color:"#F56C6C","margin-right":"4px"}},null,-1)),nl={class:"dialog-footer"},rl={key:1,style:{color:"#909399"}},il={class:"dialog-footer"},ul=Ae({name:"VipMember"}),dl=Object.assign(ul,{components:{CustomPagination:be}},{setup(W){const{proxy:w}=Oe(),{vip_member_type:E}=w.useDict("vip_member_type"),j=b([]),N=b(!1),D=b(!1),q=b(!0),$=b(!0),z=b([]),L=b(!0),Q=b(!0),K=b(0),H=b(""),_=b({}),G=b([]),U=b([]),T=b([]),ve=Ee({form:{},queryParams:{pageNum:1,pageSize:10,phoneNumber:null,plateNo:null,warehouseId:null},rules:{plateNo:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],packageId:[{required:!0,message:"请选择套餐",trigger:"change"}],beginVipTime:[{required:!0,message:"VIP开始时间不能为空",trigger:"change"}],endVipTime:[{required:!0,message:"VIP结束时间不能为空",trigger:"change"}]}}),{queryParams:g,form:r,rules:ye}=qe(ve);function J(n){return n?n.length===8?"success":"primary":"info"}function X(n){return n?n.length===8?"#d4edda":"#cce7ff":"#909399"}function Ve(n){if(!n)return{color:"#909399"};const t=new Date,s=new Date(n),u=Math.ceil((s-t)/(1e3*60*60*24));return u<0?{color:"#F56C6C",fontWeight:"bold"}:u<=7?{color:"#E6A23C",fontWeight:"bold"}:u<=30?{color:"#E6A23C"}:{color:"#67C23A"}}function S(){q.value=!0,je(g.value).then(n=>{j.value=n.rows,K.value=n.total,q.value=!1})}function Z(){_e().then(n=>{G.value=n.data,U.value=ee(n.data)})}function ee(n){if(!n||n.length===0)return[];const t=n.filter(u=>u.parentId==="0"),s=n.filter(u=>u.parentId!=="0");return t.map(u=>{const h=s.filter(p=>p.parentId===u.id).map(p=>({value:p.id,label:p.warehouseName,isLeaf:!0}));return{value:u.id,label:u.warehouseName,children:h.length>0?h:void 0}})}function we(n){return n&&n.parentId==="0"}function ke(n,t){if(!n||!t)return null;n=String(n);const s=t.find(h=>String(h.id)===n);if(!s)return null;if(we(s))return[s.id];const u=t.find(h=>String(h.id)===String(s.parentId));return u?[u.id,s.id]:[s.id]}function Ie(n){r.value.packageId=null,T.value=[],n&&he(n).then(t=>{T.value=t.data||[]}).catch(()=>{T.value=[]})}function Ne(n){n&&T.value.find(t=>t.id===n)}function Te(){N.value=!1,R()}function R(){r.value={id:null,warehouseId:null,userId:null,phoneNumber:null,plateNo:null,beginVipTime:null,endVipTime:null,packageId:null,dlySystemId:null,remark:null},T.value=[],w.resetForm("memberRef")}function A(){g.value.pageNum=1,S()}function Ce(){w.resetForm("queryRef"),Object.assign(g.value,{pageNum:1,pageSize:10,phoneNumber:null,plateNo:null,warehouseId:null}),A()}function Se(n){z.value=n.map(t=>t.id),L.value=n.length!=1,Q.value=!n.length}function Pe(n){const t=n.id||z.value;ge(t).then(s=>{_.value=s.data||{},D.value=!0}).catch(s=>{console.error("获取会员详情失败:",s),w.$modal.msgError("获取会员详情失败")})}function xe(){R(),U.value.length===0&&Z(),N.value=!0,H.value="添加会员信息"}function le(n){R();const t=n.id||z.value;Promise.all([_e(),ge(t)]).then(([s,u])=>{G.value=s.data,U.value=ee(s.data),N.value=!0,H.value="修改会员信息",fe(()=>{r.value=u.data,fe(()=>{if(r.value.warehouseId){const h=ke(r.value.warehouseId,s.data);h&&h.length>0&&(r.value.warehouseId=h)}if(r.value.warehouseId){const h=Array.isArray(r.value.warehouseId)?r.value.warehouseId[r.value.warehouseId.length-1]:r.value.warehouseId;he(h).then(p=>{T.value=p.data||[]}).catch(()=>{T.value=[]})}})})})}function De(){w.$refs.memberRef.validate(n=>{if(n){if(!r.value.warehouseId){w.$modal.msgError("请选择场库");return}let t=r.value.warehouseId;Array.isArray(t)&&(t=t[t.length-1]),r.value.warehouseId=t,r.value.id!=null?Qe(r.value).then(s=>{w.$modal.msgSuccess("修改成功"),N.value=!1,S()}):Ke(r.value).then(s=>{w.$modal.msgSuccess("新增成功"),N.value=!1,S()})}})}function ae(n){const t=n.id||z.value;w.$modal.confirm('是否确认删除会员信息编号为"'+t+'"的数据项？').then(function(){return Le(t)}).then(()=>{S(),w.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Ue(){w.download("system/vip/member/export",{...g.value},`vip_member_${new Date().getTime()}.xlsx`)}return S(),Z(),(n,t)=>{const s=m("el-input"),u=m("el-form-item"),h=m("el-cascader"),p=m("el-button"),te=m("el-form"),y=m("el-col"),Me=m("right-toolbar"),P=m("el-row"),I=m("el-table-column"),M=m("el-tag"),oe=m("dict-tag"),Ye=m("el-table"),F=m("el-divider"),ne=m("el-option"),re=m("el-select"),ie=m("el-date-picker"),ue=m("el-dialog"),k=m("el-descriptions-item"),We=m("el-descriptions"),x=de("hasPermi"),$e=de("loading");return c(),Y("div",Ge,[C(e(te,{model:o(g),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(u,{label:"手机号码",prop:"phoneNumber"},{default:l(()=>[e(s,{modelValue:o(g).phoneNumber,"onUpdate:modelValue":t[0]||(t[0]=a=>o(g).phoneNumber=a),placeholder:"请输入手机号码",clearable:"",style:{width:"200px"},onKeyup:se(A,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"车牌号",prop:"plateNo"},{default:l(()=>[e(s,{modelValue:o(g).plateNo,"onUpdate:modelValue":t[1]||(t[1]=a=>o(g).plateNo=a),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:se(A,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"场库/停车场",prop:"warehouseId"},{default:l(()=>[e(h,{modelValue:o(g).warehouseId,"onUpdate:modelValue":t[2]||(t[2]=a=>o(g).warehouseId=a),options:o(U),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),e(u,null,{default:l(()=>[e(p,{type:"primary",icon:"Search",onClick:A},{default:l(()=>[d("搜索")]),_:1}),e(p,{icon:"Refresh",onClick:Ce},{default:l(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[He,o($)]]),e(P,{gutter:10,class:"mb8"},{default:l(()=>[e(y,{span:1.5},{default:l(()=>[C((c(),v(p,{type:"primary",plain:"",icon:"Plus",onClick:xe},{default:l(()=>[d("新增")]),_:1})),[[x,["vip:member:add"]]])]),_:1}),e(y,{span:1.5},{default:l(()=>[C((c(),v(p,{type:"success",plain:"",icon:"Edit",disabled:o(L),onClick:le},{default:l(()=>[d("修改")]),_:1},8,["disabled"])),[[x,["vip:member:edit"]]])]),_:1}),e(y,{span:1.5},{default:l(()=>[C((c(),v(p,{type:"danger",plain:"",icon:"Delete",disabled:o(Q),onClick:ae},{default:l(()=>[d("删除")]),_:1},8,["disabled"])),[[x,["vip:member:remove"]]])]),_:1}),e(y,{span:1.5},{default:l(()=>[C((c(),v(p,{type:"warning",plain:"",icon:"Download",onClick:Ue},{default:l(()=>[d("导出")]),_:1})),[[x,["vip:member:export"]]])]),_:1}),e(Me,{showSearch:o($),"onUpdate:showSearch":t[3]||(t[3]=a=>B($)?$.value=a:null),onQueryTable:S},null,8,["showSearch"])]),_:1}),C((c(),v(Ye,{data:o(j),onSelectionChange:Se},{default:l(()=>[e(I,{type:"selection",width:"55",align:"center"}),e(I,{label:"手机号码",align:"center",prop:"phoneNumber",width:"130"},{default:l(a=>[e(M,{type:"info",effect:"plain",size:"small"},{default:l(()=>[Je,d(" "+f(a.row.phoneNumber),1)]),_:2},1024)]),_:1}),e(I,{label:"车牌号",align:"center",prop:"plateNo",width:"120"},{default:l(a=>[e(M,{type:J(a.row.plateNo),color:X(a.row.plateNo),effect:"plain"},{default:l(()=>[d(f(a.row.plateNo),1)]),_:2},1032,["type","color"])]),_:1}),e(I,{label:"场库位置",align:"center",width:"180"},{default:l(a=>[V("span",null,f(a.row.warehouseName),1)]),_:1}),e(I,{label:"套餐名称",align:"center",prop:"packageName",width:"140"},{default:l(a=>[a.row.packageName?(c(),v(M,{key:0,type:"warning",effect:"light",size:"small"},{default:l(()=>[Xe,d(" "+f(a.row.packageName),1)]),_:2},1024)):(c(),Y("span",Ze,"未绑定套餐"))]),_:1}),e(I,{label:"会员类型",align:"center",prop:"vipType",width:"100"},{default:l(a=>[e(oe,{options:o(E),value:a.row.vipType},null,8,["options","value"])]),_:1}),e(I,{label:"VIP开始时间",align:"center",prop:"beginVipTime",width:"180"},{default:l(a=>[V("div",el,[ll,V("span",al,f(n.parseTime(a.row.beginVipTime)),1)])]),_:1}),e(I,{label:"VIP结束时间",align:"center",prop:"endVipTime",width:"180"},{default:l(a=>[V("div",tl,[ol,V("span",{style:Re(Ve(a.row.endVipTime))},f(n.parseTime(a.row.endVipTime)),5)])]),_:1}),e(I,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(a=>[V("span",null,f(n.parseTime(a.row.createTime)),1)]),_:1}),e(I,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(a=>[e(p,{link:"",type:"primary",icon:"View",onClick:i=>Pe(a.row)},{default:l(()=>[d("查看")]),_:2},1032,["onClick"]),C((c(),v(p,{link:"",type:"primary",icon:"Edit",onClick:i=>le(a.row)},{default:l(()=>[d("修改")]),_:2},1032,["onClick"])),[[x,["vip:member:edit"]]]),C((c(),v(p,{link:"",type:"primary",icon:"Delete",onClick:i=>ae(a.row)},{default:l(()=>[d("删除")]),_:2},1032,["onClick"])),[[x,["vip:member:remove"]]])]),_:1})]),_:1},8,["data"])),[[$e,o(q)]]),e(be,{total:o(K),"current-page":o(g).pageNum,"onUpdate:currentPage":t[4]||(t[4]=a=>o(g).pageNum=a),"page-size":o(g).pageSize,"onUpdate:pageSize":t[5]||(t[5]=a=>o(g).pageSize=a),onPagination:S},null,8,["total","current-page","page-size"]),e(ue,{title:o(H),modelValue:o(N),"onUpdate:modelValue":t[15]||(t[15]=a=>B(N)?N.value=a:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[V("div",nl,[e(p,{type:"primary",onClick:De},{default:l(()=>[d("确 定")]),_:1}),e(p,{onClick:Te},{default:l(()=>[d("取 消")]),_:1})])]),default:l(()=>[e(te,{ref:"memberRef",model:o(r),rules:o(ye),"label-width":"100px"},{default:l(()=>[e(P,null,{default:l(()=>[e(y,{span:24},{default:l(()=>[e(u,{label:"场库/停车场",prop:"warehouseId"},{default:l(()=>[e(h,{ref:"cascaderRef",modelValue:o(r).warehouseId,"onUpdate:modelValue":t[6]||(t[6]=a=>o(r).warehouseId=a),options:o(U),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"100%"},clearable:"",filterable:"","show-all-levels":!1,onChange:Ie},null,8,["modelValue","options"])]),_:1})]),_:1})]),_:1}),e(F,{"content-position":"left"},{default:l(()=>[d("会员基本信息")]),_:1}),o(r).id?(c(),v(P,{key:0},{default:l(()=>[e(y,{span:24},{default:l(()=>[e(u,{label:"会员ID"},{default:l(()=>[e(s,{modelValue:o(r).id,"onUpdate:modelValue":t[7]||(t[7]=a=>o(r).id=a),disabled:"",style:{"background-color":"#f5f7fa"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):pe("",!0),e(P,null,{default:l(()=>[e(y,{span:12},{default:l(()=>[e(u,{label:"手机号码",prop:"phoneNumber"},{default:l(()=>[e(s,{modelValue:o(r).phoneNumber,"onUpdate:modelValue":t[8]||(t[8]=a=>o(r).phoneNumber=a),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(u,{label:"车牌号",prop:"plateNo"},{default:l(()=>[e(s,{modelValue:o(r).plateNo,"onUpdate:modelValue":t[9]||(t[9]=a=>o(r).plateNo=a),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(F,{"content-position":"left"},{default:l(()=>[d("套餐选择")]),_:1}),e(P,null,{default:l(()=>[e(y,{span:12},{default:l(()=>[e(u,{label:"套餐选择",prop:"packageId"},{default:l(()=>[e(re,{modelValue:o(r).packageId,"onUpdate:modelValue":t[10]||(t[10]=a=>o(r).packageId=a),placeholder:"请选择套餐",clearable:"",filterable:"",onChange:Ne},{default:l(()=>[(c(!0),Y(me,null,ce(o(T),a=>(c(),v(ne,{key:a.id,label:a.packageName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(u,{label:"会员类型",prop:"vipType"},{default:l(()=>[e(re,{modelValue:o(r).vipType,"onUpdate:modelValue":t[11]||(t[11]=a=>o(r).vipType=a),placeholder:"请选择会员类型",clearable:""},{default:l(()=>[(c(!0),Y(me,null,ce(o(E),a=>(c(),v(ne,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(F,{"content-position":"left"},{default:l(()=>[d("VIP时间设置")]),_:1}),e(P,null,{default:l(()=>[e(y,{span:12},{default:l(()=>[e(u,{label:"VIP开始时间",prop:"beginVipTime"},{default:l(()=>[e(ie,{modelValue:o(r).beginVipTime,"onUpdate:modelValue":t[12]||(t[12]=a=>o(r).beginVipTime=a),type:"datetime",placeholder:"选择VIP开始时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(u,{label:"VIP结束时间",prop:"endVipTime"},{default:l(()=>[e(ie,{modelValue:o(r).endVipTime,"onUpdate:modelValue":t[13]||(t[13]=a=>o(r).endVipTime=a),type:"datetime",placeholder:"选择VIP结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(u,{label:"备注",prop:"remark"},{default:l(()=>[e(s,{modelValue:o(r).remark,"onUpdate:modelValue":t[14]||(t[14]=a=>o(r).remark=a),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(ue,{title:"会员详情",modelValue:o(D),"onUpdate:modelValue":t[17]||(t[17]=a=>B(D)?D.value=a:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[V("div",il,[e(p,{onClick:t[16]||(t[16]=a=>D.value=!1)},{default:l(()=>[d("关 闭")]),_:1})])]),default:l(()=>[e(We,{column:2,border:""},{default:l(()=>{var a;return[e(k,{label:"会员ID",span:2},{default:l(()=>[e(M,{type:"info",size:"large"},{default:l(()=>{var i;return[d(f(((i=o(_))==null?void 0:i.id)||"-"),1)]}),_:1})]),_:1}),e(k,{label:"手机号码"},{default:l(()=>{var i;return[d(f(((i=o(_))==null?void 0:i.phoneNumber)||"-"),1)]}),_:1}),e(k,{label:"车牌号"},{default:l(()=>{var i;return[(i=o(_))!=null&&i.plateNo?(c(),v(M,{key:0,type:J(o(_).plateNo),color:X(o(_).plateNo),effect:"plain"},{default:l(()=>[d(f(o(_).plateNo),1)]),_:1},8,["type","color"])):(c(),Y("span",rl,"-"))]}),_:1}),e(k,{label:"套餐名称"},{default:l(()=>{var i;return[d(f(((i=o(_))==null?void 0:i.packageName)||"-"),1)]}),_:1}),e(k,{label:"会员类型"},{default:l(()=>{var i;return[e(oe,{options:o(E),value:(i=o(_))==null?void 0:i.vipType},null,8,["options","value"])]}),_:1}),e(k,{label:"运营商",span:2},{default:l(()=>{var i;return[d(f(((i=o(_))==null?void 0:i.operatorName)||"-"),1)]}),_:1}),e(k,{label:"场库"},{default:l(()=>{var i;return[d(f(((i=o(_))==null?void 0:i.warehouseName)||"-"),1)]}),_:1}),e(k,{label:"VIP开始时间"},{default:l(()=>{var i;return[d(f(n.parseTime((i=o(_))==null?void 0:i.beginVipTime)),1)]}),_:1}),e(k,{label:"VIP结束时间",span:2},{default:l(()=>{var i;return[d(f(n.parseTime((i=o(_))==null?void 0:i.endVipTime)),1)]}),_:1}),e(k,{label:"创建时间",span:2},{default:l(()=>{var i;return[d(f(n.parseTime((i=o(_))==null?void 0:i.createTime)),1)]}),_:1}),(a=o(_))!=null&&a.remark?(c(),v(k,{key:0,label:"备注",span:2},{default:l(()=>[d(f(o(_).remark),1)]),_:1})):pe("",!0)]}),_:1})]),_:1},8,["modelValue"])])}}}),fl=ze(dl,[["__scopeId","data-v-9b3f9323"]]);export{fl as default};
