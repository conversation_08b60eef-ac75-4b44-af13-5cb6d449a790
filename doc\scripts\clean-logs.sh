#!/bin/bash

# 停车场管理系统 - 微服务日志清理脚本
# 作者: AI Assistant
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')

echo "=========================================="
echo "  停车场管理系统 - 微服务日志清理工具"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志清理函数
clean_logs() {
    echo -e "${BLUE}开始清理微服务日志...${NC}"
    
    # 启动日志清理容器
    docker-compose run --rm log-cleaner
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 日志清理完成！${NC}"
    else
        echo -e "${RED}✗ 日志清理失败！${NC}"
        return 1
    fi
}

# 显示日志统计信息
show_log_stats() {
    echo -e "${BLUE}正在统计日志文件大小...${NC}"
    
    # 获取各个服务的日志卷使用情况
    echo -e "${YELLOW}各服务日志卷使用情况:${NC}"
    docker system df -v | grep -E "(park_.*_logs|mysql_logs|redis_logs|nacos_logs|jenkins_logs)"
}

# 重启所有服务并清理日志
restart_with_clean() {
    echo -e "${YELLOW}警告: 此操作将停止所有服务，清理日志，然后重新启动！${NC}"
    read -p "确认继续? (y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}停止所有服务...${NC}"
        docker-compose down
        
        echo -e "${BLUE}清理日志...${NC}"
        clean_logs
        
        echo -e "${BLUE}启动所有服务...${NC}"
        docker-compose up -d
        
        echo -e "${GREEN}✓ 服务重启并清理日志完成！${NC}"
    else
        echo -e "${YELLOW}操作已取消${NC}"
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo -e "${BLUE}请选择操作:${NC}"
    echo "1. 清理日志文件"
    echo "2. 显示日志统计"
    echo "3. 重启服务并清理日志"
    echo "4. 退出"
    echo ""
}

# 主程序
main() {
    # 检查是否在正确的目录
    if [ ! -f "docker-compose.yml" ]; then
        echo -e "${RED}错误: 请在包含 docker-compose.yml 的目录中运行此脚本${NC}"
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "请输入选项 (1-4): " choice
        
        case $choice in
            1)
                clean_logs
                ;;
            2)
                show_log_stats
                ;;
            3)
                restart_with_clean
                ;;
            4)
                echo -e "${GREEN}再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选项，请重新选择${NC}"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 如果直接运行脚本，执行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
