<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.ErrorDataLogMapper">
    
    <resultMap type="ErrorDataLog" id="ErrorDataLogResult">
        <result property="id"    column="id"    />
        <result property="errCode"    column="errCode"    />
        <result property="errCodeName"    column="err_code_name"    />
        <result property="parkingId"    column="parkingId"    />
        <result property="parkingName"    column="parking_name"    />
        <result property="plateNum"    column="plateNum"    />
        <result property="inTime"    column="inTime"    />
        <result property="inChannelName"    column="inChannelName"    />
        <result property="outTime"    column="outTime"    />
        <result property="outChannelName"    column="outChannelName"    />
        <result property="money"    column="money"    />
        <result property="imgPath"    column="imgPath"    />
        <result property="remark"    column="remark"    />
        <result property="lastUpdate"    column="lastUpdate"    />
    </resultMap>

    <sql id="selectErrorDataLogVo">
        select edl.id, edl.errCode, 
               case edl.errCode 
                   when 0 then '未识别车牌'
                   when 1 then '入场时无出场记录'
                   when 2 then '出场时无入场记录'
                   when 3 then '收费异常'
                   else '未知错误'
               end as err_code_name,
               edl.parkingId, 
               coalesce(mw.warehouse_name, edl.parkingId) as parking_name,
               edl.plateNum, edl.inTime, edl.inChannelName, edl.outTime, edl.outChannelName, 
               edl.money, edl.imgPath, edl.remark, edl.lastUpdate
        from error_data_log edl
        left join mini_warehouse mw on edl.parkingId = mw.id and mw.delete_flag = 0
    </sql>

    <select id="selectErrorDataLogList" parameterType="ErrorDataLog" resultMap="ErrorDataLogResult">
        <include refid="selectErrorDataLogVo"/>
        <where>  
            <if test="errCode != null "> and edl.errCode = #{errCode}</if>
            <if test="parkingId != null  and parkingId != ''"> and edl.parkingId = #{parkingId}</if>
            <if test="plateNum != null  and plateNum != ''"> and edl.plateNum like concat('%', #{plateNum}, '%')</if>
            <if test="inTime != null  and inTime != ''"> and edl.inTime like concat('%', #{inTime}, '%')</if>
            <if test="outTime != null  and outTime != ''"> and edl.outTime like concat('%', #{outTime}, '%')</if>
            <if test="money != null  and money != ''"> and edl.money = #{money}</if>
            <if test="remark != null  and remark != ''"> and edl.remark like concat('%', #{remark}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by edl.lastUpdate desc
    </select>
    
    <select id="selectErrorDataLogById" parameterType="String" resultMap="ErrorDataLogResult">
        <include refid="selectErrorDataLogVo"/>
        where edl.id = #{id}
    </select>
        
    <insert id="insertErrorDataLog" parameterType="ErrorDataLog">
        insert into error_data_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="errCode != null">errCode,</if>
            <if test="parkingId != null">parkingId,</if>
            <if test="plateNum != null">plateNum,</if>
            <if test="inTime != null">inTime,</if>
            <if test="inChannelName != null">inChannelName,</if>
            <if test="outTime != null">outTime,</if>
            <if test="outChannelName != null">outChannelName,</if>
            <if test="money != null">money,</if>
            <if test="imgPath != null">imgPath,</if>
            <if test="remark != null">remark,</if>
            <if test="lastUpdate != null">lastUpdate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="errCode != null">#{errCode},</if>
            <if test="parkingId != null">#{parkingId},</if>
            <if test="plateNum != null">#{plateNum},</if>
            <if test="inTime != null">#{inTime},</if>
            <if test="inChannelName != null">#{inChannelName},</if>
            <if test="outTime != null">#{outTime},</if>
            <if test="outChannelName != null">#{outChannelName},</if>
            <if test="money != null">#{money},</if>
            <if test="imgPath != null">#{imgPath},</if>
            <if test="remark != null">#{remark},</if>
            <if test="lastUpdate != null">#{lastUpdate},</if>
         </trim>
    </insert>

    <update id="updateErrorDataLog" parameterType="ErrorDataLog">
        update error_data_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="errCode != null">errCode = #{errCode},</if>
            <if test="parkingId != null">parkingId = #{parkingId},</if>
            <if test="plateNum != null">plateNum = #{plateNum},</if>
            <if test="inTime != null">inTime = #{inTime},</if>
            <if test="inChannelName != null">inChannelName = #{inChannelName},</if>
            <if test="outTime != null">outTime = #{outTime},</if>
            <if test="outChannelName != null">outChannelName = #{outChannelName},</if>
            <if test="money != null">money = #{money},</if>
            <if test="imgPath != null">imgPath = #{imgPath},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="lastUpdate != null">lastUpdate = #{lastUpdate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErrorDataLogById" parameterType="String">
        delete from error_data_log where id = #{id}
    </delete>

    <delete id="deleteErrorDataLogByIds" parameterType="String">
        delete from error_data_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据车牌号查询错误数据日志列表 -->
    <select id="selectErrorDataLogByPlateNum" parameterType="String" resultMap="ErrorDataLogResult">
        <include refid="selectErrorDataLogVo"/>
        where edl.plateNum = #{plateNum}
        order by edl.lastUpdate desc
    </select>

    <!-- 根据场库ID查询错误数据日志列表 -->
    <select id="selectErrorDataLogByParkingId" parameterType="String" resultMap="ErrorDataLogResult">
        <include refid="selectErrorDataLogVo"/>
        where edl.parkingId = #{parkingId}
        order by edl.lastUpdate desc
    </select>

    <!-- 根据错误码查询错误数据日志列表 -->
    <select id="selectErrorDataLogByErrCode" parameterType="Integer" resultMap="ErrorDataLogResult">
        <include refid="selectErrorDataLogVo"/>
        where edl.errCode = #{errCode}
        order by edl.lastUpdate desc
    </select>

    <!-- 统计错误数据日志数量 -->
    <select id="countErrorDataLog" parameterType="ErrorDataLog" resultType="int">
        select count(*) from error_data_log edl
        <where>
            <if test="errCode != null "> and edl.errCode = #{errCode}</if>
            <if test="parkingId != null  and parkingId != ''"> and edl.parkingId = #{parkingId}</if>
            <if test="plateNum != null  and plateNum != ''"> and edl.plateNum like concat('%', #{plateNum}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>

    <!-- 统计错误数据日志按错误码分组 -->
    <select id="countErrorDataLogByErrCode" parameterType="ErrorDataLog" resultType="map">
        select
            errCode as code,
            case errCode
                when 0 then '未识别车牌'
                when 1 then '入场时无出场记录'
                when 2 then '出场时无入场记录'
                when 3 then '收费异常'
                else '未知错误'
            end as codeName,
            count(*) as count
        from error_data_log edl
        <where>
            <if test="parkingId != null  and parkingId != ''"> and edl.parkingId = #{parkingId}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        group by errCode
        order by errCode
    </select>

    <!-- 统计错误数据日志按场库分组 -->
    <select id="countErrorDataLogByParking" parameterType="ErrorDataLog" resultType="map">
        select
            edl.parkingId as parkingId,
            coalesce(mw.warehouse_name, edl.parkingId) as parkingName,
            count(*) as count
        from error_data_log edl
        left join mini_warehouse mw on edl.parkingId = mw.id and mw.delete_flag = 0
        <where>
            <if test="errCode != null "> and edl.errCode = #{errCode}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and date_format(edl.lastUpdate,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        group by edl.parkingId, mw.warehouse_name
        order by count desc
    </select>

</mapper>
