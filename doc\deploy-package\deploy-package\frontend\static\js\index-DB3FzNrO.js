import{v as N,O as he,d as ve,r as m,C as ye,T as be,x as we,e as d,Q as Z,c as x,o as r,R as w,f as e,S as Ve,l,h as a,m as Te,L as $,M as O,j as p,n as i,P as ee,i as ae,t as v,k as Pe}from"./index-D_FV2sri.js";import{C as le}from"./index-C9zsUI-h.js";function Ce(g){return N({url:"/system/merchant/package/list",method:"get",params:g})}function te(g){return N({url:"/system/merchant/package/"+g,method:"get"})}function xe(g){return N({url:"/system/merchant/package",method:"post",data:g})}function Ne(g){return N({url:"/system/merchant/package",method:"put",data:g})}function Se(g){return N({url:"/system/merchant/package/"+g,method:"delete"})}function Ue(){return N({url:"/system/merchant/package/warehouseOptions",method:"get"})}const Ie={class:"app-container"},qe={key:0,class:"view-content"},De={class:"dialog-footer"},Me=he({name:"MerchantPackage"}),Oe=Object.assign(Me,{components:{CustomPagination:le}},{setup(g){const{proxy:y}=ve(),{merchant_package_type:U}=y.useDict("merchant_package_type"),L=m([]),j=m([]),b=m(!1),B=m(!0),I=m(!0),E=m([]),Q=m(!0),z=m(!0),K=m(0),q=m(""),S=m(!1),ne=ye({form:{},queryParams:{pageNum:1,pageSize:10,packageTitle:null,warehouseId:null,packageType:null},rules:{packageTitle:[{required:!0,message:"套餐标题不能为空",trigger:"blur"}],warehouseId:[{required:!0,message:"所属场库不能为空",trigger:"change"}],packageType:[{required:!0,message:"套餐类型不能为空",trigger:"change"}],packagePrice:[{required:!0,message:"套餐价格不能为空",trigger:"blur"}],deductionPrice:[{required:!0,message:"抵扣价格不能为空",trigger:"blur"}]}}),{queryParams:s,form:o,rules:oe}=be(ne);function V(){B.value=!0,Ce(s.value).then(u=>{L.value=u.rows,K.value=u.total,B.value=!1})}function F(){s.value.pageNum=1,V()}function ue(){y.resetForm("queryRef"),F()}function re(u){E.value=u.map(n=>n.id),Q.value=u.length!=1,z.value=!u.length}function ie(){D(),S.value=!1,G(),b.value=!0,q.value="添加商户套餐"}function A(u){D(),S.value=!1;const n=u.id||E.value;te(n).then(T=>{o.value=T.data,b.value=!0,q.value="修改商户套餐"})}function de(u){D(),S.value=!0;const n=u.id;te(n).then(T=>{o.value=T.data,b.value=!0,q.value="查看商户套餐"})}function se(){y.$refs.packageRef.validate(u=>{u&&(o.value.id!=null?Ne(o.value).then(n=>{y.$modal.msgSuccess("修改成功"),b.value=!1,V()}):xe(o.value).then(n=>{y.$modal.msgSuccess("新增成功"),b.value=!1,V()}))})}function W(u){const n=u.id||E.value;y.$modal.confirm('是否确认删除商户套餐编号为"'+n+'"的数据项？').then(function(){return Se(n)}).then(()=>{V(),y.$modal.msgSuccess("删除成功")}).catch(()=>{})}function pe(){y.download("system/merchant/package/export",{...s.value},`merchant_package_${new Date().getTime()}.xlsx`)}function ce(){b.value=!1,D()}function D(){o.value={id:null,packageTitle:null,warehouseId:null,packageType:null,packagePrice:null,deductionPrice:null,remark:null},y.resetForm("packageRef")}function G(){Ue().then(u=>{j.value=u.data})}return we(()=>{V(),G()}),(u,n)=>{const T=d("el-input"),f=d("el-form-item"),M=d("el-option"),R=d("el-select"),c=d("el-button"),H=d("el-form"),_=d("el-col"),me=d("right-toolbar"),C=d("el-row"),k=d("el-table-column"),J=d("dict-tag"),ge=d("el-table"),h=d("el-descriptions-item"),fe=d("el-descriptions"),X=d("el-input-number"),_e=d("el-dialog"),P=Z("hasPermi"),ke=Z("loading");return r(),x("div",Ie,[w(e(H,{model:l(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[e(f,{label:"套餐标题",prop:"packageTitle"},{default:a(()=>[e(T,{modelValue:l(s).packageTitle,"onUpdate:modelValue":n[0]||(n[0]=t=>l(s).packageTitle=t),placeholder:"请输入套餐标题",clearable:"",style:{width:"200px"},onKeyup:Te(F,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"所属场库",prop:"warehouseId"},{default:a(()=>[e(R,{modelValue:l(s).warehouseId,"onUpdate:modelValue":n[1]||(n[1]=t=>l(s).warehouseId=t),placeholder:"请选择场库",clearable:"",style:{width:"200px"}},{default:a(()=>[(r(!0),x($,null,O(l(j),t=>(r(),p(M,{key:t.id,label:t.warehouseName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"套餐类型",prop:"packageType"},{default:a(()=>[e(R,{modelValue:l(s).packageType,"onUpdate:modelValue":n[2]||(n[2]=t=>l(s).packageType=t),placeholder:"请选择套餐类型",clearable:"",style:{width:"200px"}},{default:a(()=>[(r(!0),x($,null,O(l(U),t=>(r(),p(M,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,null,{default:a(()=>[e(c,{type:"primary",icon:"Search",onClick:F},{default:a(()=>[i("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:ue},{default:a(()=>[i("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ve,l(I)]]),e(C,{gutter:10,class:"mb8"},{default:a(()=>[e(_,{span:1.5},{default:a(()=>[w((r(),p(c,{type:"primary",plain:"",icon:"Plus",onClick:ie},{default:a(()=>[i("新增")]),_:1})),[[P,["merchant:package:add"]]])]),_:1}),e(_,{span:1.5},{default:a(()=>[w((r(),p(c,{type:"success",plain:"",icon:"Edit",disabled:l(Q),onClick:A},{default:a(()=>[i("修改")]),_:1},8,["disabled"])),[[P,["merchant:package:edit"]]])]),_:1}),e(_,{span:1.5},{default:a(()=>[w((r(),p(c,{type:"danger",plain:"",icon:"Delete",disabled:l(z),onClick:W},{default:a(()=>[i("删除")]),_:1},8,["disabled"])),[[P,["merchant:package:remove"]]])]),_:1}),e(_,{span:1.5},{default:a(()=>[w((r(),p(c,{type:"warning",plain:"",icon:"Download",onClick:pe},{default:a(()=>[i("导出")]),_:1})),[[P,["merchant:package:export"]]])]),_:1}),e(me,{showSearch:l(I),"onUpdate:showSearch":n[3]||(n[3]=t=>ee(I)?I.value=t:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),w((r(),p(ge,{data:l(L),onSelectionChange:re},{default:a(()=>[e(k,{type:"selection",width:"55",align:"center"}),e(k,{label:"套餐标题",align:"center",prop:"packageTitle","show-overflow-tooltip":!0}),e(k,{label:"所属场库",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),e(k,{label:"项目名称",align:"center",prop:"projectName","show-overflow-tooltip":!0}),e(k,{label:"运营商",align:"center",prop:"companyName","show-overflow-tooltip":!0}),e(k,{label:"套餐类型",align:"center",prop:"packageType"},{default:a(t=>[e(J,{options:l(U),value:t.row.packageType},null,8,["options","value"])]),_:1}),e(k,{label:"套餐价格",align:"center",prop:"packagePrice"}),e(k,{label:"抵扣价格",align:"center",prop:"deductionPrice"}),e(k,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(t=>[ae("span",null,v(u.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(k,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(t=>[w((r(),p(c,{link:"",type:"primary",icon:"View",onClick:Y=>de(t.row)},{default:a(()=>[i("查看")]),_:2},1032,["onClick"])),[[P,["merchant:package:query"]]]),w((r(),p(c,{link:"",type:"primary",icon:"Edit",onClick:Y=>A(t.row)},{default:a(()=>[i("修改")]),_:2},1032,["onClick"])),[[P,["merchant:package:edit"]]]),w((r(),p(c,{link:"",type:"primary",icon:"Delete",onClick:Y=>W(t.row)},{default:a(()=>[i("删除")]),_:2},1032,["onClick"])),[[P,["merchant:package:remove"]]])]),_:1})]),_:1},8,["data"])),[[ke,l(B)]]),e(le,{total:l(K),page:l(s).pageNum,"onUpdate:page":n[4]||(n[4]=t=>l(s).pageNum=t),limit:l(s).pageSize,"onUpdate:limit":n[5]||(n[5]=t=>l(s).pageSize=t),onPagination:V},null,8,["total","page","limit"]),e(_e,{title:l(q),modelValue:l(b),"onUpdate:modelValue":n[12]||(n[12]=t=>ee(b)?b.value=t:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[ae("div",De,[e(c,{onClick:ce},{default:a(()=>[i("取 消")]),_:1}),l(S)?Pe("",!0):(r(),p(c,{key:0,type:"primary",onClick:se},{default:a(()=>[i("确 定")]),_:1}))])]),default:a(()=>[l(S)?(r(),x("div",qe,[e(fe,{column:2,border:""},{default:a(()=>[e(h,{label:"套餐标题"},{default:a(()=>[i(v(l(o).packageTitle),1)]),_:1}),e(h,{label:"所属场库"},{default:a(()=>[i(v(l(o).warehouseName),1)]),_:1}),e(h,{label:"项目名称"},{default:a(()=>[i(v(l(o).projectName),1)]),_:1}),e(h,{label:"运营商"},{default:a(()=>[i(v(l(o).companyName),1)]),_:1}),e(h,{label:"套餐类型"},{default:a(()=>[e(J,{options:l(U),value:l(o).packageType},null,8,["options","value"])]),_:1}),e(h,{label:"套餐价格"},{default:a(()=>[i("¥"+v(l(o).packagePrice),1)]),_:1}),e(h,{label:"抵扣价格"},{default:a(()=>[i("¥"+v(l(o).deductionPrice),1)]),_:1}),e(h,{label:"备注",span:2},{default:a(()=>[i(v(l(o).remark||"无"),1)]),_:1}),e(h,{label:"创建时间"},{default:a(()=>[i(v(u.parseTime(l(o).createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(h,{label:"更新时间"},{default:a(()=>[i(v(u.parseTime(l(o).updateTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})]),_:1})])):(r(),p(H,{key:1,ref:"packageRef",model:l(o),rules:l(oe),"label-width":"100px"},{default:a(()=>[e(C,null,{default:a(()=>[e(_,{span:24},{default:a(()=>[e(f,{label:"套餐标题",prop:"packageTitle"},{default:a(()=>[e(T,{modelValue:l(o).packageTitle,"onUpdate:modelValue":n[6]||(n[6]=t=>l(o).packageTitle=t),placeholder:"请输入套餐标题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,null,{default:a(()=>[e(_,{span:24},{default:a(()=>[e(f,{label:"所属场库",prop:"warehouseId"},{default:a(()=>[e(R,{modelValue:l(o).warehouseId,"onUpdate:modelValue":n[7]||(n[7]=t=>l(o).warehouseId=t),placeholder:"请选择场库",style:{width:"100%"}},{default:a(()=>[(r(!0),x($,null,O(l(j),t=>(r(),p(M,{key:t.id,label:t.warehouseName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,null,{default:a(()=>[e(_,{span:24},{default:a(()=>[e(f,{label:"套餐类型",prop:"packageType"},{default:a(()=>[e(R,{modelValue:l(o).packageType,"onUpdate:modelValue":n[8]||(n[8]=t=>l(o).packageType=t),placeholder:"请选择套餐类型",style:{width:"100%"}},{default:a(()=>[(r(!0),x($,null,O(l(U),t=>(r(),p(M,{key:t.value,label:t.label,value:parseInt(t.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,null,{default:a(()=>[e(_,{span:12},{default:a(()=>[e(f,{label:"套餐价格",prop:"packagePrice"},{default:a(()=>[e(X,{modelValue:l(o).packagePrice,"onUpdate:modelValue":n[9]||(n[9]=t=>l(o).packagePrice=t),min:.01,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:a(()=>[e(f,{label:"抵扣价格",prop:"deductionPrice"},{default:a(()=>[e(X,{modelValue:l(o).deductionPrice,"onUpdate:modelValue":n[10]||(n[10]=t=>l(o).deductionPrice=t),min:.01,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,null,{default:a(()=>[e(_,{span:24},{default:a(()=>[e(f,{label:"备注",prop:"remark"},{default:a(()=>[e(T,{modelValue:l(o).remark,"onUpdate:modelValue":n[11]||(n[11]=t=>l(o).remark=t),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]))]),_:1},8,["title","modelValue"])])}}});export{Oe as default};
