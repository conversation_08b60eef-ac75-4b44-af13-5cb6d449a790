import{v as w,O as fe,d as _e,r as _,C as ge,T as ve,e as s,Q as L,c as I,o as d,R as y,f as e,S as ye,l,h as o,m as Q,L as K,M as j,j as g,n as p,P as O,i as A,t as M}from"./index-Cox2ohSR.js";import{C as G}from"./index-Cy2yB1cz.js";function be(i){return w({url:"/system/post/list",method:"get",params:i})}function he(i){return w({url:"/system/post/"+i,method:"get"})}function Ce(i){return w({url:"/system/post",method:"post",data:i})}function Ve(i){return w({url:"/system/post",method:"put",data:i})}function we(i){return w({url:"/system/post/"+i,method:"delete"})}const ke={class:"app-container"},Se={class:"dialog-footer"},Ne=fe({name:"Post"}),Ue=Object.assign(Ne,{components:{CustomPagination:G}},{setup(i){const{proxy:c}=_e(),{sys_normal_disable:x}=c.useDict("sys_normal_disable"),$=_([]),v=_(!1),P=_(!0),k=_(!0),U=_([]),q=_(!0),T=_(!0),z=_(0),D=_(""),H=ge({form:{},queryParams:{pageNum:1,pageSize:10,postCode:void 0,postName:void 0,status:void 0},rules:{postName:[{required:!0,message:"岗位名称不能为空",trigger:"blur"}],postCode:[{required:!0,message:"岗位编码不能为空",trigger:"blur"}],postSort:[{required:!0,message:"岗位顺序不能为空",trigger:"blur"}]}}),{queryParams:u,form:n,rules:J}=ve(H);function b(){P.value=!0,be(u.value).then(r=>{$.value=r.rows,z.value=r.total,P.value=!1})}function W(){v.value=!1,R()}function R(){n.value={postId:void 0,postCode:void 0,postName:void 0,postSort:0,status:"0",remark:void 0},c.resetForm("postRef")}function S(){u.value.pageNum=1,b()}function X(){c.resetForm("queryRef"),S()}function Y(r){U.value=r.map(a=>a.postId),q.value=r.length!=1,T.value=!r.length}function Z(){R(),v.value=!0,D.value="添加岗位"}function B(r){R();const a=r.postId||U.value;he(a).then(h=>{n.value=h.data,v.value=!0,D.value="修改岗位"})}function ee(){c.$refs.postRef.validate(r=>{r&&(n.value.postId!=null?Ve(n.value).then(a=>{c.$modal.msgSuccess("修改成功"),v.value=!1,b()}):Ce(n.value).then(a=>{c.$modal.msgSuccess("新增成功"),v.value=!1,b()}))})}function E(r){const a=r.postId||U.value;c.$modal.confirm('是否确认删除岗位编号为"'+a+'"的数据项？').then(function(){return we(a)}).then(()=>{b(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}function te(){c.download("system/post/export",{...u.value},`post_${new Date().getTime()}.xlsx`)}return b(),(r,a)=>{const h=s("el-input"),f=s("el-form-item"),le=s("el-option"),oe=s("el-select"),m=s("el-button"),F=s("el-form"),N=s("el-col"),ae=s("right-toolbar"),ne=s("el-row"),C=s("el-table-column"),se=s("dict-tag"),ue=s("el-table"),re=s("el-input-number"),de=s("el-radio"),pe=s("el-radio-group"),ie=s("el-dialog"),V=L("hasPermi"),me=L("loading");return d(),I("div",ke,[y(e(F,{model:l(u),ref:"queryRef",inline:!0},{default:o(()=>[e(f,{label:"岗位编码",prop:"postCode"},{default:o(()=>[e(h,{modelValue:l(u).postCode,"onUpdate:modelValue":a[0]||(a[0]=t=>l(u).postCode=t),placeholder:"请输入岗位编码",clearable:"",style:{width:"200px"},onKeyup:Q(S,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位名称",prop:"postName"},{default:o(()=>[e(h,{modelValue:l(u).postName,"onUpdate:modelValue":a[1]||(a[1]=t=>l(u).postName=t),placeholder:"请输入岗位名称",clearable:"",style:{width:"200px"},onKeyup:Q(S,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"状态",prop:"status"},{default:o(()=>[e(oe,{modelValue:l(u).status,"onUpdate:modelValue":a[2]||(a[2]=t=>l(u).status=t),placeholder:"岗位状态",clearable:"",style:{width:"200px"}},{default:o(()=>[(d(!0),I(K,null,j(l(x),t=>(d(),g(le,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,null,{default:o(()=>[e(m,{type:"primary",icon:"Search",onClick:S},{default:o(()=>[p("搜索")]),_:1}),e(m,{icon:"Refresh",onClick:X},{default:o(()=>[p("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ye,l(k)]]),e(ne,{gutter:10,class:"mb8"},{default:o(()=>[e(N,{span:1.5},{default:o(()=>[y((d(),g(m,{type:"primary",plain:"",icon:"Plus",onClick:Z},{default:o(()=>[p("新增")]),_:1})),[[V,["system:post:add"]]])]),_:1}),e(N,{span:1.5},{default:o(()=>[y((d(),g(m,{type:"success",plain:"",icon:"Edit",disabled:l(q),onClick:B},{default:o(()=>[p("修改")]),_:1},8,["disabled"])),[[V,["system:post:edit"]]])]),_:1}),e(N,{span:1.5},{default:o(()=>[y((d(),g(m,{type:"danger",plain:"",icon:"Delete",disabled:l(T),onClick:E},{default:o(()=>[p("删除")]),_:1},8,["disabled"])),[[V,["system:post:remove"]]])]),_:1}),e(N,{span:1.5},{default:o(()=>[y((d(),g(m,{type:"warning",plain:"",icon:"Download",onClick:te},{default:o(()=>[p("导出")]),_:1})),[[V,["system:post:export"]]])]),_:1}),e(ae,{showSearch:l(k),"onUpdate:showSearch":a[3]||(a[3]=t=>O(k)?k.value=t:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),y((d(),g(ue,{data:l($),onSelectionChange:Y},{default:o(()=>[e(C,{type:"selection",width:"55",align:"center"}),e(C,{label:"岗位编码",align:"center",prop:"postCode"}),e(C,{label:"岗位名称",align:"center",prop:"postName"}),e(C,{label:"岗位排序",align:"center",prop:"postSort"}),e(C,{label:"状态",align:"center",prop:"status"},{default:o(t=>[e(se,{options:l(x),value:t.row.status},null,8,["options","value"])]),_:1}),e(C,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:o(t=>[A("span",null,M(r.parseTime(t.row.createTime)),1)]),_:1}),e(C,{label:"操作",width:"180",align:"center","class-name":"small-padding fixed-width"},{default:o(t=>[y((d(),g(m,{link:"",type:"primary",icon:"Edit",onClick:ce=>B(t.row)},{default:o(()=>[p("修改")]),_:2},1032,["onClick"])),[[V,["system:post:edit"]]]),y((d(),g(m,{link:"",type:"primary",icon:"Delete",onClick:ce=>E(t.row)},{default:o(()=>[p("删除")]),_:2},1032,["onClick"])),[[V,["system:post:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,l(P)]]),e(G,{total:l(z),"current-page":l(u).pageNum,"onUpdate:currentPage":a[4]||(a[4]=t=>l(u).pageNum=t),"page-size":l(u).pageSize,"onUpdate:pageSize":a[5]||(a[5]=t=>l(u).pageSize=t),onPagination:b},null,8,["total","current-page","page-size"]),e(ie,{title:l(D),modelValue:l(v),"onUpdate:modelValue":a[11]||(a[11]=t=>O(v)?v.value=t:null),width:"500px","append-to-body":""},{footer:o(()=>[A("div",Se,[e(m,{type:"primary",onClick:ee},{default:o(()=>[p("确 定")]),_:1}),e(m,{onClick:W},{default:o(()=>[p("取 消")]),_:1})])]),default:o(()=>[e(F,{ref:"postRef",model:l(n),rules:l(J),"label-width":"80px"},{default:o(()=>[e(f,{label:"岗位名称",prop:"postName"},{default:o(()=>[e(h,{modelValue:l(n).postName,"onUpdate:modelValue":a[6]||(a[6]=t=>l(n).postName=t),placeholder:"请输入岗位名称"},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位编码",prop:"postCode"},{default:o(()=>[e(h,{modelValue:l(n).postCode,"onUpdate:modelValue":a[7]||(a[7]=t=>l(n).postCode=t),placeholder:"请输入编码名称"},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位顺序",prop:"postSort"},{default:o(()=>[e(re,{modelValue:l(n).postSort,"onUpdate:modelValue":a[8]||(a[8]=t=>l(n).postSort=t),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位状态",prop:"status"},{default:o(()=>[e(pe,{modelValue:l(n).status,"onUpdate:modelValue":a[9]||(a[9]=t=>l(n).status=t)},{default:o(()=>[(d(!0),I(K,null,j(l(x),t=>(d(),g(de,{key:t.value,value:t.value},{default:o(()=>[p(M(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"备注",prop:"remark"},{default:o(()=>[e(h,{modelValue:l(n).remark,"onUpdate:modelValue":a[10]||(a[10]=t=>l(n).remark=t),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ue as default};
