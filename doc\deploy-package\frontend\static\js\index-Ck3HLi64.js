import{v as y,_ as ce,O as pe,d as me,r as f,C as _e,T as fe,e as c,Q,c as U,o as r,R as w,f as l,S as ge,l as n,h as t,m as z,L as j,M as F,j as m,n as g,P as A,k as M,i as G,t as we}from"./index-Cox2ohSR.js";import{C as he}from"./index-Cy2yB1cz.js";function be(p){return y({url:"/system/owner/wxuser/list",method:"get",params:p})}function ve(p){return y({url:"/system/owner/wxuser/"+p,method:"delete"})}function ye(p,u){return y({url:"/system/owner/wxuser/changeStatus",method:"put",data:{id:p,status:u}})}function xe(p){return y({url:"/system/owner/wxuser/batchEnable/"+p,method:"put"})}function ke(p){return y({url:"/system/owner/wxuser/batchDisable/"+p,method:"put"})}function Ce(p){return y({url:"/system/owner/wxuser/cars/"+p,method:"get"})}const Ne={class:"app-container"},Ve={key:1},Se={class:"dialog-footer"},Te=pe({name:"WxUser"}),Ue=Object.assign(Te,{setup(p){const{proxy:u}=me(),{wx_user_status:$,wx_user_type:B}=u.useDict("wx_user_status","wx_user_type"),H=f([{label:"燃油",value:"1"},{label:"纯电",value:"2"},{label:"混动",value:"3"}]),J=f([{label:"否",value:"0"},{label:"是",value:"1"}]),E=f([]),R=f([]),x=f(!1),D=f(!0),C=f(!0),N=f([]),X=f(!0),V=f(!0),W=f(0),Y=_e({queryParams:{pageNum:1,pageSize:10,userName:null,nickName:null,phoneNumber:null,status:null,userType:null}}),{queryParams:s}=fe(Y);function h(){D.value=!0,be(s.value).then(o=>{E.value=o.rows,W.value=o.total,D.value=!1})}function I(o,a){let v=a===0?"启用":"停用";u.$modal.confirm('确认要"'+v+'""'+o.userName+'"用户吗?').then(function(){return console.log("调用状态修改API:",{id:o.id,status:a}),ye(o.id,a)}).then(i=>{console.log("状态修改响应:",i),u.$modal.msgSuccess(v+"成功"),h()}).catch(function(i){console.error("状态修改失败:",i),i.response&&i.response.data&&i.response.data.msg?u.$modal.msgError("操作失败："+i.response.data.msg):u.$modal.msgError("操作失败，请稍后重试")})}function k(){s.value.pageNum=1,h()}function Z(){u.resetForm("queryRef"),Object.assign(s.value,{pageNum:1,pageSize:10,userName:null,nickName:null,phoneNumber:null,status:null,userType:null}),k()}function ee(o){N.value=o.map(a=>a.id),X.value=o.length!=1,V.value=!o.length}function L(o){const a=o.id||N.value;u.$modal.confirm('是否确认删除小程序用户编号为"'+a+'"的数据项？').then(function(){return ve(a)}).then(()=>{h(),u.$modal.msgSuccess("删除成功")}).catch(()=>{})}function le(){u.download("owner/wxuser/export",{...s.value},`wxuser_${new Date().getTime()}.xlsx`)}function te(){const o=N.value;u.$modal.confirm("是否确认启用选中的用户？").then(function(){return xe(o)}).then(()=>{h(),u.$modal.msgSuccess("启用成功")}).catch(()=>{})}function ae(){const o=N.value;u.$modal.confirm("是否确认停用选中的用户？").then(function(){return ke(o)}).then(()=>{h(),u.$modal.msgSuccess("停用成功")}).catch(()=>{})}function ne(o){Ce(o.id).then(a=>{R.value=a.data,x.value=!0})}return h(),(o,a)=>{const v=c("el-input"),i=c("el-form-item"),q=c("el-option"),K=c("el-select"),_=c("el-button"),oe=c("el-form"),S=c("el-col"),se=c("right-toolbar"),re=c("el-row"),d=c("el-table-column"),ue=c("el-image"),T=c("dict-tag"),O=c("el-table"),ie=c("el-dialog"),b=Q("hasPermi"),de=Q("loading");return r(),U("div",Ne,[w(l(oe,{model:n(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[l(i,{label:"用户账号",prop:"userName"},{default:t(()=>[l(v,{modelValue:n(s).userName,"onUpdate:modelValue":a[0]||(a[0]=e=>n(s).userName=e),placeholder:"请输入用户账号",clearable:"",style:{width:"200px"},onKeyup:z(k,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"用户昵称",prop:"nickName"},{default:t(()=>[l(v,{modelValue:n(s).nickName,"onUpdate:modelValue":a[1]||(a[1]=e=>n(s).nickName=e),placeholder:"请输入用户昵称",clearable:"",style:{width:"200px"},onKeyup:z(k,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"手机号",prop:"phoneNumber"},{default:t(()=>[l(v,{modelValue:n(s).phoneNumber,"onUpdate:modelValue":a[2]||(a[2]=e=>n(s).phoneNumber=e),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:z(k,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"状态",prop:"status"},{default:t(()=>[l(K,{modelValue:n(s).status,"onUpdate:modelValue":a[3]||(a[3]=e=>n(s).status=e),placeholder:"用户状态",clearable:"",style:{width:"200px"}},{default:t(()=>[(r(!0),U(j,null,F(n($),e=>(r(),m(q,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"用户类型",prop:"userType"},{default:t(()=>[l(K,{modelValue:n(s).userType,"onUpdate:modelValue":a[4]||(a[4]=e=>n(s).userType=e),placeholder:"用户类型",clearable:"",style:{width:"200px"}},{default:t(()=>[(r(!0),U(j,null,F(n(B),e=>(r(),m(q,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,null,{default:t(()=>[l(_,{type:"primary",icon:"Search",onClick:k},{default:t(()=>[g("搜索")]),_:1}),l(_,{icon:"Refresh",onClick:Z},{default:t(()=>[g("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ge,n(C)]]),l(re,{gutter:10,class:"mb8"},{default:t(()=>[l(S,{span:1.5},{default:t(()=>[w((r(),m(_,{type:"danger",plain:"",icon:"Delete",disabled:n(V),onClick:L},{default:t(()=>[g("删除")]),_:1},8,["disabled"])),[[b,["owner:wxuser:remove"]]])]),_:1}),l(S,{span:1.5},{default:t(()=>[w((r(),m(_,{type:"warning",plain:"",icon:"Download",onClick:le},{default:t(()=>[g("导出")]),_:1})),[[b,["owner:wxuser:export"]]])]),_:1}),l(S,{span:1.5},{default:t(()=>[w((r(),m(_,{type:"success",plain:"",icon:"Check",disabled:n(V),onClick:te},{default:t(()=>[g("批量启用")]),_:1},8,["disabled"])),[[b,["owner:wxuser:edit"]]])]),_:1}),l(S,{span:1.5},{default:t(()=>[w((r(),m(_,{type:"info",plain:"",icon:"Close",disabled:n(V),onClick:ae},{default:t(()=>[g("批量停用")]),_:1},8,["disabled"])),[[b,["owner:wxuser:edit"]]])]),_:1}),l(se,{showSearch:n(C),"onUpdate:showSearch":a[5]||(a[5]=e=>A(C)?C.value=e:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),w((r(),m(O,{data:n(E),onSelectionChange:ee},{default:t(()=>[l(d,{type:"selection",width:"55",align:"center"}),l(d,{label:"用户账号",align:"center",prop:"userName"}),l(d,{label:"用户昵称",align:"center",prop:"nickName"}),l(d,{label:"手机号",align:"center",prop:"phoneNumber",width:"120"}),l(d,{label:"头像",align:"center",prop:"img",width:"100"},{default:t(e=>[e.row.img?(r(),m(ue,{key:0,style:{width:"40px",height:"40px","border-radius":"50%"},src:e.row.img,"preview-src-list":[e.row.img],fit:"cover"},null,8,["src","preview-src-list"])):(r(),U("span",Ve,"-"))]),_:1}),l(d,{label:"状态",align:"center",prop:"status"},{default:t(e=>[l(T,{options:n($),value:e.row.status},null,8,["options","value"])]),_:1}),l(d,{label:"用户类型",align:"center",prop:"userType"},{default:t(e=>[l(T,{options:n(B),value:e.row.userType},null,8,["options","value"])]),_:1}),l(d,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(e=>[w((r(),m(_,{link:"",type:"primary",icon:"View",onClick:P=>ne(e.row)},{default:t(()=>[g("查询车辆")]),_:2},1032,["onClick"])),[[b,["owner:wxuser:car"]]]),e.row.status===1?w((r(),m(_,{key:0,link:"",type:"success",icon:"Check",onClick:P=>I(e.row,0)},{default:t(()=>[g("启用")]),_:2},1032,["onClick"])),[[b,["owner:wxuser:edit"]]]):M("",!0),e.row.status===0?w((r(),m(_,{key:1,link:"",type:"danger",icon:"Close",onClick:P=>I(e.row,1)},{default:t(()=>[g("停用")]),_:2},1032,["onClick"])),[[b,["owner:wxuser:edit"]]]):M("",!0),w((r(),m(_,{link:"",type:"danger",icon:"Delete",onClick:P=>L(e.row)},{default:t(()=>[g("删除")]),_:2},1032,["onClick"])),[[b,["owner:wxuser:remove"]]])]),_:1})]),_:1},8,["data"])),[[de,n(D)]]),l(he,{total:n(W),"current-page":n(s).pageNum,"onUpdate:currentPage":a[6]||(a[6]=e=>n(s).pageNum=e),"page-size":n(s).pageSize,"onUpdate:pageSize":a[7]||(a[7]=e=>n(s).pageSize=e),onPagination:h},null,8,["total","current-page","page-size"]),l(ie,{title:"用户车辆信息",modelValue:n(x),"onUpdate:modelValue":a[9]||(a[9]=e=>A(x)?x.value=e:null),width:"800px","append-to-body":""},{footer:t(()=>[G("div",Se,[l(_,{onClick:a[8]||(a[8]=e=>x.value=!1)},{default:t(()=>[g("关 闭")]),_:1})])]),default:t(()=>[l(O,{data:n(R),style:{width:"100%"}},{default:t(()=>[l(d,{label:"车牌号",align:"center",prop:"plateNo"}),l(d,{label:"品牌",align:"center",prop:"carBrand"}),l(d,{label:"车型",align:"center",prop:"carType"}),l(d,{label:"能源类型",align:"center",prop:"energyType"},{default:t(e=>[l(T,{options:n(H),value:e.row.energyType},null,8,["options","value"])]),_:1}),l(d,{label:"是否默认",align:"center",prop:"isDefault"},{default:t(e=>[l(T,{options:n(J),value:e.row.isDefault},null,8,["options","value"])]),_:1}),l(d,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(e=>[G("span",null,we(o.parseTime(e.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}}),Pe=ce(Ue,[["__scopeId","data-v-09fae0e2"]]);export{Pe as default};
