"""
配置管理模块
"""
import os
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field


class ServiceConfig(BaseModel):
    """服务配置模型"""
    name: str
    path: str
    port: Optional[int] = None
    dockerfile: Optional[str] = None
    build_context: str = "."
    dependencies: List[str] = Field(default_factory=list)
    build_command: Optional[str] = None
    dist_path: Optional[str] = None


class EnvironmentConfig(BaseModel):
    """环境配置模型"""
    maven_profiles: str
    docker_registry: str
    skip_tests: bool = False


class BuildOptions(BaseModel):
    """构建选项模型"""
    parallel_build: bool = True
    max_parallel: int = 4
    cleanup_after_build: bool = True
    docker_build_args: List[str] = Field(default_factory=list)
    maven_args: List[str] = Field(default_factory=list)


class BuildConfig(BaseModel):
    """构建配置模型"""
    environments: Dict[str, EnvironmentConfig]
    options: BuildOptions


class Settings:
    """配置管理类"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._get_default_config_path()
        self.project_root = self._find_project_root()
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        current_dir = Path(__file__).parent.parent.parent
        return str(current_dir / "config" / "services.yaml")
    
    def _find_project_root(self) -> Path:
        """查找项目根目录"""
        current_dir = Path.cwd()
        
        # 查找包含park-api目录的父目录
        while current_dir != current_dir.parent:
            if (current_dir / "park-api").exists():
                return current_dir
            current_dir = current_dir.parent
        
        # 如果没找到，返回当前目录的父目录
        return Path.cwd().parent
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 解析服务配置
            self.backend_services = {}
            self.frontend_services = {}
            
            if 'services' in config_data:
                if 'backend' in config_data['services']:
                    for service_id, service_data in config_data['services']['backend'].items():
                        self.backend_services[service_id] = ServiceConfig(**service_data)
                
                if 'frontend' in config_data['services']:
                    for service_id, service_data in config_data['services']['frontend'].items():
                        self.frontend_services[service_id] = ServiceConfig(**service_data)
            
            # 解析构建配置
            if 'build_config' in config_data:
                self.build_config = BuildConfig(**config_data['build_config'])
            else:
                # 默认构建配置
                self.build_config = BuildConfig(
                    environments={
                        'dev': EnvironmentConfig(
                            maven_profiles='dev',
                            docker_registry='localhost:5000',
                            skip_tests=True
                        )
                    },
                    options=BuildOptions()
                )
                
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def get_backend_service(self, service_id: str) -> Optional[ServiceConfig]:
        """获取后端服务配置"""
        return self.backend_services.get(service_id)
    
    def get_frontend_service(self, service_id: str) -> Optional[ServiceConfig]:
        """获取前端服务配置"""
        return self.frontend_services.get(service_id)
    
    def get_all_backend_services(self) -> Dict[str, ServiceConfig]:
        """获取所有后端服务配置"""
        return self.backend_services
    
    def get_all_frontend_services(self) -> Dict[str, ServiceConfig]:
        """获取所有前端服务配置"""
        return self.frontend_services
    
    def get_environment_config(self, env: str) -> Optional[EnvironmentConfig]:
        """获取环境配置"""
        return self.build_config.environments.get(env)
    
    def get_build_options(self) -> BuildOptions:
        """获取构建选项"""
        return self.build_config.options
    
    def get_service_path(self, service_config: ServiceConfig) -> Path:
        """获取服务的完整路径"""
        return self.project_root / "park-api" / service_config.path
    
    def get_frontend_path(self, service_config: ServiceConfig) -> Path:
        """获取前端项目的完整路径"""
        return self.project_root / service_config.path


# 全局配置实例
settings = Settings()
