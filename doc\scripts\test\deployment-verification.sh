#!/bin/bash

# 部署验证脚本
# 综合验证部署是否成功，包括网络架构、容器状态、应用功能等

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 默认参数
DEPLOY_ENV="test"
VERBOSE="false"
TIMEOUT=60
COMPREHENSIVE="false"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            DEPLOY_ENV="${1#*=}"
            shift
            ;;
        --verbose)
            VERBOSE="true"
            shift
            ;;
        --timeout=*)
            TIMEOUT="${1#*=}"
            shift
            ;;
        --comprehensive)
            COMPREHENSIVE="true"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV        部署环境 (test|staging|prod) 默认: test"
            echo "  --verbose        详细输出"
            echo "  --timeout=SEC    超时时间（秒） 默认: 60"
            echo "  --comprehensive  全面验证"
            echo "  -h, --help       显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

log "INFO" "开始部署验证 - 环境: $DEPLOY_ENV"
log "INFO" "外部访问地址: $EXTERNAL_URL"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
CRITICAL_FAILURES=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local is_critical="${3:-false}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "执行验证: $test_name"
    fi
    
    if eval "$test_command"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        if [ "$VERBOSE" = "true" ]; then
            log "INFO" "✓ $test_name - 通过"
        fi
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        if [ "$is_critical" = "true" ]; then
            CRITICAL_FAILURES=$((CRITICAL_FAILURES + 1))
            log "ERROR" "✗ $test_name - 失败 (关键)"
        else
            log "WARN" "✗ $test_name - 失败"
        fi
        return 1
    fi
}

# 1. 基础设施验证
verify_infrastructure() {
    log "INFO" "=== 基础设施验证 ==="
    
    # Docker服务
    run_test "Docker服务状态" "systemctl is-active docker > /dev/null 2>&1" "true"
    
    # Nginx服务
    run_test "Nginx服务状态" "systemctl is-active nginx > /dev/null 2>&1" "true"
    
    # 防火墙转发规则
    run_test "防火墙转发规则" "iptables -t nat -L -n | grep -q '${EXTERNAL_PORT}.*${FIREWALL_INTERNAL_PORT}'" "true"
    
    # 系统端口监听
    run_test "系统端口监听" "netstat -tlnp | grep -q ':${FIREWALL_INTERNAL_PORT}' && netstat -tlnp | grep -q ':${JENKINS_SYSTEM_PORT}' && netstat -tlnp | grep -q ':${FRONTEND_SYSTEM_PORT}'" "true"
}

# 2. 容器状态验证
verify_containers() {
    log "INFO" "=== 容器状态验证 ==="
    
    # Jenkins容器
    run_test "Jenkins容器运行" "docker ps --filter 'name=${JENKINS_CONTAINER}' --filter 'status=running' | grep -q '${JENKINS_CONTAINER}'" "true"
    
    # Nginx容器
    run_test "Nginx容器运行" "docker ps --filter 'name=${NGINX_CONTAINER}' --filter 'status=running' | grep -q '${NGINX_CONTAINER}'" "true"
    
    # 后端容器
    run_test "后端容器运行" "docker ps --filter 'name=${BACKEND_CONTAINER}' --filter 'status=running' | grep -q '${BACKEND_CONTAINER}'" "false"
    
    # 容器健康状态
    if [ "$COMPREHENSIVE" = "true" ]; then
        run_test "Jenkins容器健康" "docker inspect ${JENKINS_CONTAINER} | grep -q '\"Status\": \"running\"'" "false"
        run_test "Nginx容器健康" "docker inspect ${NGINX_CONTAINER} | grep -q '\"Status\": \"running\"'" "false"
    fi
}

# 3. 网络连通性验证
verify_network_connectivity() {
    log "INFO" "=== 网络连通性验证 ==="
    
    # DNS解析
    run_test "DNS解析" "nslookup ${BASE_DOMAIN} > /dev/null 2>&1" "true"
    
    # 外部访问
    run_test "前端外部访问" "curl -f -s --max-time ${TIMEOUT} '${FRONTEND_URL}' > /dev/null" "true"
    
    # Jenkins访问
    run_test "Jenkins外部访问" "curl -f -s --max-time ${TIMEOUT} '${JENKINS_URL}' > /dev/null" "true"
    
    # API访问
    run_test "API外部访问" "curl -f -s --max-time ${TIMEOUT} '${API_URL}/actuator/health' > /dev/null" "false"
}

# 4. 应用功能验证
verify_application_functionality() {
    log "INFO" "=== 应用功能验证 ==="
    
    # API健康检查
    run_test "API健康状态" "curl -s --max-time ${TIMEOUT} '${API_URL}/actuator/health' | grep -q 'UP'" "false"
    
    # 数据库连接
    run_test "数据库连接" "curl -s --max-time ${TIMEOUT} '${API_URL}/actuator/health/db' | grep -q 'UP'" "false"
    
    # Redis连接
    run_test "Redis连接" "curl -s --max-time ${TIMEOUT} '${API_URL}/actuator/health/redis' | grep -q 'UP'" "false"
    
    if [ "$COMPREHENSIVE" = "true" ]; then
        # 业务接口测试
        run_test "系统信息接口" "curl -f -s --max-time ${TIMEOUT} '${API_URL}/v1/system/info' > /dev/null" "false"
        
        # 前端资源加载
        run_test "前端资源加载" "curl -f -s --max-time ${TIMEOUT} '${FRONTEND_URL}static/js/' > /dev/null" "false"
    fi
}

# 5. 性能验证
verify_performance() {
    if [ "$COMPREHENSIVE" = "true" ]; then
        log "INFO" "=== 性能验证 ==="
        
        # 响应时间测试
        local response_time=$(curl -o /dev/null -s -w "%{time_total}" --max-time ${TIMEOUT} "${FRONTEND_URL}")
        run_test "前端响应时间(<5s)" "awk 'BEGIN {exit !($response_time < 5.0)}'" "false"
        
        # 容器资源使用
        run_test "容器资源使用正常" "docker stats --no-stream --format '{{.CPUPerc}}' ${JENKINS_CONTAINER} | sed 's/%//' | awk '{exit !(\$1 < 80)}'" "false"
    fi
}

# 6. 安全验证
verify_security() {
    if [ "$COMPREHENSIVE" = "true" ]; then
        log "INFO" "=== 安全验证 ==="
        
        # HTTPS证书
        run_test "HTTPS证书有效" "echo | openssl s_client -servername '${BASE_DOMAIN}' -connect '${BASE_DOMAIN}:${EXTERNAL_PORT}' 2>/dev/null | openssl x509 -noout -dates > /dev/null 2>&1" "false"
        
        # 端口安全
        run_test "敏感端口未暴露" "! nmap -p 3306,6379,8080 ${SERVER_HOST} 2>/dev/null | grep -q 'open'" "false"
    fi
}

# 生成验证报告
generate_report() {
    log "INFO" "=== 部署验证报告 ==="
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    
    echo "验证结果统计:"
    echo "- 总测试数: $TOTAL_TESTS"
    echo "- 通过数: $PASSED_TESTS"
    echo "- 失败数: $FAILED_TESTS"
    echo "- 关键失败: $CRITICAL_FAILURES"
    echo "- 成功率: ${success_rate}%"
    
    # 生成JSON报告
    cat > "${LOGS_DIR}/deployment-verification-report.json" << EOF
{
    "deployment_verification": {
        "environment": "$DEPLOY_ENV",
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "external_url": "$EXTERNAL_URL",
        "results": {
            "total_tests": $TOTAL_TESTS,
            "passed_tests": $PASSED_TESTS,
            "failed_tests": $FAILED_TESTS,
            "critical_failures": $CRITICAL_FAILURES,
            "success_rate": $success_rate
        },
        "status": "$([ $CRITICAL_FAILURES -eq 0 ] && echo 'SUCCESS' || echo 'FAILED')"
    }
}
EOF
    
    log "INFO" "验证报告已保存: ${LOGS_DIR}/deployment-verification-report.json"
}

# 主验证函数
main() {
    # 创建日志目录
    mkdir -p "$LOGS_DIR"
    
    # 执行验证
    verify_infrastructure
    verify_containers
    verify_network_connectivity
    verify_application_functionality
    verify_performance
    verify_security
    
    # 生成报告
    generate_report
    
    # 判断验证结果
    if [ $CRITICAL_FAILURES -gt 0 ]; then
        log "ERROR" "部署验证失败，有 $CRITICAL_FAILURES 个关键问题"
        exit 1
    elif [ $FAILED_TESTS -gt 0 ]; then
        log "WARN" "部署验证完成，但有 $FAILED_TESTS 个非关键问题"
        exit 0
    else
        log "INFO" "部署验证全部通过"
        exit 0
    fi
}

# 执行主函数
main "$@"
