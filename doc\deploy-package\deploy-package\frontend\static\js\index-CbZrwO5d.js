import{_ as me,O as ce,d as fe,r as y,C as _e,T as ge,e as p,Q as K,c as N,o as s,R as h,f as t,S as ye,l as e,h as o,m as O,k as x,j as c,L,M as Q,n as m,P as j,i as A,t as M}from"./index-D_FV2sri.js";import{l as ve,g as he,d as be,u as Ce,a as Ve}from"./operator-CP4zw84v.js";import{C as G}from"./index-C9zsUI-h.js";const we={class:"app-container"},Ne={key:1},ke={key:1},Pe={class:"dialog-footer"},xe=ce({name:"Operator"}),Se=Object.assign(xe,{components:{CustomPagination:G}},{setup(Ue){const{proxy:f}=fe(),{operator_status:d}=f.useDict("operator_status"),R=y([]),v=y(!1),S=y(!0),k=y(!0),U=y([]),q=y(!0),z=y(!0),T=y(0),D=y(""),H=_e({form:{},queryParams:{pageNum:1,pageSize:10,companyName:null,primaryContactName:null,primaryContactPhone:null,status:null},rules:{companyName:[{required:!0,message:"运营商公司名称不能为空",trigger:"blur"}],primaryContactName:[{required:!0,message:"主要联系人姓名不能为空",trigger:"blur"}],primaryContactPhone:[{required:!0,message:"主要联系人手机号不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:r,form:u,rules:J}=ge(H);function b(){S.value=!0,ve(r.value).then(i=>{R.value=i.rows,T.value=i.total,S.value=!1})}function W(){v.value=!1,$()}function $(){u.value={id:null,companyName:null,primaryContactName:null,primaryContactPhone:null,status:1,remark:null},f.resetForm("operatorRef")}function w(){r.value.pageNum=1,b()}function X(){f.resetForm("queryRef"),Object.assign(r.value,{pageNum:1,pageSize:10,companyName:null,primaryContactName:null,primaryContactPhone:null,status:null}),w()}function Y(i){U.value=i.map(l=>l.id),q.value=i.length!=1,z.value=!i.length}function Z(){$(),v.value=!0,D.value="添加运营商信息"}function B(i){$();const l=i.id||U.value;he(l).then(n=>{u.value=n.data,v.value=!0,D.value="修改运营商信息"})}function ee(){f.$refs.operatorRef.validate(i=>{if(i){const l={...u.value};try{const n=f.$store.state.user;n&&n.id&&(u.value.id||(l.createdBy=n.id),l.updatedBy=n.id)}catch(n){console.warn("获取用户ID失败:",n)}u.value.id!=null?Ce(l).then(n=>{f.$modal.msgSuccess("修改成功"),v.value=!1,b()}):Ve(l).then(n=>{f.$modal.msgSuccess("新增成功"),v.value=!1,b()})}})}function I(i){const l=i.id||U.value;f.$modal.confirm('是否确认删除运营商信息编号为"'+l+'"的数据项？').then(function(){return be(l)}).then(()=>{b(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ae(){f.download("system/platform/operator/export",{...r.value},`operator_${new Date().getTime()}.xlsx`)}return b(),(i,l)=>{const n=p("el-input"),_=p("el-form-item"),E=p("el-option"),te=p("el-select"),g=p("el-button"),F=p("el-form"),P=p("el-col"),le=p("right-toolbar"),oe=p("el-row"),C=p("el-table-column"),ne=p("dict-tag"),re=p("el-table"),ue=p("el-radio"),se=p("el-radio-group"),pe=p("el-dialog"),V=K("hasPermi"),ie=K("loading");return s(),N("div",we,[h(t(F,{model:e(r),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[t(_,{label:"公司名称",prop:"companyName"},{default:o(()=>[t(n,{modelValue:e(r).companyName,"onUpdate:modelValue":l[0]||(l[0]=a=>e(r).companyName=a),placeholder:"请输入运营商公司名称",clearable:"",onKeyup:O(w,["enter"])},null,8,["modelValue"])]),_:1}),t(_,{label:"联系人",prop:"primaryContactName"},{default:o(()=>[t(n,{modelValue:e(r).primaryContactName,"onUpdate:modelValue":l[1]||(l[1]=a=>e(r).primaryContactName=a),placeholder:"请输入主要联系人姓名",clearable:"",onKeyup:O(w,["enter"])},null,8,["modelValue"])]),_:1}),t(_,{label:"联系手机号",prop:"primaryContactPhone"},{default:o(()=>[t(n,{modelValue:e(r).primaryContactPhone,"onUpdate:modelValue":l[2]||(l[2]=a=>e(r).primaryContactPhone=a),placeholder:"请输入主要联系人手机号",clearable:"",onKeyup:O(w,["enter"])},null,8,["modelValue"])]),_:1}),t(_,{label:"状态",prop:"status"},{default:o(()=>[t(te,{modelValue:e(r).status,"onUpdate:modelValue":l[3]||(l[3]=a=>e(r).status=a),placeholder:"运营商状态",clearable:"",style:{width:"120px"}},{default:o(()=>[e(d)&&e(d).length>0?(s(!0),N(L,{key:0},Q(e(d),a=>(s(),c(E,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128)):x("",!0),!e(d)||e(d).length===0?(s(),c(E,{key:1,disabled:"",value:""},{default:o(()=>[m("字典数据加载中...")]),_:1})):x("",!0)]),_:1},8,["modelValue"])]),_:1}),t(_,null,{default:o(()=>[t(g,{type:"primary",icon:"Search",onClick:w},{default:o(()=>[m("搜索")]),_:1}),t(g,{icon:"Refresh",onClick:X},{default:o(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ye,e(k)]]),t(oe,{gutter:10,class:"mb8"},{default:o(()=>[t(P,{span:1.5},{default:o(()=>[h((s(),c(g,{type:"primary",plain:"",icon:"Plus",onClick:Z},{default:o(()=>[m("新增")]),_:1})),[[V,["platform:operator:add"]]])]),_:1}),t(P,{span:1.5},{default:o(()=>[h((s(),c(g,{type:"success",plain:"",icon:"Edit",disabled:e(q),onClick:B},{default:o(()=>[m("修改")]),_:1},8,["disabled"])),[[V,["platform:operator:edit"]]])]),_:1}),t(P,{span:1.5},{default:o(()=>[h((s(),c(g,{type:"danger",plain:"",icon:"Delete",disabled:e(z),onClick:I},{default:o(()=>[m("删除")]),_:1},8,["disabled"])),[[V,["platform:operator:remove"]]])]),_:1}),t(P,{span:1.5},{default:o(()=>[h((s(),c(g,{type:"warning",plain:"",icon:"Download",onClick:ae},{default:o(()=>[m("导出")]),_:1})),[[V,["platform:operator:export"]]])]),_:1}),t(le,{showSearch:e(k),"onUpdate:showSearch":l[4]||(l[4]=a=>j(k)?k.value=a:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),h((s(),c(re,{data:e(R),onSelectionChange:Y},{default:o(()=>[t(C,{type:"selection",width:"55",align:"center"}),t(C,{label:"公司名称",align:"center",prop:"companyName","show-overflow-tooltip":!0}),t(C,{label:"主要联系人",align:"center",prop:"primaryContactName"}),t(C,{label:"联系手机号",align:"center",prop:"primaryContactPhone"}),t(C,{label:"状态",align:"center",prop:"status",width:"100"},{default:o(a=>[e(d)&&e(d).length>0?(s(),c(ne,{key:0,options:e(d),value:a.row.status},null,8,["options","value"])):(s(),N("span",Ne,"加载中..."))]),_:1}),t(C,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:o(a=>[A("span",null,M(i.parseTime(a.row.createTime)),1)]),_:1}),t(C,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"150"},{default:o(a=>[h((s(),c(g,{link:"",type:"primary",icon:"Edit",onClick:de=>B(a.row)},{default:o(()=>[m("修改")]),_:2},1032,["onClick"])),[[V,["platform:operator:edit"]]]),h((s(),c(g,{link:"",type:"primary",icon:"Delete",onClick:de=>I(a.row)},{default:o(()=>[m("删除")]),_:2},1032,["onClick"])),[[V,["platform:operator:remove"]]])]),_:1})]),_:1},8,["data"])),[[ie,e(S)]]),t(G,{total:e(T),"current-page":e(r).pageNum,"onUpdate:currentPage":l[5]||(l[5]=a=>e(r).pageNum=a),"page-size":e(r).pageSize,"onUpdate:pageSize":l[6]||(l[6]=a=>e(r).pageSize=a),onPagination:b},null,8,["total","current-page","page-size"]),t(pe,{title:e(D),modelValue:e(v),"onUpdate:modelValue":l[12]||(l[12]=a=>j(v)?v.value=a:null),width:"500px","append-to-body":""},{footer:o(()=>[A("div",Pe,[t(g,{type:"primary",onClick:ee},{default:o(()=>[m("确 定")]),_:1}),t(g,{onClick:W},{default:o(()=>[m("取 消")]),_:1})])]),default:o(()=>[t(F,{ref:"operatorRef",model:e(u),rules:e(J),"label-width":"100px"},{default:o(()=>[t(_,{label:"公司名称",prop:"companyName"},{default:o(()=>[t(n,{modelValue:e(u).companyName,"onUpdate:modelValue":l[7]||(l[7]=a=>e(u).companyName=a),placeholder:"请输入运营商公司名称"},null,8,["modelValue"])]),_:1}),t(_,{label:"主要联系人",prop:"primaryContactName"},{default:o(()=>[t(n,{modelValue:e(u).primaryContactName,"onUpdate:modelValue":l[8]||(l[8]=a=>e(u).primaryContactName=a),placeholder:"请输入主要联系人姓名"},null,8,["modelValue"])]),_:1}),t(_,{label:"联系手机号",prop:"primaryContactPhone"},{default:o(()=>[t(n,{modelValue:e(u).primaryContactPhone,"onUpdate:modelValue":l[9]||(l[9]=a=>e(u).primaryContactPhone=a),placeholder:"请输入主要联系人手机号",maxlength:"11","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(_,{label:"状态",prop:"status"},{default:o(()=>[t(se,{modelValue:e(u).status,"onUpdate:modelValue":l[10]||(l[10]=a=>e(u).status=a)},{default:o(()=>[e(d)&&e(d).length>0?(s(!0),N(L,{key:0},Q(e(d),a=>(s(),c(ue,{key:a.value,value:parseInt(a.value)},{default:o(()=>[m(M(a.label),1)]),_:2},1032,["value"]))),128)):x("",!0),!e(d)||e(d).length===0?(s(),N("span",ke,"字典数据加载中...")):x("",!0)]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"备注",prop:"remark"},{default:o(()=>[t(n,{modelValue:e(u).remark,"onUpdate:modelValue":l[11]||(l[11]=a=>e(u).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Re=me(Se,[["__scopeId","data-v-13c89780"]]);export{Re as default};
