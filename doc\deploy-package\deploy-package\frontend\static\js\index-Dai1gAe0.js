import{v as k,_ as Ue,O as xe,d as De,r as _,C as Ee,T as $e,x as qe,e as f,Q as se,c as V,o as a,R as S,f as l,S as Re,h as u,m as re,l as t,L as R,M as Y,j as i,n as p,i as w,t as D,a0 as M,k as P,A as Ye,B as Me}from"./index-D_FV2sri.js";import{h as Pe}from"./template-Dro944Br.js";import{C as de}from"./index-C9zsUI-h.js";function Ae(d){return k({url:"/system/coupon/user/list",method:"get",params:d})}function Be(d){return k({url:"/system/coupon/user/"+d,method:"get"})}function Fe(d){return k({url:"/system/coupon/user",method:"post",data:d})}function ze(d){return k({url:"/system/coupon/user",method:"put",data:d})}function He(d){return k({url:"/system/coupon/user/"+d,method:"delete"})}function Le(d){return k({url:"/system/coupon/user/issue",method:"post",params:d})}function Qe(d,g){return k({url:"/system/coupon/user/void/"+d,method:"put",params:{reason:g}})}function Ke(){return k({url:"/system/coupon/user/updateExpired",method:"put"})}const Oe=d=>(Ye("data-v-d4d76a40"),d=d(),Me(),d),je={class:"app-container"},Ge=Oe(()=>w("br",null,null,-1)),Je={key:0},We={key:1},Xe={key:0,class:"amount-text"},Ze={key:1},el={key:1},ll={class:"dialog-footer"},ul={class:"dialog-footer"},ol=xe({name:"CouponUser"}),tl=Object.assign(ol,{components:{CustomPagination:de}},{setup(d){const{proxy:g}=De(),{coupon_use_status:A,coupon_use_scene:O}=g.useDict("coupon_use_status","coupon_use_scene"),B=_(),T=_(),N=_(),j=_([]),G=_([]),C=_(!1),U=_(!1),F=_(!0),z=_(!0),H=_([]),J=_(!0),W=_(!0),X=_(0),Z=_(""),ie=Ee({form:{},queryParams:{pageNum:1,pageSize:10,couponNo:null,phoneNumber:null,useStatus:null,useScene:null},rules:{couponNo:[{required:!0,message:"优惠券编号不能为空",trigger:"blur"}],receiveTime:[{required:!0,message:"领取时间不能为空",trigger:"change"}],validStartTime:[{required:!0,message:"有效期开始时间不能为空",trigger:"change"}],validEndTime:[{required:!0,message:"有效期结束时间不能为空",trigger:"change"}],useStatus:[{required:!0,message:"使用状态不能为空",trigger:"change"}]},issueForm:{couponId:null,issueType:1,phoneNumber:null,userId:null,memberId:null},issueRules:{couponId:[{required:!0,message:"请选择优惠券",trigger:"change"}],phoneNumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],userId:[{required:!0,message:"用户ID不能为空",trigger:"blur"}],memberId:[{required:!0,message:"会员ID不能为空",trigger:"blur"}]}}),{queryParams:m,form:r,rules:pe,issueForm:n,issueRules:me}=$e(ie);function y(){F.value=!0,Ae(m.value).then(s=>{j.value=s.rows,X.value=s.total,F.value=!1})}function ce(){Pe(2).then(s=>{G.value=s.data})}function fe(){C.value=!1,ee()}function ve(){U.value=!1,le()}function ee(){r.value={id:null,couponId:null,userId:null,memberId:null,couponNo:null,phoneNumber:null,receiveTime:null,validStartTime:null,validEndTime:null,useStatus:0,useTime:null,useScene:null,orderNo:null,parkingLotId:null,originalAmount:null,discountAmount:null,finalAmount:null},T.value&&T.value.resetFields()}function le(){n.value={couponId:null,issueType:1,phoneNumber:null,userId:null,memberId:null},N.value&&N.value.resetFields()}function E(){m.value.pageNum=1,y()}function _e(){B.value&&B.value.resetFields(),E()}function ge(s){H.value=s.map(o=>o.id),J.value=s.length!==1,W.value=!s.length}function be(){le(),ce(),U.value=!0}function ue(s){ee();const o=s.id||H.value;Be(o).then(h=>{r.value=h.data,C.value=!0,Z.value="修改用户优惠券"})}function ye(){T.value&&T.value.validate(s=>{s&&(r.value.id!=null?ze(r.value).then(o=>{g.$modal.msgSuccess("修改成功"),C.value=!1,y()}):Fe(r.value).then(o=>{g.$modal.msgSuccess("新增成功"),C.value=!1,y()}))})}function he(){N.value&&N.value.validate(s=>{if(s){const o={couponId:n.value.couponId};n.value.issueType===1?o.phoneNumber=n.value.phoneNumber:n.value.issueType===2?o.userId=n.value.userId:n.value.issueType===3&&(o.memberId=n.value.memberId),Le(o).then(h=>{g.$modal.msgSuccess("发放成功"),U.value=!1,y()})}})}function oe(s){const o=s.id||H.value;g.$modal.confirm('是否确认删除用户优惠券编号为"'+o+'"的数据项？').then(function(){return He(o)}).then(()=>{y(),g.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Ve(s){g.$modal.prompt("请输入作废原因","作废优惠券",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/.+/,inputErrorMessage:"作废原因不能为空"}).then(({value:o})=>{Qe(s.id,o).then(()=>{g.$modal.msgSuccess("作废成功"),y()})}).catch(()=>{})}function Se(){g.download("system/coupon/user/export",{...m.value},`user_coupon_${new Date().getTime()}.xlsx`)}function Ie(){g.$modal.confirm("是否确认更新所有过期优惠券的状态？").then(function(){return Ke()}).then(s=>{y(),g.$modal.msgSuccess(s.msg)}).catch(()=>{})}return qe(()=>{y()}),(s,o)=>{const h=f("el-input"),c=f("el-form-item"),$=f("el-option"),q=f("el-select"),v=f("el-button"),L=f("el-form"),x=f("el-col"),ke=f("right-toolbar"),Ce=f("el-row"),b=f("el-table-column"),te=f("dict-tag"),we=f("el-table"),Q=f("el-date-picker"),ae=f("el-dialog"),K=f("el-radio"),Te=f("el-radio-group"),I=se("hasPermi"),Ne=se("loading");return a(),V("div",je,[S(l(L,{model:t(m),ref_key:"queryRef",ref:B,inline:!0,"label-width":"68px"},{default:u(()=>[l(c,{label:"优惠券编号",prop:"couponNo"},{default:u(()=>[l(h,{modelValue:t(m).couponNo,"onUpdate:modelValue":o[0]||(o[0]=e=>t(m).couponNo=e),placeholder:"请输入优惠券编号",clearable:"",onKeyup:re(E,["enter"])},null,8,["modelValue"])]),_:1}),l(c,{label:"手机号码",prop:"phoneNumber"},{default:u(()=>[l(h,{modelValue:t(m).phoneNumber,"onUpdate:modelValue":o[1]||(o[1]=e=>t(m).phoneNumber=e),placeholder:"请输入手机号码",clearable:"",onKeyup:re(E,["enter"])},null,8,["modelValue"])]),_:1}),l(c,{label:"使用状态",prop:"useStatus"},{default:u(()=>[l(q,{modelValue:t(m).useStatus,"onUpdate:modelValue":o[2]||(o[2]=e=>t(m).useStatus=e),placeholder:"请选择使用状态",clearable:"",style:{width:"180px"}},{default:u(()=>[(a(!0),V(R,null,Y(t(A),e=>(a(),i($,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"使用场景",prop:"useScene"},{default:u(()=>[l(q,{modelValue:t(m).useScene,"onUpdate:modelValue":o[3]||(o[3]=e=>t(m).useScene=e),placeholder:"请选择使用场景",clearable:"",style:{width:"180px"}},{default:u(()=>[(a(!0),V(R,null,Y(t(O),e=>(a(),i($,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,null,{default:u(()=>[l(v,{type:"primary",icon:"Search",onClick:E},{default:u(()=>[p("搜索")]),_:1}),l(v,{icon:"Refresh",onClick:_e},{default:u(()=>[p("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Re,z.value]]),l(Ce,{gutter:10,class:"mb8"},{default:u(()=>[l(x,{span:1.5},{default:u(()=>[S((a(),i(v,{type:"primary",plain:"",icon:"Plus",onClick:be},{default:u(()=>[p("发放优惠券")]),_:1})),[[I,["coupon:user:issue"]]])]),_:1}),l(x,{span:1.5},{default:u(()=>[S((a(),i(v,{type:"success",plain:"",icon:"Edit",disabled:J.value,onClick:ue},{default:u(()=>[p("修改")]),_:1},8,["disabled"])),[[I,["coupon:user:edit"]]])]),_:1}),l(x,{span:1.5},{default:u(()=>[S((a(),i(v,{type:"danger",plain:"",icon:"Delete",disabled:W.value,onClick:oe},{default:u(()=>[p("删除")]),_:1},8,["disabled"])),[[I,["coupon:user:remove"]]])]),_:1}),l(x,{span:1.5},{default:u(()=>[S((a(),i(v,{type:"warning",plain:"",icon:"Download",onClick:Se},{default:u(()=>[p("导出")]),_:1})),[[I,["coupon:user:export"]]])]),_:1}),l(x,{span:1.5},{default:u(()=>[S((a(),i(v,{type:"info",plain:"",icon:"Refresh",onClick:Ie},{default:u(()=>[p("更新过期状态")]),_:1})),[[I,["coupon:user:edit"]]])]),_:1}),l(ke,{showSearch:z.value,"onUpdate:showSearch":o[4]||(o[4]=e=>z.value=e),onQueryTable:y},null,8,["showSearch"])]),_:1}),S((a(),i(we,{data:j.value,onSelectionChange:ge},{default:u(()=>[l(b,{type:"selection",width:"55",align:"center"}),l(b,{label:"优惠券编号",align:"center",prop:"couponNo"}),l(b,{label:"优惠券名称",align:"center",prop:"couponName"}),l(b,{label:"手机号码",align:"center",prop:"phoneNumber"}),l(b,{label:"领取时间",align:"center",prop:"receiveTime",width:"180"},{default:u(e=>[w("span",null,D(t(M)(e.row.receiveTime,"{y}-{m}-{d} {h}:{i}")),1)]),_:1}),l(b,{label:"有效期",align:"center",width:"180"},{default:u(e=>[w("span",null,D(t(M)(e.row.validStartTime,"{y}-{m}-{d}")),1),Ge,w("span",null,"至 "+D(t(M)(e.row.validEndTime,"{y}-{m}-{d}")),1)]),_:1}),l(b,{label:"使用状态",align:"center",prop:"useStatus"},{default:u(e=>[l(te,{options:t(A),value:e.row.useStatus},null,8,["options","value"])]),_:1}),l(b,{label:"使用时间",align:"center",prop:"useTime",width:"180"},{default:u(e=>[e.row.useTime?(a(),V("span",Je,D(t(M)(e.row.useTime,"{y}-{m}-{d} {h}:{i}")),1)):(a(),V("span",We,"-"))]),_:1}),l(b,{label:"优惠金额",align:"center",prop:"discountAmount"},{default:u(e=>[e.row.discountAmount?(a(),V("span",Xe,D(e.row.discountAmount)+"元",1)):(a(),V("span",Ze,"-"))]),_:1}),l(b,{label:"使用场景",align:"center",prop:"useScene"},{default:u(e=>[e.row.useScene?(a(),i(te,{key:0,options:t(O),value:e.row.useScene},null,8,["options","value"])):(a(),V("span",el,"-"))]),_:1}),l(b,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:u(e=>[S((a(),i(v,{link:"",type:"primary",icon:"Edit",onClick:ne=>ue(e.row)},{default:u(()=>[p("修改")]),_:2},1032,["onClick"])),[[I,["coupon:user:edit"]]]),S((a(),i(v,{link:"",type:"primary",icon:"Delete",onClick:ne=>oe(e.row)},{default:u(()=>[p("删除")]),_:2},1032,["onClick"])),[[I,["coupon:user:remove"]]]),e.row.useStatus===0?S((a(),i(v,{key:0,link:"",type:"danger",icon:"Close",onClick:ne=>Ve(e.row)},{default:u(()=>[p("作废")]),_:2},1032,["onClick"])),[[I,["coupon:user:void"]]]):P("",!0)]),_:1})]),_:1},8,["data"])),[[Ne,F.value]]),l(de,{total:X.value,"current-page":t(m).pageNum,"onUpdate:currentPage":o[5]||(o[5]=e=>t(m).pageNum=e),"page-size":t(m).pageSize,"onUpdate:pageSize":o[6]||(o[6]=e=>t(m).pageSize=e),onPagination:y},null,8,["total","current-page","page-size"]),l(ae,{title:Z.value,modelValue:C.value,"onUpdate:modelValue":o[13]||(o[13]=e=>C.value=e),width:"600px","append-to-body":""},{footer:u(()=>[w("div",ll,[l(v,{type:"primary",onClick:ye},{default:u(()=>[p("确 定")]),_:1}),l(v,{onClick:fe},{default:u(()=>[p("取 消")]),_:1})])]),default:u(()=>[l(L,{ref_key:"userCouponRef",ref:T,model:t(r),rules:t(pe),"label-width":"100px"},{default:u(()=>[l(c,{label:"优惠券编号",prop:"couponNo"},{default:u(()=>[l(h,{modelValue:t(r).couponNo,"onUpdate:modelValue":o[7]||(o[7]=e=>t(r).couponNo=e),placeholder:"请输入优惠券编号",disabled:t(r).id!=null},null,8,["modelValue","disabled"])]),_:1}),l(c,{label:"手机号码",prop:"phoneNumber"},{default:u(()=>[l(h,{modelValue:t(r).phoneNumber,"onUpdate:modelValue":o[8]||(o[8]=e=>t(r).phoneNumber=e),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),l(c,{label:"领取时间",prop:"receiveTime"},{default:u(()=>[l(Q,{modelValue:t(r).receiveTime,"onUpdate:modelValue":o[9]||(o[9]=e=>t(r).receiveTime=e),type:"datetime",placeholder:"选择领取时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(c,{label:"有效期开始时间",prop:"validStartTime"},{default:u(()=>[l(Q,{modelValue:t(r).validStartTime,"onUpdate:modelValue":o[10]||(o[10]=e=>t(r).validStartTime=e),type:"datetime",placeholder:"选择有效期开始时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(c,{label:"有效期结束时间",prop:"validEndTime"},{default:u(()=>[l(Q,{modelValue:t(r).validEndTime,"onUpdate:modelValue":o[11]||(o[11]=e=>t(r).validEndTime=e),type:"datetime",placeholder:"选择有效期结束时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(c,{label:"使用状态",prop:"useStatus"},{default:u(()=>[l(q,{modelValue:t(r).useStatus,"onUpdate:modelValue":o[12]||(o[12]=e=>t(r).useStatus=e),placeholder:"请选择使用状态"},{default:u(()=>[(a(!0),V(R,null,Y(t(A),e=>(a(),i($,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(ae,{title:"发放优惠券",modelValue:U.value,"onUpdate:modelValue":o[19]||(o[19]=e=>U.value=e),width:"600px","append-to-body":""},{footer:u(()=>[w("div",ul,[l(v,{type:"primary",onClick:he},{default:u(()=>[p("确 定")]),_:1}),l(v,{onClick:ve},{default:u(()=>[p("取 消")]),_:1})])]),default:u(()=>[l(L,{ref_key:"issueRef",ref:N,model:t(n),rules:t(me),"label-width":"100px"},{default:u(()=>[l(c,{label:"优惠券",prop:"couponId"},{default:u(()=>[l(q,{modelValue:t(n).couponId,"onUpdate:modelValue":o[14]||(o[14]=e=>t(n).couponId=e),placeholder:"请选择优惠券",filterable:""},{default:u(()=>[(a(!0),V(R,null,Y(G.value,e=>(a(),i($,{key:e.id,label:e.couponName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"发放方式",prop:"issueType"},{default:u(()=>[l(Te,{modelValue:t(n).issueType,"onUpdate:modelValue":o[15]||(o[15]=e=>t(n).issueType=e)},{default:u(()=>[l(K,{value:1},{default:u(()=>[p("按手机号")]),_:1}),l(K,{value:2},{default:u(()=>[p("按用户ID")]),_:1}),l(K,{value:3},{default:u(()=>[p("按会员ID")]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(n).issueType===1?(a(),i(c,{key:0,label:"手机号码",prop:"phoneNumber"},{default:u(()=>[l(h,{modelValue:t(n).phoneNumber,"onUpdate:modelValue":o[16]||(o[16]=e=>t(n).phoneNumber=e),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1})):P("",!0),t(n).issueType===2?(a(),i(c,{key:1,label:"用户ID",prop:"userId"},{default:u(()=>[l(h,{modelValue:t(n).userId,"onUpdate:modelValue":o[17]||(o[17]=e=>t(n).userId=e),placeholder:"请输入用户ID",type:"number"},null,8,["modelValue"])]),_:1})):P("",!0),t(n).issueType===3?(a(),i(c,{key:2,label:"会员ID",prop:"memberId"},{default:u(()=>[l(h,{modelValue:t(n).memberId,"onUpdate:modelValue":o[18]||(o[18]=e=>t(n).memberId=e),placeholder:"请输入会员ID",type:"number"},null,8,["modelValue"])]),_:1})):P("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),rl=Ue(tl,[["__scopeId","data-v-d4d76a40"]]);export{rl as default};
