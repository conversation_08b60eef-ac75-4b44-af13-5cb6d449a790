import{_ as Ne,O as xe,d as De,r as v,C as Ue,T as ze,e as s,Q as te,c as P,o as p,R as V,f as a,S as We,l as n,h as l,m as $e,L as x,M as D,j as i,n as c,P as ne,t as U,i as z,U as Re,k as qe,D as oe,A as Oe,B as Ee}from"./index-Cox2ohSR.js";import{l as Be,a as ue,b as je,d as Fe,u as Qe,c as Le}from"./package-hUyxr3P7.js";import{C as re}from"./index-Cy2yB1cz.js";const Ae=W=>(Oe("data-v-e73ad198"),W=W(),Ee(),W),Ke={class:"app-container"},Me=Ae(()=>z("i",{class:"el-icon-box",style:{"margin-right":"4px"}},null,-1)),Ge={style:{display:"flex","align-items":"center","justify-content":"center"}},He={class:"price-text"},Je={class:"dialog-footer"},Xe=xe({name:"VipPackage"}),Ye=Object.assign(Xe,{components:{CustomPagination:re}},{setup(W){const{proxy:k}=De(),{vip_package_type:R,vip_package_status:L,vip_member_type:q}=k.useDict("vip_package_type","vip_package_status","vip_member_type"),A=v([]),w=v(!1),O=v(!0),$=v(!0),E=v([]),K=v(!0),M=v(!0),G=v(0),B=v(""),H=v([]),C=v([]),pe=v(),se=Ue({form:{},queryParams:{pageNum:1,pageSize:10,packageName:null,packageType:null,vipType:null,warehouseId:null},rules:{packageName:[{required:!0,message:"套餐名称不能为空",trigger:"blur"}],packageType:[{required:!0,message:"套餐类型不能为空",trigger:"change"}],packagePrice:[{required:!0,message:"套餐价格不能为空",trigger:"blur"}],packageStatus:[{required:!0,message:"套餐状态不能为空",trigger:"change"}]}}),{queryParams:d,form:u,rules:ie}=ze(se);function T(){O.value=!0,Be(d.value).then(o=>{A.value=o.rows,G.value=o.total,O.value=!1})}function J(){ue().then(o=>{H.value=o.data,C.value=X(o.data)})}function X(o){if(!o||o.length===0)return[];const t=o.filter(r=>N(r)),m=o.filter(r=>!N(r));return t.map(r=>{const f=m.filter(g=>g.parentId===r.id).map(g=>({value:g.id,label:g.warehouseName,leaf:!0}));return{value:r.id,label:r.warehouseName,children:f.length>0?f:void 0}})}function de(o,t){if(!o||!t||t.length===0)return null;const m=String(o),r=t.find(g=>String(g.id)===m);if(!r)return null;if(N(r))return[r.id];const f=t.find(g=>String(g.id)===String(r.parentId));return f?[f.id,r.id]:[r.id]}function N(o){const t=o.parentId;return t===0||t==="0"||t===null||t===void 0}function ce(o){return N(o)?"primary":"success"}function me(o){return N(o)?"el-icon-office-building":"el-icon-location"}function fe(o,t){return o?o.length<=t?o:o.substring(0,t)+"...":""}function ge(){w.value=!1,j()}function j(){u.value={id:null,warehouseId:null,packageName:null,packageType:null,packagePrice:null,customDays:null,packageStatus:1,remark:null},k.resetForm("packageRef")}function F(){d.value.pageNum=1,T()}function _e(){k.resetForm("queryRef"),Object.assign(d.value,{pageNum:1,pageSize:10,packageName:null,packageType:null,vipType:null,warehouseId:null}),F()}function ve(o){E.value=o.map(t=>t.id),K.value=o.length!=1,M.value=!o.length}function ke(){j(),C.value.length===0&&J(),w.value=!0,B.value="添加会员套餐配置"}function Y(o){j();const t=o.id||E.value;Promise.all([ue(),je(t)]).then(([m,r])=>{H.value=m.data,C.value=X(m.data),w.value=!0,B.value="修改会员套餐配置",oe(()=>{u.value=r.data,oe(()=>{if(u.value.warehouseId){const f=de(u.value.warehouseId,m.data);f&&f.length>0&&(u.value.warehouseId=f)}})})}).catch(m=>{console.error("加载数据失败:",m),k.$modal.msgError("加载数据失败")})}function ye(o){u.value.customDays=null}function he(){k.$refs.packageRef.validate(o=>{if(o){if(u.value.packageType===0&&!u.value.customDays){k.$modal.msgError("请输入自定义天数");return}u.value.id!=null?Qe(u.value).then(t=>{k.$modal.msgSuccess("修改成功"),w.value=!1,T()}):Le(u.value).then(t=>{k.$modal.msgSuccess("新增成功"),w.value=!1,T()})}})}function Z(o){const t=o.id||E.value;k.$modal.confirm('是否确认删除会员套餐配置编号为"'+t+'"的数据项？').then(function(){return Fe(t)}).then(()=>{T(),k.$modal.msgSuccess("删除成功")}).catch(()=>{})}function be(){k.download("system/vip/package/export",{...d.value},`vip_package_${new Date().getTime()}.xlsx`)}return T(),J(),(o,t)=>{const m=s("el-input"),r=s("el-form-item"),f=s("el-option"),g=s("el-select"),ee=s("el-cascader"),y=s("el-button"),ae=s("el-form"),_=s("el-col"),we=s("right-toolbar"),b=s("el-row"),h=s("el-table-column"),S=s("el-tag"),Q=s("dict-tag"),Ve=s("el-table"),le=s("el-input-number"),Te=s("el-radio"),Se=s("el-radio-group"),Ie=s("el-dialog"),I=te("hasPermi"),Pe=te("loading");return p(),P("div",Ke,[V(a(ae,{model:n(d),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[a(r,{label:"套餐名称",prop:"packageName"},{default:l(()=>[a(m,{modelValue:n(d).packageName,"onUpdate:modelValue":t[0]||(t[0]=e=>n(d).packageName=e),placeholder:"请输入套餐名称",clearable:"",onKeyup:$e(F,["enter"])},null,8,["modelValue"])]),_:1}),a(r,{label:"套餐类型",prop:"packageType"},{default:l(()=>[a(g,{modelValue:n(d).packageType,"onUpdate:modelValue":t[1]||(t[1]=e=>n(d).packageType=e),placeholder:"套餐类型",clearable:"",style:{width:"200px"}},{default:l(()=>[(p(!0),P(x,null,D(n(R),e=>(p(),i(f,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"会员类型",prop:"vipType"},{default:l(()=>[a(g,{modelValue:n(d).vipType,"onUpdate:modelValue":t[2]||(t[2]=e=>n(d).vipType=e),placeholder:"会员类型",clearable:"",style:{width:"200px"}},{default:l(()=>[(p(!0),P(x,null,D(n(q),e=>(p(),i(f,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"场库/停车场",prop:"warehouseId"},{default:l(()=>[a(ee,{modelValue:n(d).warehouseId,"onUpdate:modelValue":t[3]||(t[3]=e=>n(d).warehouseId=e),options:n(C),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),a(r,null,{default:l(()=>[a(y,{type:"primary",icon:"Search",onClick:F},{default:l(()=>[c("搜索")]),_:1}),a(y,{icon:"Refresh",onClick:_e},{default:l(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[We,n($)]]),a(b,{gutter:10,class:"mb8"},{default:l(()=>[a(_,{span:1.5},{default:l(()=>[V((p(),i(y,{type:"primary",plain:"",icon:"Plus",onClick:ke},{default:l(()=>[c("新增")]),_:1})),[[I,["vip:package:add"]]])]),_:1}),a(_,{span:1.5},{default:l(()=>[V((p(),i(y,{type:"success",plain:"",icon:"Edit",disabled:n(K),onClick:Y},{default:l(()=>[c("修改")]),_:1},8,["disabled"])),[[I,["vip:package:edit"]]])]),_:1}),a(_,{span:1.5},{default:l(()=>[V((p(),i(y,{type:"danger",plain:"",icon:"Delete",disabled:n(M),onClick:Z},{default:l(()=>[c("删除")]),_:1},8,["disabled"])),[[I,["vip:package:remove"]]])]),_:1}),a(_,{span:1.5},{default:l(()=>[V((p(),i(y,{type:"warning",plain:"",icon:"Download",onClick:be},{default:l(()=>[c("导出")]),_:1})),[[I,["vip:package:export"]]])]),_:1}),a(we,{showSearch:n($),"onUpdate:showSearch":t[4]||(t[4]=e=>ne($)?$.value=e:null),onQueryTable:T},null,8,["showSearch"])]),_:1}),V((p(),i(Ve,{data:n(A),onSelectionChange:ve},{default:l(()=>[a(h,{type:"selection",width:"55",align:"center"}),a(h,{label:"套餐ID",align:"center",prop:"id"}),a(h,{label:"套餐名称",align:"center",prop:"packageName",width:"160","show-overflow-tooltip":!0},{default:l(e=>[a(S,{type:"warning",effect:"light",size:"small"},{default:l(()=>[Me,c(" "+U(e.row.packageName),1)]),_:2},1024)]),_:1}),a(h,{label:"场库/停车场",align:"center",prop:"warehouseName",width:"200"},{default:l(e=>[z("div",Ge,[a(S,{type:ce(e.row),effect:"light",size:"small",title:e.row.warehouseName},{default:l(()=>[z("i",{class:Re(me(e.row)),style:{"margin-right":"4px"}},null,2),c(" "+U(fe(e.row.warehouseName,8)),1)]),_:2},1032,["type","title"])])]),_:1}),a(h,{label:"套餐类型",align:"center",prop:"packageType"},{default:l(e=>[a(Q,{options:n(R),value:e.row.packageType},null,8,["options","value"])]),_:1}),a(h,{label:"会员类型",align:"center",prop:"vipType"},{default:l(e=>[a(Q,{options:n(q),value:e.row.vipType},null,8,["options","value"])]),_:1}),a(h,{label:"套餐价格",align:"center",prop:"packagePrice",width:"120"},{default:l(e=>[z("span",He,"¥"+U(e.row.packagePrice||"0.00"),1)]),_:1}),a(h,{label:"会员时长",align:"center",prop:"packageType",width:"100"},{default:l(e=>[e.row.packageType===0?(p(),i(S,{key:0,type:"info",effect:"light",size:"small"},{default:l(()=>[c(" 自定义 ")]),_:1})):e.row.packageType===30?(p(),i(S,{key:1,type:"warning",effect:"light",size:"small"},{default:l(()=>[c(" 包月 ")]),_:1})):e.row.packageType===365?(p(),i(S,{key:2,type:"success",effect:"light",size:"small"},{default:l(()=>[c(" 包年 ")]),_:1})):(p(),i(S,{key:3,type:"primary",effect:"light",size:"small"},{default:l(()=>[c(U(e.row.packageType)+"天 ",1)]),_:2},1024))]),_:1}),a(h,{label:"启用状态",align:"center",prop:"packageStatus",width:"120"},{default:l(e=>[a(Q,{options:n(L),value:e.row.packageStatus},null,8,["options","value"])]),_:1}),a(h,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(e=>[V((p(),i(y,{link:"",type:"primary",icon:"Edit",onClick:Ce=>Y(e.row)},{default:l(()=>[c("修改")]),_:2},1032,["onClick"])),[[I,["vip:package:edit"]]]),V((p(),i(y,{link:"",type:"primary",icon:"Delete",onClick:Ce=>Z(e.row)},{default:l(()=>[c("删除")]),_:2},1032,["onClick"])),[[I,["vip:package:remove"]]])]),_:1})]),_:1},8,["data"])),[[Pe,n(O)]]),a(re,{total:n(G),"current-page":n(d).pageNum,"onUpdate:currentPage":t[5]||(t[5]=e=>n(d).pageNum=e),"page-size":n(d).pageSize,"onUpdate:pageSize":t[6]||(t[6]=e=>n(d).pageSize=e),onPagination:T},null,8,["total","current-page","page-size"]),a(Ie,{title:n(B),modelValue:n(w),"onUpdate:modelValue":t[15]||(t[15]=e=>ne(w)?w.value=e:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[z("div",Je,[a(y,{type:"primary",onClick:he},{default:l(()=>[c("确 定")]),_:1}),a(y,{onClick:ge},{default:l(()=>[c("取 消")]),_:1})])]),default:l(()=>[a(ae,{ref:"packageRef",model:n(u),rules:n(ie),"label-width":"100px"},{default:l(()=>[a(b,null,{default:l(()=>[a(_,{span:24},{default:l(()=>[a(r,{label:"套餐名称",prop:"packageName"},{default:l(()=>[a(m,{modelValue:n(u).packageName,"onUpdate:modelValue":t[7]||(t[7]=e=>n(u).packageName=e),placeholder:"请输入套餐名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(b,null,{default:l(()=>[a(_,{span:24},{default:l(()=>[a(r,{label:"场库/停车场",prop:"warehouseId"},{default:l(()=>[a(ee,{ref_key:"cascaderRef",ref:pe,modelValue:n(u).warehouseId,"onUpdate:modelValue":t[8]||(t[8]=e=>n(u).warehouseId=e),options:n(C),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"100%"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1})]),_:1})]),_:1}),a(b,null,{default:l(()=>[a(_,{span:12},{default:l(()=>[a(r,{label:"套餐类型",prop:"packageType"},{default:l(()=>[a(g,{modelValue:n(u).packageType,"onUpdate:modelValue":t[9]||(t[9]=e=>n(u).packageType=e),placeholder:"请选择套餐类型",onChange:ye},{default:l(()=>[(p(!0),P(x,null,D(n(R),e=>(p(),i(f,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(b,null,{default:l(()=>[a(_,{span:12},{default:l(()=>[a(r,{label:"会员类型",prop:"vipType"},{default:l(()=>[a(g,{modelValue:n(u).vipType,"onUpdate:modelValue":t[10]||(t[10]=e=>n(u).vipType=e),placeholder:"请选择会员类型"},{default:l(()=>[(p(!0),P(x,null,D(n(q),e=>(p(),i(f,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(u).packageType===0?(p(),i(b,{key:0},{default:l(()=>[a(_,{span:12},{default:l(()=>[a(r,{label:"自定义天数",prop:"customDays"},{default:l(()=>[a(le,{modelValue:n(u).customDays,"onUpdate:modelValue":t[11]||(t[11]=e=>n(u).customDays=e),min:1,placeholder:"请输入自定义天数"},null,8,["modelValue"])]),_:1})]),_:1}),a(_,{span:12},{default:l(()=>[a(r,{label:"会员时长"},{default:l(()=>[a(m,{value:n(u).customDays?n(u).customDays+"天":"",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})):n(u).packageType?(p(),i(b,{key:1},{default:l(()=>[a(_,{span:12},{default:l(()=>[a(r,{label:"会员时长"},{default:l(()=>[a(m,{value:n(u).packageType+"天",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})):qe("",!0),a(b,null,{default:l(()=>[a(_,{span:12},{default:l(()=>[a(r,{label:"套餐价格",prop:"packagePrice"},{default:l(()=>[a(le,{modelValue:n(u).packagePrice,"onUpdate:modelValue":t[12]||(t[12]=e=>n(u).packagePrice=e),min:0,precision:2,placeholder:"请输入套餐价格"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(b,null,{default:l(()=>[a(_,{span:12},{default:l(()=>[a(r,{label:"套餐状态",prop:"packageStatus"},{default:l(()=>[a(Se,{modelValue:n(u).packageStatus,"onUpdate:modelValue":t[13]||(t[13]=e=>n(u).packageStatus=e)},{default:l(()=>[(p(!0),P(x,null,D(n(L),e=>(p(),i(Te,{key:e.value,value:parseInt(e.value)},{default:l(()=>[c(U(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(r,{label:"备注",prop:"remark"},{default:l(()=>[a(m,{modelValue:n(u).remark,"onUpdate:modelValue":t[14]||(t[14]=e=>n(u).remark=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),la=Ne(Ye,[["__scopeId","data-v-e73ad198"]]);export{la as default};
