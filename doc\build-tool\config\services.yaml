# 停车场管理系统微服务配置
services:
  backend:
    lgjy-auth:
      name: "认证中心"
      path: "lgjy-auth"
      port: 9200
      dockerfile: "lgjy-auth/Dockerfile"
      build_context: "."
      dependencies: ["lgjy-common", "lgjy-api"]
      
    lgjy-gateway:
      name: "API网关"
      path: "lgjy-gateway"
      port: 8080
      dockerfile: "lgjy-gateway/Dockerfile"
      build_context: "."
      dependencies: ["lgjy-common", "lgjy-api"]
      
    lgjy-wx-auth:
      name: "微信认证服务"
      path: "lgjy-wx-auth"
      port: 9202
      dockerfile: "lgjy-wx-auth/Dockerfile"
      build_context: "."
      dependencies: ["lgjy-common", "lgjy-api"]
      
    lgjy-system:
      name: "系统管理服务"
      path: "lgjy-modules/lgjy-system"
      port: 9201
      dockerfile: "lgjy-modules/lgjy-system/Dockerfile"
      build_context: "."
      dependencies: ["lgjy-common", "lgjy-api"]
      
    lgjy-file:
      name: "文件服务"
      path: "lgjy-modules/lgjy-file"
      port: 9203
      dockerfile: "lgjy-modules/lgjy-file/Dockerfile"
      build_context: "."
      dependencies: ["lgjy-common", "lgjy-api"]
      
    lgjy-gate:
      name: "道闸服务"
      path: "lgjy-modules/lgjy-gate"
      port: 9204
      dockerfile: "lgjy-modules/lgjy-gate/Dockerfile"
      build_context: "."
      dependencies: ["lgjy-common", "lgjy-api"]
      
    lgjy-wx:
      name: "微信服务"
      path: "lgjy-modules/lgjy-wx"
      port: 9205
      dockerfile: "lgjy-modules/lgjy-wx/Dockerfile"
      build_context: "."
      dependencies: ["lgjy-common", "lgjy-api"]

  frontend:
    park-ui:
      name: "管理员前端"
      path: "park-ui"
      build_command: "npm run build:prod"
      dist_path: "park-ui/dist"
      dockerfile: "park-ui/Dockerfile"
      
    park-uniapp:
      name: "小程序端"
      path: "park-uniapp"
      build_command: "npm run build:mp-weixin"
      dist_path: "park-uniapp/unpackage/dist/build/mp-weixin"

# 构建配置
build_config:
  environments:
    dev:
      maven_profiles: "dev"
      docker_registry: "localhost:5000"
      skip_tests: true
    test:
      maven_profiles: "test"
      docker_registry: "test-registry.com"
      skip_tests: false
    prod:
      maven_profiles: "prod"
      docker_registry: "prod-registry.com"
      skip_tests: false
      
  options:
    parallel_build: true
    max_parallel: 4
    cleanup_after_build: true
    docker_build_args:
      - "--no-cache"
    maven_args:
      - "-T 1C"  # 并行构建
      - "-q"     # 安静模式
