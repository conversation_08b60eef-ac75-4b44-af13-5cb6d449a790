import{v as N,O as be,d as we,r as c,C as ve,T as _e,x as ye,e as m,Q as F,c as Te,o as _,R as w,f as l,S as L,l as t,h as n,m as P,n as g,j as T,P as j,i as q,t as H,D as G}from"./index-D_FV2sri.js";function Ve(f){return N({url:"/system/owner/whitelist/list",method:"get",params:f})}function Ne(f){return N({url:"/system/owner/whitelist/"+f,method:"get"})}function ke(f){return N({url:"/system/owner/whitelist",method:"post",data:f})}function Ie(f){return N({url:"/system/owner/whitelist",method:"put",data:f})}function De(f){return N({url:"/system/owner/whitelist/"+f,method:"delete"})}function J(){return N({url:"/system/owner/whitelist/warehouseOptions",method:"get"})}const Se={class:"app-container"},xe={class:"dialog-footer"},Ce=be({name:"Whitelist"}),Ye=Object.assign(Ce,{setup(f){const{proxy:b}=we(),M=c([]),$=c([]),I=c([]),v=c(!1),x=c(!0),D=c(!0),C=c([]),O=c(!0),R=c(!0),U=c(0),Y=c(""),X=ve({form:{},queryParams:{pageNum:1,pageSize:10,plateNo:null,name:null,phoneNumber:null,warehouseId:null},rules:{plateNo:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],name:[{required:!0,message:"姓名不能为空",trigger:"blur"}],phoneNumber:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],warehouseId:[{required:!0,message:"场库不能为空",trigger:"change"}],beginTime:[{required:!0,message:"开始时间不能为空",trigger:"blur"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"blur"}]}}),{queryParams:s,form:o,rules:Z}=_e(X);function y(){x.value=!0,Ve(s.value).then(r=>{M.value=r.rows,U.value=r.total,x.value=!1})}function E(){J().then(r=>{$.value=r.data,I.value=B(r.data)})}function B(r){if(!r||r.length===0)return[];const e=r.filter(i=>i.parentId==="0"),u=r.filter(i=>i.parentId!=="0");return e.map(i=>{const p=u.filter(d=>d.parentId===i.id).map(d=>({value:d.id,label:d.warehouseName,isLeaf:!0}));return{value:i.id,label:i.warehouseName,children:p.length>0?p:void 0}})}function ee(r){const e=r.parentId;return e===0||e==="0"||e===null||e===void 0}function le(r,e){if(!r||!e||e.length===0)return null;const u=String(r),i=e.find(d=>String(d.id)===u);if(!i)return null;if(ee(i))return[i.id];const p=e.find(d=>String(d.id)===String(i.parentId));return p?[p.id,i.id]:[i.id]}function te(){v.value=!1,W()}function W(){o.value={id:null,plateNo:null,name:null,phoneNumber:null,warehouseId:null,beginTime:null,endTime:null,remark:null},b.resetForm("whitelistRef")}function k(){s.value.pageNum=1,y()}function ae(){b.resetForm("queryRef"),k()}function ne(r){C.value=r.map(e=>e.id),O.value=r.length!=1,R.value=!r.length}function oe(){W(),E(),v.value=!0,Y.value="添加白名单管理"}function K(r){W();const e=r.id||C.value;Promise.all([J(),Ne(e)]).then(([u,i])=>{$.value=u.data,I.value=B(u.data),v.value=!0,Y.value="修改白名单管理",G(()=>{o.value=i.data,o.value.beginTime&&(o.value.beginTime=o.value.beginTime.split(" ")[0]),o.value.endTime&&(o.value.endTime=o.value.endTime.split(" ")[0]),G(()=>{if(o.value.warehouseId){console.log("白名单修改 - 原始warehouseId:",o.value.warehouseId),console.log("白名单修改 - 场库选项数据:",u.data);const p=le(o.value.warehouseId,u.data);console.log("白名单修改 - 构建的路径:",p),p&&p.length>0&&(o.value.warehouseId=p,console.log("白名单修改 - 设置后的warehouseId:",o.value.warehouseId))}})})}).catch(u=>{console.error("加载数据失败:",u),b.$modal.msgError("加载数据失败")})}function re(){b.$refs.whitelistRef.validate(r=>{if(r){const e={...o.value};Array.isArray(e.warehouseId)&&(e.warehouseId=e.warehouseId[e.warehouseId.length-1]),e.beginTime&&(e.beginTime=e.beginTime+" 00:00:00"),e.endTime&&(e.endTime=e.endTime+" 23:59:59"),o.value.id!=null?Ie(e).then(u=>{b.$modal.msgSuccess("修改成功"),v.value=!1,y()}):ke(e).then(u=>{b.$modal.msgSuccess("新增成功"),v.value=!1,y()})}})}function Q(r){const e=r.id||C.value;b.$modal.confirm('是否确认删除白名单管理编号为"'+e+'"的数据项？').then(function(){return De(e)}).then(()=>{y(),b.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ie(){b.download("system/owner/whitelist/export",{...s.value},`whitelist_${new Date().getTime()}.xlsx`)}function ue(r){const e=new Date;return e.setHours(0,0,0,0),r.getTime()<e.getTime()}function de(r){if(!o.value.beginTime)return!1;const e=new Date(o.value.beginTime);return r.getTime()<e.getTime()}return ye(()=>{y(),E()}),(r,e)=>{const u=m("el-input"),i=m("el-form-item"),p=m("el-cascader"),d=m("el-button"),z=m("el-form"),S=m("el-col"),se=m("right-toolbar"),pe=m("el-row"),h=m("el-table-column"),me=m("el-table"),ce=m("pagination"),A=m("el-date-picker"),fe=m("el-dialog"),V=F("hasPermi"),he=F("loading");return _(),Te("div",Se,[w(l(z,{model:t(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[l(i,{label:"车牌号",prop:"plateNo"},{default:n(()=>[l(u,{modelValue:t(s).plateNo,"onUpdate:modelValue":e[0]||(e[0]=a=>t(s).plateNo=a),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:P(k,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"姓名",prop:"name"},{default:n(()=>[l(u,{modelValue:t(s).name,"onUpdate:modelValue":e[1]||(e[1]=a=>t(s).name=a),placeholder:"请输入姓名",clearable:"",style:{width:"200px"},onKeyup:P(k,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"手机号",prop:"phoneNumber"},{default:n(()=>[l(u,{modelValue:t(s).phoneNumber,"onUpdate:modelValue":e[2]||(e[2]=a=>t(s).phoneNumber=a),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:P(k,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"场库/停车场",prop:"warehouseId"},{default:n(()=>[l(p,{modelValue:t(s).warehouseId,"onUpdate:modelValue":e[3]||(e[3]=a=>t(s).warehouseId=a),options:t(I),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"200px"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),l(i,null,{default:n(()=>[l(d,{type:"primary",icon:"Search",onClick:k},{default:n(()=>[g("搜索")]),_:1}),l(d,{icon:"Refresh",onClick:ae},{default:n(()=>[g("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[L,t(D)]]),l(pe,{gutter:10,class:"mb8"},{default:n(()=>[l(S,{span:1.5},{default:n(()=>[w((_(),T(d,{type:"primary",plain:"",icon:"Plus",onClick:oe},{default:n(()=>[g("新增")]),_:1})),[[V,["owner:whitelist:add"]]])]),_:1}),l(S,{span:1.5},{default:n(()=>[w((_(),T(d,{type:"success",plain:"",icon:"Edit",disabled:t(O),onClick:K},{default:n(()=>[g("修改")]),_:1},8,["disabled"])),[[V,["owner:whitelist:edit"]]])]),_:1}),l(S,{span:1.5},{default:n(()=>[w((_(),T(d,{type:"danger",plain:"",icon:"Delete",disabled:t(R),onClick:Q},{default:n(()=>[g("删除")]),_:1},8,["disabled"])),[[V,["owner:whitelist:remove"]]])]),_:1}),l(S,{span:1.5},{default:n(()=>[w((_(),T(d,{type:"warning",plain:"",icon:"Download",onClick:ie},{default:n(()=>[g("导出")]),_:1})),[[V,["owner:whitelist:export"]]])]),_:1}),l(se,{showSearch:t(D),"onUpdate:showSearch":e[4]||(e[4]=a=>j(D)?D.value=a:null),onQueryTable:y},null,8,["showSearch"])]),_:1}),w((_(),T(me,{data:t(M),onSelectionChange:ne},{default:n(()=>[l(h,{type:"selection",width:"55",align:"center"}),l(h,{label:"车牌号",align:"center",prop:"plateNo"}),l(h,{label:"姓名",align:"center",prop:"name"}),l(h,{label:"手机号",align:"center",prop:"phoneNumber"}),l(h,{label:"场库名称",align:"center",prop:"warehouseName"}),l(h,{label:"白名单类型",align:"center",prop:"whiteType"}),l(h,{label:"开始时间",align:"center",prop:"beginTime",width:"180"},{default:n(a=>[q("span",null,H(r.parseTime(a.row.beginTime,"{y}-{m}-{d}")),1)]),_:1}),l(h,{label:"结束时间",align:"center",prop:"endTime",width:"180"},{default:n(a=>[q("span",null,H(r.parseTime(a.row.endTime,"{y}-{m}-{d}"))+" 23:59:59",1)]),_:1}),l(h,{label:"备注",align:"center",prop:"remark"}),l(h,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(a=>[w((_(),T(d,{link:"",type:"primary",icon:"Edit",onClick:ge=>K(a.row)},{default:n(()=>[g("修改")]),_:2},1032,["onClick"])),[[V,["owner:whitelist:edit"]]]),w((_(),T(d,{link:"",type:"primary",icon:"Delete",onClick:ge=>Q(a.row)},{default:n(()=>[g("删除")]),_:2},1032,["onClick"])),[[V,["owner:whitelist:remove"]]])]),_:1})]),_:1},8,["data"])),[[he,t(x)]]),w(l(ce,{total:t(U),page:t(s).pageNum,"onUpdate:page":e[5]||(e[5]=a=>t(s).pageNum=a),limit:t(s).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>t(s).pageSize=a),onPagination:y},null,8,["total","page","limit"]),[[L,t(U)>0]]),l(fe,{title:t(Y),modelValue:t(v),"onUpdate:modelValue":e[14]||(e[14]=a=>j(v)?v.value=a:null),width:"500px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:n(()=>[q("div",xe,[l(d,{type:"primary",onClick:re},{default:n(()=>[g("确 定")]),_:1}),l(d,{onClick:te},{default:n(()=>[g("取 消")]),_:1})])]),default:n(()=>[l(z,{ref:"whitelistRef",model:t(o),rules:t(Z),"label-width":"80px"},{default:n(()=>[l(i,{label:"车牌号",prop:"plateNo"},{default:n(()=>[l(u,{modelValue:t(o).plateNo,"onUpdate:modelValue":e[7]||(e[7]=a=>t(o).plateNo=a),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1}),l(i,{label:"姓名",prop:"name"},{default:n(()=>[l(u,{modelValue:t(o).name,"onUpdate:modelValue":e[8]||(e[8]=a=>t(o).name=a),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),l(i,{label:"手机号",prop:"phoneNumber"},{default:n(()=>[l(u,{modelValue:t(o).phoneNumber,"onUpdate:modelValue":e[9]||(e[9]=a=>t(o).phoneNumber=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),l(i,{label:"场库/停车场",prop:"warehouseId"},{default:n(()=>[l(p,{ref:"cascaderRef",modelValue:t(o).warehouseId,"onUpdate:modelValue":e[10]||(e[10]=a=>t(o).warehouseId=a),options:t(I),props:{value:"value",label:"label",children:"children",emitPath:!1,checkStrictly:!0,expandTrigger:"hover"},placeholder:"请选择场库或停车场",style:{width:"100%"},clearable:"",filterable:"","show-all-levels":!1},null,8,["modelValue","options"])]),_:1}),l(i,{label:"开始时间",prop:"beginTime"},{default:n(()=>[l(A,{modelValue:t(o).beginTime,"onUpdate:modelValue":e[11]||(e[11]=a=>t(o).beginTime=a),type:"date",placeholder:"选择开始日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":ue,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(i,{label:"结束时间",prop:"endTime"},{default:n(()=>[l(A,{modelValue:t(o).endTime,"onUpdate:modelValue":e[12]||(e[12]=a=>t(o).endTime=a),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":de,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(i,{label:"备注",prop:"remark"},{default:n(()=>[l(u,{modelValue:t(o).remark,"onUpdate:modelValue":e[13]||(e[13]=a=>t(o).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ye as default};
