#!/bin/bash

# 停车管理系统回滚脚本
# 作者: DevOps Team
# 版本: 1.0

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 解析命令行参数
ROLLBACK_ENV="test"
BACKUP_NAME=""
ROLLBACK_SERVICES="all"
CONFIRM_ROLLBACK="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --env=*)
            ROLLBACK_ENV="${1#*=}"
            shift
            ;;
        --backup=*)
            BACKUP_NAME="${1#*=}"
            shift
            ;;
        --services=*)
            ROLLBACK_SERVICES="${1#*=}"
            shift
            ;;
        --confirm)
            CONFIRM_ROLLBACK="true"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --env=ENV            回滚环境 (test|prod) 默认: test"
            echo "  --backup=NAME        指定备份名称"
            echo "  --services=SERVICES  回滚服务 (all|backend|frontend|service_name) 默认: all"
            echo "  --confirm            确认回滚操作"
            echo "  -h, --help           显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

# 列出可用备份
list_available_backups() {
    log "INFO" "可用备份列表:"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log "WARN" "备份目录不存在: $BACKUP_DIR"
        return 1
    fi
    
    local backups=($(find "$BACKUP_DIR" -maxdepth 1 -type d -name "backup-*" | sort -r))
    
    if [ ${#backups[@]} -eq 0 ]; then
        log "WARN" "没有找到可用备份"
        return 1
    fi
    
    echo "序号  备份名称                           创建时间              大小"
    echo "----  --------------------------------  ------------------  --------"
    
    local index=1
    for backup in "${backups[@]}"; do
        local backup_name=$(basename "$backup")
        local backup_time=$(stat -c %y "$backup" | cut -d' ' -f1,2 | cut -d'.' -f1)
        local backup_size=$(du -sh "$backup" 2>/dev/null | cut -f1 || echo "未知")
        
        printf "%-4d  %-32s  %-18s  %s\n" "$index" "$backup_name" "$backup_time" "$backup_size"
        ((index++))
    done
    
    return 0
}

# 选择备份
select_backup() {
    if [ -n "$BACKUP_NAME" ]; then
        local backup_path="$BACKUP_DIR/$BACKUP_NAME"
        if [ -d "$backup_path" ]; then
            echo "$backup_path"
            return 0
        else
            log "ERROR" "指定的备份不存在: $BACKUP_NAME"
            return 1
        fi
    fi
    
    # 自动选择最新备份
    local latest_backup=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "backup-*" | sort -r | head -1)
    
    if [ -z "$latest_backup" ]; then
        log "ERROR" "没有找到可用备份"
        return 1
    fi
    
    log "INFO" "自动选择最新备份: $(basename "$latest_backup")"
    echo "$latest_backup"
    return 0
}

# 验证备份完整性
validate_backup() {
    local backup_path="$1"
    
    log "INFO" "验证备份完整性: $(basename "$backup_path")"
    
    # 检查备份清单
    local manifest_file="$backup_path/backup-manifest.json"
    if [ ! -f "$manifest_file" ]; then
        log "ERROR" "备份清单文件不存在: $manifest_file"
        return 1
    fi
    
    # 检查必要目录
    local required_dirs=("config" "data")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$backup_path/$dir" ]; then
            log "ERROR" "备份目录不完整，缺少: $dir"
            return 1
        fi
    done
    
    # 检查关键配置文件
    if [ ! -f "$backup_path/config/docker-compose.yml" ]; then
        log "ERROR" "Docker Compose配置文件不存在"
        return 1
    fi
    
    log "INFO" "备份完整性验证通过"
    return 0
}

# 确认回滚操作
confirm_rollback_operation() {
    local backup_path="$1"
    local backup_name=$(basename "$backup_path")
    
    if [ "$CONFIRM_ROLLBACK" = "true" ]; then
        return 0
    fi
    
    echo ""
    echo "=== 回滚确认 ==="
    echo "环境: $ROLLBACK_ENV"
    echo "备份: $backup_name"
    echo "服务: $ROLLBACK_SERVICES"
    echo ""
    
    # 显示备份信息
    if [ -f "$backup_path/backup-manifest.json" ]; then
        echo "备份信息:"
        cat "$backup_path/backup-manifest.json" | grep -E '"backup_name"|"timestamp"|"environment"|"git_commit"' | sed 's/^/  /'
        echo ""
    fi
    
    echo "警告: 回滚操作将会："
    echo "1. 停止当前运行的服务"
    echo "2. 恢复配置文件和数据"
    echo "3. 重启服务"
    echo "4. 可能导致数据丢失"
    echo ""
    
    read -p "确认执行回滚操作? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log "INFO" "回滚操作已取消"
        exit 0
    fi
    
    return 0
}

# 停止服务
stop_services_for_rollback() {
    log "INFO" "停止服务进行回滚..."
    
    cd "$DEPLOY_DIR"
    
    case "$ROLLBACK_SERVICES" in
        "all")
            docker-compose down
            ;;
        "backend")
            local backend_services=($(get_services_by_group "backend"))
            for service in "${backend_services[@]}"; do
                local container_name="park-${service,,}"
                docker-compose stop "$container_name" || true
            done
            ;;
        "frontend")
            docker-compose stop park-nginx || true
            ;;
        *)
            local container_name="park-${ROLLBACK_SERVICES,,}"
            docker-compose stop "$container_name" || true
            ;;
    esac
    
    log "INFO" "服务停止完成"
}

# 恢复配置文件
restore_config() {
    local backup_path="$1"
    
    log "INFO" "恢复配置文件..."
    
    # 备份当前配置
    local current_backup_dir="$BACKUP_DIR/rollback-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$current_backup_dir"
    
    # 恢复Docker Compose配置
    if [ -f "$backup_path/config/docker-compose.yml" ]; then
        cp "$DOCKER_COMPOSE_FILE" "$current_backup_dir/" 2>/dev/null || true
        cp "$backup_path/config/docker-compose.yml" "$DOCKER_COMPOSE_FILE"
        log "INFO" "已恢复Docker Compose配置"
    fi
    
    # 恢复Nginx配置
    if [ -d "$backup_path/config/nginx" ]; then
        local nginx_config_dir="$DEPLOY_DIR/nginx"
        if [ -d "$nginx_config_dir" ]; then
            cp -r "$nginx_config_dir" "$current_backup_dir/" 2>/dev/null || true
        fi
        cp -r "$backup_path/config/nginx" "$DEPLOY_DIR/"
        log "INFO" "已恢复Nginx配置"
    fi
    
    # 恢复SSL证书
    if [ -d "$backup_path/config/ssl" ]; then
        local ssl_dir="/opt/park/ssl"
        if [ -d "$ssl_dir" ]; then
            cp -r "$ssl_dir" "$current_backup_dir/" 2>/dev/null || true
        fi
        cp -r "$backup_path/config/ssl" "/opt/park/"
        log "INFO" "已恢复SSL证书"
    fi
    
    log "INFO" "当前配置已备份到: $current_backup_dir"
    log "INFO" "配置文件恢复完成"
}

# 恢复数据库
restore_database() {
    local backup_path="$1"
    
    log "INFO" "恢复数据库..."
    
    local db_backup_file="$backup_path/data/database-backup.sql.gz"
    
    if [ ! -f "$db_backup_file" ]; then
        log "WARN" "数据库备份文件不存在，跳过数据库恢复"
        return 0
    fi
    
    # 检查MySQL容器是否运行
    if ! docker ps --format "{{.Names}}" | grep -q "^park-mysql$"; then
        log "INFO" "启动MySQL容器..."
        cd "$DEPLOY_DIR"
        docker-compose up -d mysql
        
        # 等待MySQL启动
        sleep 30
    fi
    
    # 恢复数据库
    log "WARN" "正在恢复数据库，这将覆盖现有数据..."
    
    if zcat "$db_backup_file" | docker exec -i park-mysql mysql \
        -u "$TEST_MYSQL_USER" \
        -p"$TEST_MYSQL_PASSWORD"; then
        log "INFO" "数据库恢复完成"
    else
        log "ERROR" "数据库恢复失败"
        return 1
    fi
}

# 恢复Redis数据
restore_redis() {
    local backup_path="$1"
    
    log "INFO" "恢复Redis数据..."
    
    local redis_backup_file="$backup_path/data/redis/dump.rdb"
    
    if [ ! -f "$redis_backup_file" ]; then
        log "WARN" "Redis备份文件不存在，跳过Redis恢复"
        return 0
    fi
    
    # 停止Redis容器
    cd "$DEPLOY_DIR"
    docker-compose stop redis || true
    
    # 恢复Redis数据文件
    docker cp "$redis_backup_file" park-redis:/data/dump.rdb
    
    # 启动Redis容器
    docker-compose up -d redis
    
    log "INFO" "Redis数据恢复完成"
}

# 启动服务
start_services_after_rollback() {
    log "INFO" "启动服务..."
    
    cd "$DEPLOY_DIR"
    
    case "$ROLLBACK_SERVICES" in
        "all")
            docker-compose up -d
            ;;
        "backend")
            # 按依赖顺序启动后端服务
            local backend_services=($(get_services_by_group "backend"))
            local sorted_services=($(sort_services_by_order "${backend_services[@]}"))
            
            for service in "${sorted_services[@]}"; do
                local container_name="park-${service,,}"
                docker-compose up -d "$container_name"
                sleep 10
            done
            ;;
        "frontend")
            docker-compose up -d park-nginx
            ;;
        *)
            local container_name="park-${ROLLBACK_SERVICES,,}"
            docker-compose up -d "$container_name"
            ;;
    esac
    
    log "INFO" "服务启动完成"
}

# 验证回滚结果
validate_rollback() {
    log "INFO" "验证回滚结果..."
    
    # 等待服务启动
    sleep 60
    
    # 执行健康检查
    if "$SCRIPTS_ROOT/deploy/health-check.sh" --env="$ROLLBACK_ENV"; then
        log "INFO" "回滚后健康检查通过"
        return 0
    else
        log "ERROR" "回滚后健康检查失败"
        return 1
    fi
}

# 主回滚函数
main() {
    log "INFO" "开始回滚操作..."
    log "INFO" "回滚环境: $ROLLBACK_ENV"
    log "INFO" "回滚服务: $ROLLBACK_SERVICES"
    
    # 列出可用备份
    if ! list_available_backups; then
        return 1
    fi
    
    # 选择备份
    local backup_path
    if ! backup_path=$(select_backup); then
        return 1
    fi
    
    # 验证备份完整性
    if ! validate_backup "$backup_path"; then
        return 1
    fi
    
    # 确认回滚操作
    confirm_rollback_operation "$backup_path"
    
    log "INFO" "开始执行回滚..."
    
    # 停止服务
    stop_services_for_rollback
    
    # 恢复配置文件
    restore_config "$backup_path"
    
    # 恢复数据（如果需要）
    if [ "$ROLLBACK_SERVICES" = "all" ] || [ "$ROLLBACK_SERVICES" = "backend" ]; then
        restore_database "$backup_path"
        restore_redis "$backup_path"
    fi
    
    # 启动服务
    start_services_after_rollback
    
    # 验证回滚结果
    if validate_rollback; then
        log "INFO" "回滚操作成功完成！"
        log "INFO" "使用的备份: $(basename "$backup_path")"
        return 0
    else
        log "ERROR" "回滚操作失败"
        return 1
    fi
}

# 执行主函数
main "$@"
