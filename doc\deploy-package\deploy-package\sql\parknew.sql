/*
 Navicat MySQL Data Transfer

 Source Server         : parkingnew
 Source Server Type    : MySQL
 Source Server Version : 80400
 Source Host           : localhost:3306
 Source Schema         : parknew

 Target Server Type    : MySQL
 Target Server Version : 80400
 File Encoding         : 65001

 Date: 17/07/2025 14:04:18
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for error_data_log
-- ----------------------------
DROP TABLE IF EXISTS `error_data_log`;
CREATE TABLE `error_data_log`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'id',
  `errCode` int NULL DEFAULT NULL COMMENT '错误码（0未识别车牌，1入场时，无出场记录，2出场时无入场记录，3收费异常 ',
  `parkingId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '场库id',
  `plateNum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号',
  `inTime` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '入场时间',
  `inChannelName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '入场通道名称',
  `outTime` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出场时间',
  `outChannelName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出场通道名称',
  `money` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '钱',
  `imgPath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片（入场，出场）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `lastUpdate` datetime NULL DEFAULT NULL COMMENT '最后一次更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of error_data_log
-- ----------------------------
INSERT INTO `error_data_log` VALUES ('EDL001', 0, '723055983177371648', '无法识别', '1703145600', '北门入口', NULL, NULL, '0.00', '/images/error/unrecognized_plate_001.jpg', '车牌污损严重，无法识别', '2025-01-15 08:30:15');
INSERT INTO `error_data_log` VALUES ('EDL002', 0, '1871825000000001001', '模糊车牌', '1703232000', '南门入口', NULL, NULL, '0.00', '/images/error/unrecognized_plate_002.jpg', '雨天车牌模糊', '2025-01-15 10:45:22');
INSERT INTO `error_data_log` VALUES ('EDL004', 1, '723055983177371648', '沪A12345', '1703145600', '北门入口', NULL, NULL, '15.00', '/images/error/no_exit_record_001.jpg', '车辆重复入场，未找到上次出场记录', '2025-01-15 09:15:45');
INSERT INTO `error_data_log` VALUES ('EDL005', 1, '1871825000000001001', '京B67890', '1703232000', '西门入口', NULL, NULL, '20.00', '/images/error/no_exit_record_002.jpg', '系统故障导致出场记录丢失', '2025-01-15 11:30:12');
INSERT INTO `error_data_log` VALUES ('EDL006', 1, '1871825000000001005', '粤C11111', '1703318400', '南门入口', NULL, NULL, '12.50', '/images/error/no_exit_record_003.jpg', '道闸系统重启后数据丢失', '2025-01-15 15:45:28');
INSERT INTO `error_data_log` VALUES ('EDL007', 1, '723055983177371648', '浙D22222', '1703404800', '北门入口', NULL, NULL, '18.00', '/images/error/no_exit_record_004.jpg', '车辆异常入场，疑似跟车进入', '2025-01-15 16:20:55');
INSERT INTO `error_data_log` VALUES ('EDL008', 2, '723055983177371648', '苏E33333', NULL, NULL, '1703491200', '北门出口', '25.00', '/images/error/no_entry_record_001.jpg', '车辆直接出场，未找到入场记录', '2025-01-15 12:45:18');
INSERT INTO `error_data_log` VALUES ('EDL009', 2, '1871825000000001001', '鲁F44444', NULL, NULL, '1703577600', '东门出口', '30.00', '/images/error/no_entry_record_002.jpg', '可能通过其他通道入场', '2025-01-15 13:20:35');
INSERT INTO `error_data_log` VALUES ('EDL010', 2, '1871825000000001005', '川G55555', NULL, NULL, '1703664000', '西门出口', '22.50', '/images/error/no_entry_record_003.jpg', '入场记录被误删', '2025-01-15 17:10:42');
INSERT INTO `error_data_log` VALUES ('EDL011', 2, '723055983177371648', '豫H66666', NULL, NULL, '1703750400', '南门出口', '35.00', '/images/error/no_entry_record_004.jpg', '系统维护期间入场记录丢失', '2025-01-15 18:30:25');
INSERT INTO `error_data_log` VALUES ('EDL012', 3, '723055983177371648', '湘I77777', '1703145600', '北门入口', '1703232000', '北门出口', '-5.00', '/images/error/payment_error_001.jpg', '计费系统异常，出现负数金额', '2025-01-15 14:15:30');
INSERT INTO `error_data_log` VALUES ('EDL013', 3, '1871825000000001001', '闽J88888', '1703232000', '西门入口', '1703318400', '西门出口', '999.99', '/images/error/payment_error_002.jpg', '计费异常，金额过高', '2025-01-15 15:45:18');
INSERT INTO `error_data_log` VALUES ('EDL014', 3, '1871825000000001005', '桂K99999', '1703318400', '东门入口', '1703404800', '东门出口', '0.01', '/images/error/payment_error_003.jpg', 'VIP用户计费错误', '2025-01-15 16:20:45');
INSERT INTO `error_data_log` VALUES ('EDL015', 3, '723055983177371648', '云L00000', '1703404800', '南门入口', '1703491200', '南门出口', '500.00', '/images/error/payment_error_004.jpg', '长时间停车费用计算错误', '2025-01-15 19:30:12');
INSERT INTO `error_data_log` VALUES ('EDL016', 0, '723055983177371648', '沪AD12345', '1703836800', '北门入口', NULL, NULL, '0.00', '/images/error/new_energy_plate_001.jpg', '新能源车牌识别失败', '2025-01-16 08:15:20');
INSERT INTO `error_data_log` VALUES ('EDL017', 1, '1871825000000001001', '京AF67890', '1703923200', '西门入口', NULL, NULL, '15.00', '/images/error/new_energy_entry_001.jpg', '新能源车重复入场', '2025-01-16 09:30:45');
INSERT INTO `error_data_log` VALUES ('EDL018', 2, '1871825000000001005', '粤AG11111', NULL, NULL, '1704009600', '东门出口', '20.00', '/images/error/new_energy_exit_001.jpg', '新能源车无入场记录出场', '2025-01-16 10:45:30');
INSERT INTO `error_data_log` VALUES ('EDL019', 1, '723055983177371648', '临A12345', '1704096000', '北门入口', NULL, NULL, '10.00', '/images/error/temp_car_001.jpg', '临时车牌重复入场', '2025-01-16 11:20:15');
INSERT INTO `error_data_log` VALUES ('EDL020', 3, '1871825000000001001', '临B67890', '1704182400', '南门入口', '1704268800', '南门出口', '1000.00', '/images/error/temp_car_payment_001.jpg', '临时车计费异常', '2025-01-16 12:35:40');
INSERT INTO `error_data_log` VALUES ('EDL021', 3, '723055983177371648', '赣C12345', '1737648000', '北门入口', '1737734400', '北门出口', '240.00', '/images/error/overnight_parking_001.jpg', '跨天停车费用异常', '2025-01-24 09:15:30');
INSERT INTO `error_data_log` VALUES ('EDL022', 1, '1871825000000001005', '黔D67890', '1737820800', '西门入口', NULL, NULL, '0.00', '/images/error/overnight_entry_001.jpg', '跨天车辆重复入场', '2025-01-24 14:20:50');
INSERT INTO `error_data_log` VALUES ('EDL023', 0, '723055983177371648', '系统故障', '1737993600', '北门入口', NULL, NULL, '0.00', '/images/error/system_error_001.jpg', '道闸系统重启导致识别异常', '2025-01-24 15:30:18');
INSERT INTO `error_data_log` VALUES ('EDL024', 2, '1871825000000001001', '蒙E11111', NULL, NULL, '1738080000', '东门出口', '45.00', '/images/error/system_error_002.jpg', '数据库连接异常导致记录丢失', '2025-01-24 16:45:33');
INSERT INTO `error_data_log` VALUES ('EDL025', 0, '1871825000000001005', '警A12345', '1738166400', '南门入口', NULL, NULL, '0.00', '/images/error/special_plate_001.jpg', '特殊车牌识别异常', '2025-01-24 17:20:45');
INSERT INTO `error_data_log` VALUES ('EDL026', 1, '723055983177371648', '赣A11111', '1738252800', '西门入口', NULL, NULL, '20.00', '/images/error/real_plate_001.jpg', '真实车牌重复入场异常', '2025-01-24 18:35:20');
INSERT INTO `error_data_log` VALUES ('EDL027', 3, '723055983177371648', '沪S67445', '1738339200', '北门入口', '1738425600', '北门出口', '88.88', '/images/error/real_plate_002.jpg', '真实车牌计费异常', '2025-01-24 19:15:30');
INSERT INTO `error_data_log` VALUES ('EDL028', 2, '723055983177371648', '沪AS1234', NULL, NULL, '1738512000', '东门出口', '25.00', '/images/error/real_plate_003.jpg', '真实车牌无入场记录', '2025-01-24 20:30:45');
INSERT INTO `error_data_log` VALUES ('EDL029', 0, '1871825000000001001', '无法识别3', '1738598400', '南门入口', NULL, NULL, '0.00', '/images/error/recent_unrecognized_001.jpg', '最新车牌识别异常', '2025-01-24 21:45:20');
INSERT INTO `error_data_log` VALUES ('EDL030', 1, '1871825000000001005', '川H12345', '1738684800', '西门入口', NULL, NULL, '30.00', '/images/error/recent_entry_001.jpg', '最新入场异常', '2025-01-24 22:20:15');

-- ----------------------------
-- Table structure for gate_car_info
-- ----------------------------
DROP TABLE IF EXISTS `gate_car_info`;
CREATE TABLE `gate_car_info`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parkingId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `plateNum` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `beginTime` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `endTime` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `userName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `lastUpdate` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gate_car_info
-- ----------------------------

-- ----------------------------
-- Table structure for gate_log
-- ----------------------------
DROP TABLE IF EXISTS `gate_log`;
CREATE TABLE `gate_log`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parkingName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称',
  `accessAddress` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求IP地址',
  `Address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '远程请求路径',
  `operate` int NULL DEFAULT NULL COMMENT '操作数',
  `parm` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参数',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求数据',
  `result` int NULL DEFAULT NULL COMMENT '结果值',
  `resultMsg` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '结果消息',
  `lastUpdate` datetime NULL DEFAULT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gate_log
-- ----------------------------
INSERT INTO `gate_log` VALUES ('0110b49310124c4e8fd11f6eb8ba7c52', '思卓', '**************', 'http://wxy.ittiger.club:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"5405714A2116D8A4BD08A12D00CE1473\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750829821230}', 0, '车场不存在', '2025-06-25 13:37:03');
INSERT INTO `gate_log` VALUES ('0280d6fe11c649658e18f50464f5e211', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"C38395F6B1682C7C1D355403F4CECBF3\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750836693659}', 1, '', '2025-06-25 15:31:34');
INSERT INTO `gate_log` VALUES ('059732bc02554e62b9f4e9807b61d7d1', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"AE67616B7629C787E8DD543427D7A93D\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750827519337}', 1, '', '2025-06-25 12:58:40');
INSERT INTO `gate_log` VALUES ('090fd3c44e864608b468319dc3eb2d3f', '思卓', '0.0.0.0', 'http://qr.it-wy.cn:83/transmit/vehicle/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"C1482D46120AD57840AE6B4049A29037\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750905789265}', 0, '未查询到车辆信息', '2025-06-26 10:43:14');
INSERT INTO `gate_log` VALUES ('0a77f9e90c4b45ac85cea49bac5843a9', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"0E93C0EF056DCD3B7AC1A20F2203499E\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750834410383}', 1, '', '2025-06-25 14:53:31');
INSERT INTO `gate_log` VALUES ('0aacf04ae535479b8c5ba2fe6cb071b4', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"B6E681143ECE40DFC525D81B9FEFD4F4\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750829197584}', 1, '', '2025-06-25 13:26:38');
INSERT INTO `gate_log` VALUES ('0e76341f24d041fa83c1bfeb38a8d105', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"3CFAC5141111B1F8D6A7ABAF8EB05930\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750832015542}', 1, '', '2025-06-25 14:13:36');
INSERT INTO `gate_log` VALUES ('113196b524874ad2b6234ed7cab01bd9', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤MX7749', '{\"sign\":\"125091362C24CAFD94E1B863D8764509\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤MX7749\",\"timestamp\":1750827632732}', 1, '', '2025-06-25 13:00:33');
INSERT INTO `gate_log` VALUES ('13b60de6b23042a698c227f414d2c2f7', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"63A6B6072253FB0EF1190780D2C4B4AD\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750904905853}', 1, '', '2025-06-26 10:28:26');
INSERT INTO `gate_log` VALUES ('19c09268d0b34907b5f83e78b8daac35', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"D98E2C3583D84E98E6590BD5A058FC6E\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750829347218}', 1, '', '2025-06-25 13:29:08');
INSERT INTO `gate_log` VALUES ('22f1ca7169844ca8afe0908c8f3a20af', '思卓', '0.0.0.0', 'http://qr.it-wy.cn:83/transmit/vehicle/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"DFE9E41A6CD7390668DE87BC13A87B56\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750906748678}', 0, '未查询到车辆信息', '2025-06-26 10:59:09');
INSERT INTO `gate_log` VALUES ('23f83191316b49cf9b27fd9b07bafb1a', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"2B96C1422BD9C234F95BD152A8F56890\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750837116612}', 1, '', '2025-06-25 15:38:37');
INSERT INTO `gate_log` VALUES ('30de3909b8f14e8f91f964554918168b', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"4EAAACA22CE2A854E58C739ACADBD8D7\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750901496338}', 1, '', '2025-06-26 09:31:37');
INSERT INTO `gate_log` VALUES ('30e3ca965fef4ec3b4af9805855333a6', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"057F44B2E6EE110AFA3355653A088288\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750829594838}', 1, '', '2025-06-25 13:33:15');
INSERT INTO `gate_log` VALUES ('32aa1b529b4844f58fd89178735654d0', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤MX7749', '{\"sign\":\"D0B355F5CEE7F3D9474984494A36FC39\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤MX7749\",\"timestamp\":1750826158341}', 1, '', '2025-06-25 12:35:59');
INSERT INTO `gate_log` VALUES ('3485e15cfdf5422ca9ef3b40f3d2c6a9', '思卓', '0.0.0.0', 'http://qr.it-wy.cn:83/transmit/vehicle/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"5EAC39CCC81B340B6F3EC6B511A8E182\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750906168650}', 0, '未查询到车辆信息', '2025-06-26 10:49:29');
INSERT INTO `gate_log` VALUES ('372855d5d7704a3b9367a42fe2b92ea1', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"4B111889E549DD8783C42DCC9CC073DF\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750837702020}', 1, '', '2025-06-25 15:48:22');
INSERT INTO `gate_log` VALUES ('376e1aaf80774c359dbf3b0da0eaae8b', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤MX7749', '{\"sign\":\"C0A22EEEC98C3938679C6EE12572C071\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤MX7749\",\"timestamp\":1750825301379}', 1, '', '2025-06-25 12:21:43');
INSERT INTO `gate_log` VALUES ('3a5d0aac6a944e3785b4ca47a5694148', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"BFF96000C002183F8A0004370D863FD9\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750834559514}', 1, '', '2025-06-25 14:56:00');
INSERT INTO `gate_log` VALUES ('3b1ea8c6486145b5a2fa29a7abb558bd', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"7C08A35E3D5C9962C6EAB9CEF9DFE112\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750831741063}', 1, '', '2025-06-25 14:09:55');
INSERT INTO `gate_log` VALUES ('42b1914d2a9f4c50a3ab04f5fe46827a', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"972ECCCFB0C2E3020416D6A74EEE4E15\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750830353173}', 1, '', '2025-06-25 13:45:53');
INSERT INTO `gate_log` VALUES ('42c2775d91654855bd38e600b0780cd5', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"CF2B316B31E9703809CF85B0E507BD53\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750827820842}', 1, '', '2025-06-25 13:03:41');
INSERT INTO `gate_log` VALUES ('44625a0ce47d4aa38585210d153c5480', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"5FDD1C683AD49F00F1CE3D6D151AAD23\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750830182611}', 0, '签名错误', '2025-06-25 13:43:03');
INSERT INTO `gate_log` VALUES ('47404585ff7a46079c9380c39318cad6', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/pay/notice', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"amount\":100,\"payType\":2,\"tradeNo\":\"37Y120250626100309281065388P\",\"sign\":\"09BEED7990CA353A31DCE8CD546D30B7\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750904845502}', 1, '', '2025-06-26 10:27:26');
INSERT INTO `gate_log` VALUES ('49006d4173784fb0b13528add5988278', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"8F78ECE557C311CE44B2ADF61642FB23\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750901252305}', 0, '客户端不在线', '2025-06-26 09:27:33');
INSERT INTO `gate_log` VALUES ('507801cf163e449d9277a2f6d336f49a', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"C6D954A20B9E28E0CF7C11AA533A18F3\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750836776875}', 1, '', '2025-06-25 15:32:57');
INSERT INTO `gate_log` VALUES ('542bcf43fdc445f1a9a783fb6b907eeb', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"E38E48428A75A60409E056E8A3400751\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750828458370}', 1, '', '2025-06-25 13:14:19');
INSERT INTO `gate_log` VALUES ('558de68a655a4a509f3606cb413767a6', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"6323823220FFED9D6EAF3273CF6DBDDA\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750903389289}', 1, '', '2025-06-26 10:03:09');
INSERT INTO `gate_log` VALUES ('587d070481aa4ec49831d97263eb8cd9', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"B718606A450F493D867A18406C0E6E22\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750901228612}', 0, '客户端不在线', '2025-06-26 09:27:09');
INSERT INTO `gate_log` VALUES ('5b3b79e9ee544b32a0c601b67b269166', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/pay/notice', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"amount\":100,\"payType\":2,\"tradeNo\":\"37Y120250626100309281065388P\",\"sign\":\"035A1C1E94806CC2449C2E588F406CF9\",\"plate\":\"沪AS1234\",\"timestamp\":1750904518495}', 0, '车场id不能为空', '2025-06-26 10:22:55');
INSERT INTO `gate_log` VALUES ('5fdf7885e64c4dc4a32835e567513706', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"2B6E945283B15CD1C0E9529F1200F174\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750838068831}', 1, '', '2025-06-25 15:54:29');
INSERT INTO `gate_log` VALUES ('6281050570494db499988ad0af8f4acc', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"6DCEAA7CEC30EC4681547B802EE93C29\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750836143561}', 1, '', '2025-06-25 15:22:24');
INSERT INTO `gate_log` VALUES ('6294481a6cfc4fb0ad3c35948b7f3d16', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"F3C363EBEC94185E41D850D6F44995C1\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750903369652}', 1, '', '2025-06-26 10:02:50');
INSERT INTO `gate_log` VALUES ('63a9174e4f214db4b4fa7b994eacb3bf', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"C31C3D53AAA852EB140A3A204296DC4F\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750828857223}', 1, '', '2025-06-25 13:20:58');
INSERT INTO `gate_log` VALUES ('6b10f675850d4328ad1c5f75c5900deb', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤MX7749', '{\"sign\":\"0626E32A4F5D38AF0F0E5D1743BF1186\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤MX7749\",\"timestamp\":1750826531157}', 1, '', '2025-06-25 12:42:11');
INSERT INTO `gate_log` VALUES ('6c1614b1626a49058ddcbb63887941fe', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"67A2979372BCA0872CB54F9C444971D3\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750904652681}', 1, '', '2025-06-26 10:24:13');
INSERT INTO `gate_log` VALUES ('6ea0771cc735480a8feb43cc612ddad6', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"D245F3E1D694C553754469EE28A68C5E\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750834684822}', 1, '', '2025-06-25 14:58:05');
INSERT INTO `gate_log` VALUES ('712865dac13a420289e9566b58155060', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"7DEE92E6D70A4AB008AD360D17FB729D\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750833040974}', 1, '', '2025-06-25 14:30:41');
INSERT INTO `gate_log` VALUES ('75912510179548458b275040a57425e8', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"352AAB08E390174C7AD569C35BA5348B\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750902483782}', 1, '', '2025-06-26 09:48:04');
INSERT INTO `gate_log` VALUES ('7a5c16078b024993bda7a405342985f9', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"327953CA8B7B8F34D0D3AB5E9D855BC4\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750904875521}', 1, '', '2025-06-26 10:27:56');
INSERT INTO `gate_log` VALUES ('7b9058b4e9204175b9489f7159ff7346', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"D62B97877C9911D2230CD105178E9B11\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750901866023}', 1, '', '2025-06-26 09:37:46');
INSERT INTO `gate_log` VALUES ('7d490a0938114cb78974cc1b554f6d67', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/pay/notice', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"amount\":100,\"payType\":2,\"tradeNo\":\"37Y120250626100309281065388P\",\"sign\":\"A0631BBC43CD0CF4A9A4EFB46D7C2782\",\"plate\":\"沪AS1234\",\"timestamp\":1750903436082}', 0, '车场id不能为空', '2025-06-26 10:03:56');
INSERT INTO `gate_log` VALUES ('7f4d8b9f44434abd9ef1407cf4ee5bed', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"E21441746054CDF3A1A202877E5FB4F9\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750903980235}', 1, '', '2025-06-26 10:13:00');
INSERT INTO `gate_log` VALUES ('81471ae1f76c42f6b1d276d647f50bc2', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"2D8619C2F21E7A8FE9028B3C35B374A1\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750828444076}', 1, '', '2025-06-25 13:14:04');
INSERT INTO `gate_log` VALUES ('81f37e751e2b451298091be463e025a8', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤MX7749', '{\"sign\":\"41FC0F8AFCC793DC5B090523BE9C6BBF\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤MX7749\",\"timestamp\":1750826540817}', 1, '', '2025-06-25 12:42:48');
INSERT INTO `gate_log` VALUES ('8262e33641db44bd84ef38c791ee6ecd', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"DD2CC3BC46C2BFD2CFE81DE148E4DB81\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750835596808}', 1, '', '2025-06-25 15:13:17');
INSERT INTO `gate_log` VALUES ('88471c2ba065427892d0ec2fff7edaa5', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"CAADED5CE5F1F5927FE64D6EB8D8F5CE\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750903352950}', 1, '', '2025-06-26 10:02:33');
INSERT INTO `gate_log` VALUES ('88767a2fc8db45b3ac30ee99791ff447', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"B25F692459422D571D970AC06A0A6E69\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750835553958}', 0, '客户端不在线', '2025-06-25 15:12:35');
INSERT INTO `gate_log` VALUES ('a77a2c49d6184451b31a4e630537d101', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"9AA76BE8C7DD5067172546136E2FE6AA\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750902725375}', 1, '', '2025-06-26 09:52:06');
INSERT INTO `gate_log` VALUES ('a9679b0e13784f25aa7898458eeb823a', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"AD6604D6684ECDA58179A96C0A0B1D8B\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750835148993}', 1, '', '2025-06-25 15:05:49');
INSERT INTO `gate_log` VALUES ('ad079d127f3b4b0ebc7f85309832dfef', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"7429FEE1EED6F8A9937D7F8815B98029\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750829925828}', 1, '', '2025-06-25 13:38:46');
INSERT INTO `gate_log` VALUES ('ada59ae8a2814d7d9de92c0fa6d6e97d', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"3F8581CEE289E4039C6AFE715B2294CA\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750827689976}', 1, '', '2025-06-25 13:01:30');
INSERT INTO `gate_log` VALUES ('aecba701de704d6899233226cdf98bbd', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"840542C23D23CE67D4E805C10CD6B4E1\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750836209422}', 1, '', '2025-06-25 15:23:30');
INSERT INTO `gate_log` VALUES ('b2bd2280019242d5acb469e696f055ef', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"AFFE16289667F35B563E0CE7975FDC15\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750906938827}', 1, '', '2025-06-26 11:02:20');
INSERT INTO `gate_log` VALUES ('b39b2e57336f4cb8af116151d23ea067', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"ACFEAE162E0D1E9DB1FF153A209A9708\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750830343639}', 1, '', '2025-06-25 13:45:44');
INSERT INTO `gate_log` VALUES ('b589d9c2ccd24470952fcaaaa38cd07c', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"943648A5469D967441FF5164423AAA9B\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750827512013}', 1, '', '2025-06-25 12:58:32');
INSERT INTO `gate_log` VALUES ('be5161abbda04f9d9267fc716bb5d865', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"BB62DA0CB9D60022AC155C621566BA7E\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750903257318}', 1, '', '2025-06-26 10:00:58');
INSERT INTO `gate_log` VALUES ('c286864771b043b58a478439105420c4', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"C5E11C4FB2532DAAE02A2B58ED22E1F3\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750836840332}', 1, '', '2025-06-25 15:34:01');
INSERT INTO `gate_log` VALUES ('c5feaa9e4ab24854aab8720866da73b2', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"EA62FDB3FD09BC0DDF19B23BB85B89A3\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750835626412}', 1, '', '2025-06-25 15:13:47');
INSERT INTO `gate_log` VALUES ('cc74e043c6d34350b4c9fdb3b2e9e1b8', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"5F427B8F3425A0553A2B834AE3124A9D\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750836122794}', 1, '', '2025-06-25 15:22:03');
INSERT INTO `gate_log` VALUES ('cfe49a28a40644949ba0f4763c00e368', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"8203B8FEB220C02C4D55D5E3B043DD0B\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750831015314}', 1, '', '2025-06-25 13:56:56');
INSERT INTO `gate_log` VALUES ('d05ce3b3c3ac4763849c004f16e577d8', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"755C284152E015BDAB9B485B811AB024\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750828283478}', 1, '', '2025-06-25 13:11:24');
INSERT INTO `gate_log` VALUES ('d368e0ff6b8442a690ae0096a4aa99b1', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"A635CD32C25C817B8BFCE23BA43BEE1B\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750829880934}', 1, '', '2025-06-25 13:38:01');
INSERT INTO `gate_log` VALUES ('d83ec1cecc914c2b92ac0103a55b2995', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"5BC5D8D03BF033D70390E702EECDEEAD\",\"plate\":\"沪S67445\",\"parkingI\":\"20240926133058945\",\"timestamp\":1750829539559}', 0, '车场id不能为空', '2025-06-25 13:32:20');
INSERT INTO `gate_log` VALUES ('dd8144a1f916497d900469c8229716e5', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"8BE386ADA9D86468E5F0039E2A9D0146\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750837706061}', 1, '', '2025-06-25 15:48:26');
INSERT INTO `gate_log` VALUES ('dda0b8687fd54a8ebb6ba078f806e04b', '思卓', '0.0.0.0', 'http://qr.it-wy.cn:83/transmit/vehicle/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"8356CD4F7E857C2AA233D53F814CA699\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750906052868}', 0, '未查询到车辆信息', '2025-06-26 10:47:33');
INSERT INTO `gate_log` VALUES ('dedfc923f92e42cba98d051a678efffe', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪S67445', '{\"sign\":\"CAF3F882E19A71E1DB6AC73630EBB1E7\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪S67445\",\"timestamp\":1750837153764}', 1, '', '2025-06-25 15:39:14');
INSERT INTO `gate_log` VALUES ('eb3e131bac694379ae50003475ff5e2a', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"A6A0FCBE1D7C3DBC24A6F90548D545C5\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750901610287}', 1, '', '2025-06-26 09:33:30');
INSERT INTO `gate_log` VALUES ('edccfb48677247e1bc3cd334bcc2f947', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"F667D76F4DFE7468CD4D2E7FCBFF675F\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750905231302}', 1, '', '2025-06-26 10:33:52');
INSERT INTO `gate_log` VALUES ('f9894134f99c4b5f9cb0743189743eab', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤MX7749', '{\"sign\":\"C3AB2DF0919B6FCE76AE780B5156BB9E\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤MX7749\",\"timestamp\":1750828492652}', 1, '', '2025-06-25 13:14:53');
INSERT INTO `gate_log` VALUES ('f99d0d17b4664b90b8471435446bb40a', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/queryv2', 0, 'parkingId:723055983177371648&&plateNum:粤QC1234', '{\"sign\":\"8716F96F7885FA5ECF742DD48A738793\",\"parkingID\":\"20240926133058945\",\"plate\":\"粤QC1234\",\"timestamp\":1750827217485}', 1, '', '2025-06-25 12:53:38');
INSERT INTO `gate_log` VALUES ('fa3d36f4a2164e1886547a398cd0f051', '思卓', '0.0.0.0', 'http://qr.it-wy.cn:83/transmit/vehicle/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"E559C1460E8ACFBE51C686FFE571F750\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750905108190}', 0, '未查询到车辆信息', '2025-06-26 10:31:48');
INSERT INTO `gate_log` VALUES ('fcf3b462ec194625ad1d7069c56dc2f0', '思卓', '**************', 'http://qr.it-wy.cn:83/transmit/price/query', 0, 'parkingId:723055983177371648&&plateNum:沪AS1234', '{\"sign\":\"25BBCFF09B0B515CD04063CF9BFB4DED\",\"parkingID\":\"20240926133058945\",\"plate\":\"沪AS1234\",\"timestamp\":1750903348084}', 1, '', '2025-06-26 10:02:28');

-- ----------------------------
-- Table structure for gate_parking_info
-- ----------------------------
DROP TABLE IF EXISTS `gate_parking_info`;
CREATE TABLE `gate_parking_info`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parkingId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本系统场库id',
  `plateNum` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号',
  `carType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车类型',
  `inTime` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '入场时间（unix）',
  `inChannelId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '入场通道id',
  `inChannelName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '入场名称',
  `inPic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '入场图片',
  `outTime` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出场时间（unix）',
  `outChannelId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出场通道id',
  `outChannelName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出场通道名称',
  `outPic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出场图片',
  `money` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '支付钱',
  `payType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '支付方式',
  `status` int NULL DEFAULT NULL COMMENT '状态（0入场，1出场）',
  `lastUpdate` datetime NULL DEFAULT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gate_parking_info
-- ----------------------------
INSERT INTO `gate_parking_info` VALUES ('0a46ed1890b74578ae5ede7147584383', '723055983177371648', '沪AS1234', '临时车', '1750901553', '1', '入口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '1750905107', '2', '出口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0.00', '2', 0, '2025-06-26 09:32:35');
INSERT INTO `gate_parking_info` VALUES ('1902922a186b4d58a98574bcba8c45da', '723055983177371648', '沪AS1236', '临时车', '1750918766', '1', '入口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0', '', '', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0', NULL, 0, '2025-06-26 14:19:28');
INSERT INTO `gate_parking_info` VALUES ('3001c44946a2438fa6a2e0345abc87c0', '723055983177371648', '沪AS1234', '临时车', '1750901553', '1', '入口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '1750905107', '2', '出口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0.00', '2', 0, '2025-06-26 09:32:35');
INSERT INTO `gate_parking_info` VALUES ('3a888eb1c04a4bac9b313dccec0419a0', '723055983177371648', '沪AS1234', '临时车', '1750901553', '1', '入口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '1750905107', '2', '出口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0.00', '2', 0, '2025-06-26 09:32:35');
INSERT INTO `gate_parking_info` VALUES ('8ae0aa984d284fd5a9d4659d59bfeda8', '723055983177371648', '沪AS1234', '临时车', '1750901553', '1', '入口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '1750905107', '2', '出口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0.00', '2', 0, '2025-06-26 09:32:35');
INSERT INTO `gate_parking_info` VALUES ('8fc48bbe90fe4d6798c901320ef50b71', '723055983177371648', '沪AS1234', '临时车', '1750901553', '1', '入口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '1750905107', '2', '出口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0.00', '2', 0, '2025-06-26 09:32:35');
INSERT INTO `gate_parking_info` VALUES ('adff6f437d53481cab6cc3670dba8b12', '723055983177371648', '沪AS1234', '临时车', '1750901553', '1', '入口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '1750905107', '2', '出口车道', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', '0.00', '2', 0, '2025-06-26 09:32:35');

-- ----------------------------
-- Table structure for gate_pull_log
-- ----------------------------
DROP TABLE IF EXISTS `gate_pull_log`;
CREATE TABLE `gate_pull_log`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键id',
  `parking_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '场库名称',
  `access_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求地址',
  `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求路径',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求数据',
  `last_update` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后一次更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gate_pull_log
-- ----------------------------
INSERT INTO `gate_pull_log` VALUES ('0b134708d4384b62bfa39c770410809b', '思卓', '**************', 'CarOut', '{\"InParkNum\":4,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":996,\"InChannelName\":\"入口车道\",\"OutTime\":\"2025-06-26 10:31:47\",\"VehicleType\":\"临时车\",\"PayType\":\"微信\",\"OrderID\":\"7b1bd962-fd6b-4708-9bef-d2eea6cd6fc8\",\"Mobile\":\"\",\"Plate\":\"沪AS1234\",\"ShouldPay\":\"100\",\"ChannelName\":\"出口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:32:33\",\"ChannelNo\":2,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"InChannelIndex\":1,\"TradeNo\":\"20250626103147160378298\",\"TotalLot\":996,\"PayMoney\":\"100\"}', '2025-06-26 10:47:32');
INSERT INTO `gate_pull_log` VALUES ('0cc2a5298116463681b7d7099c12eac3', '思卓', '**************', 'CarIn', '{\"InParkNum\":1,\"OperatorName\":\"admin\",\"UserName\":\"张三\",\"IDCard\":\"\",\"ProjectName\":\"\",\"TempLot\":998,\"VehicleType\":\"临停车\",\"OrderID\":\"5ea065f6-792c-4244-a446-c93866bea891\",\"Mobile\":\"13800000000\",\"Plate\":\"赣A11111\",\"ChannelName\":\"北门入口\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-20 16:00:00\",\"ChannelNo\":1,\"PlateType\":1,\"ChargeTypeName\":\"标准收费标准\",\"TotalLot\":998}', '2025-06-25 10:09:29');
INSERT INTO `gate_pull_log` VALUES ('12d5080cab0e4bc7b2bc7a9abbc7acb7', '思卓', '**************', 'CarIn', '{\"InParkNum\":3,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":997,\"VehicleType\":\"临时车\",\"OrderID\":\"f696c0f0-0e05-4899-b657-b33eaac4ee1d\",\"Mobile\":\"\",\"Plate\":\"沪AS1236\",\"ChannelName\":\"入口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:41:17\",\"ChannelNo\":1,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"TotalLot\":997}', '2025-06-26 09:41:17');
INSERT INTO `gate_pull_log` VALUES ('3161a54b76f14eeea0edad6ab7615de1', '思卓', '**************', 'CarOut', '{\"InParkNum\":4,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":996,\"InChannelName\":\"入口车道\",\"OutTime\":\"2025-06-26 10:31:47\",\"VehicleType\":\"临时车\",\"PayType\":\"微信\",\"OrderID\":\"7b1bd962-fd6b-4708-9bef-d2eea6cd6fc8\",\"Mobile\":\"\",\"Plate\":\"沪AS1234\",\"ShouldPay\":\"100\",\"ChannelName\":\"出口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:32:33\",\"ChannelNo\":2,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"InChannelIndex\":1,\"TradeNo\":\"20250626103147160378298\",\"TotalLot\":996,\"PayMoney\":\"100\"}', '2025-06-26 10:42:54');
INSERT INTO `gate_pull_log` VALUES ('630ea56793ac4bef9366d2fda65ed18e', '思卓', '**************', 'CarOut', '{\"InParkNum\":4,\"ProjectName\":\"测试\",\"InChannelName\":\"入口车道\",\"OrderID\":\"7b1bd962-fd6b-4708-9bef-d2eea6cd6fc8\",\"ShouldPay\":\"100\",\"ChannelNo\":2,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"PayMoney\":\"100\",\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"TempLot\":996,\"OutTime\":\"2025-06-26 10:31:47\",\"VehicleType\":\"临时车\",\"PayType\":\"微信\",\"Mobile\":\"\",\"Plate\":\"沪AS1234\",\"ChannelName\":\"出口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:32:33\",\"InChannelIndex\":1,\"TradeNo\":\"20250626103147160378298\",\"TotalLot\":996}', '2025-06-26 10:31:48');
INSERT INTO `gate_pull_log` VALUES ('783910b66d5d474d9c38fee75911f31b', '思卓', '**************', 'CarOut', '{\"InParkNum\":4,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":996,\"InChannelName\":\"入口车道\",\"OutTime\":\"2025-06-26 10:31:47\",\"VehicleType\":\"临时车\",\"PayType\":\"微信\",\"OrderID\":\"7b1bd962-fd6b-4708-9bef-d2eea6cd6fc8\",\"Mobile\":\"\",\"Plate\":\"沪AS1234\",\"ShouldPay\":\"100\",\"ChannelName\":\"出口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:32:33\",\"ChannelNo\":2,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"InChannelIndex\":1,\"TradeNo\":\"20250626103147160378298\",\"TotalLot\":996,\"PayMoney\":\"100\"}', '2025-06-26 10:59:08');
INSERT INTO `gate_pull_log` VALUES ('816989dd4f8c4e5d8cb068ed1bc535a8', '思卓', '**************', 'CarIn', '{\"InParkNum\":2,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":998,\"VehicleType\":\"临时车\",\"OrderID\":\"95a4e751-a7fb-408c-97f1-98075cbd67d7\",\"Mobile\":\"\",\"Plate\":\"沪AS1235\",\"ChannelName\":\"入口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:41:08\",\"ChannelNo\":1,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"TotalLot\":998}', '2025-06-26 09:41:11');
INSERT INTO `gate_pull_log` VALUES ('ae0cc162b12a4d43959ba5f33ffc1dad', '思卓', '**************', 'CarIn', '{\"InParkNum\":5,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":995,\"VehicleType\":\"临时车\",\"OrderID\":\"0f4df4df-23be-45a9-9329-58dba30bc8f0\",\"Mobile\":\"\",\"Plate\":\"粤QC1234\",\"ChannelName\":\"入口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-25 12:52:38\",\"ChannelNo\":1,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"TotalLot\":995}', '2025-06-25 12:52:39');
INSERT INTO `gate_pull_log` VALUES ('b45cf3740c104438ad87318ea8e1f190', '思卓', '**************', 'CarOut', '{\"InParkNum\":4,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":996,\"InChannelName\":\"入口车道\",\"OutTime\":\"2025-06-26 10:31:47\",\"VehicleType\":\"临时车\",\"PayType\":\"微信\",\"OrderID\":\"7b1bd962-fd6b-4708-9bef-d2eea6cd6fc8\",\"Mobile\":\"\",\"Plate\":\"沪AS1234\",\"ShouldPay\":\"100\",\"ChannelName\":\"出口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:32:33\",\"ChannelNo\":2,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"InChannelIndex\":1,\"TradeNo\":\"20250626103147160378298\",\"TotalLot\":996,\"PayMoney\":\"100\"}', '2025-06-26 10:51:08');
INSERT INTO `gate_pull_log` VALUES ('c6c9b975c9e540c69878839a14a89c4b', '思卓', '**************', 'CarIn', '{\"InParkNum\":1,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":999,\"VehicleType\":\"临时车\",\"OrderID\":\"7b1bd962-fd6b-4708-9bef-d2eea6cd6fc8\",\"Mobile\":\"\",\"Plate\":\"沪AS1234\",\"ChannelName\":\"入口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:32:33\",\"ChannelNo\":1,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"TotalLot\":999}', '2025-06-26 09:32:34');
INSERT INTO `gate_pull_log` VALUES ('cdb070f7d70640248e6fbcafad39bf78', '思卓', '**************', 'CarOut', '{\"InParkNum\":4,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":996,\"InChannelName\":\"入口车道\",\"OutTime\":\"2025-06-26 10:31:47\",\"VehicleType\":\"临时车\",\"PayType\":\"微信\",\"OrderID\":\"7b1bd962-fd6b-4708-9bef-d2eea6cd6fc8\",\"Mobile\":\"\",\"Plate\":\"沪AS1234\",\"ShouldPay\":\"100\",\"ChannelName\":\"出口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-26 09:32:33\",\"ChannelNo\":2,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"InChannelIndex\":1,\"TradeNo\":\"20250626103147160378298\",\"TotalLot\":996,\"PayMoney\":\"100\"}', '2025-06-26 10:49:28');
INSERT INTO `gate_pull_log` VALUES ('e44c1279aa75451d95906a26f753638d', '思卓', '**************', 'CarIn', '{\"InParkNum\":4,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":996,\"VehicleType\":\"临时车\",\"OrderID\":\"13c33751-f031-4c8a-a649-21fc420e1320\",\"Mobile\":\"\",\"Plate\":\"粤MX7749\",\"ChannelName\":\"入口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-25 10:57:26\",\"ChannelNo\":1,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"TotalLot\":996}', '2025-06-25 10:57:27');
INSERT INTO `gate_pull_log` VALUES ('f47401891cfd4de3aa7c127b711c1550', '思卓', '**************', 'CarIn', '{\"InParkNum\":0,\"OperatorName\":\"admin\",\"UserName\":\"临时用户\",\"IDCard\":\"\",\"ProjectName\":\"测试\",\"TempLot\":1000,\"VehicleType\":\"临时车\",\"OrderID\":\"53c9fafe-de05-4a8f-a10e-05c06c3296dd\",\"Mobile\":\"\",\"Plate\":\"沪S67445\",\"ChannelName\":\"入口车道\",\"ParkID\":\"20240926133058945\",\"InTime\":\"2025-06-25 13:17:19\",\"ChannelNo\":1,\"PlateType\":0,\"ChargeTypeName\":\"标准收费标准\",\"TotalLot\":1000}', '2025-06-25 13:17:20');

-- ----------------------------
-- Table structure for mini_advert_config
-- ----------------------------
DROP TABLE IF EXISTS `mini_advert_config`;
CREATE TABLE `mini_advert_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注说明',
  `advert_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告标题',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '跳转链接',
  `pic_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片地址',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-生效，2-失效',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status_delete`(`status`, `delete_flag`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_create_by`(`create_by`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '广告配置信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_advert_config
-- ----------------------------
INSERT INTO `mini_advert_config` VALUES (1, '测试广告1', '春节促销活动', 'https://www.example.com/spring-sale', 'https://www.example.com/images/spring-sale.jpg', 2, 0, 1, '2025-06-06 13:56:05', 1, '2025-06-06 14:57:56');
INSERT INTO `mini_advert_config` VALUES (2, '测试广告2', '新用户注册优惠', 'https://www.example.com/new-user', 'https://www.example.com/images/new-user.jpg', 1, 1, 1, '2025-06-06 13:56:05', NULL, '2025-06-06 15:36:38');
INSERT INTO `mini_advert_config` VALUES (3, '元旦活动广告', '元旦狂欢节', 'https://www.example.com/new-year', 'http://127.0.0.1:9300/statics/2025/06/16/banner2_20250616095456A002.png', 1, 0, 1, '2025-06-06 13:58:40', 1, '2025-06-06 15:04:44');
INSERT INTO `mini_advert_config` VALUES (4, '会员专享广告', '会员专享优惠', 'https://www.example.com/vip', 'http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png', 2, 0, 1, '2025-06-06 13:58:40', 1, '2025-06-06 15:04:44');
INSERT INTO `mini_advert_config` VALUES (5, '限时促销广告', '限时抢购活动1', 'https://www.example.com/flash-sale', 'http://127.0.0.1:9300/statics/2025/06/06/男1_20250606142446A001.png', 2, 0, 1, '2025-06-06 13:58:40', 1, '2025-06-06 15:04:50');
INSERT INTO `mini_advert_config` VALUES (7, '测试更新操作', '优化测试广告1', 'https://test.com', 'http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月23日_07_30_16_20250703001439A004.png', 1, 0, 1, '2025-06-06 15:07:27', 1, '2025-06-06 15:07:42');
INSERT INTO `mini_advert_config` VALUES (8, '123123', '123就应该', 'http://tes1231223133t.com', 'http://127.0.0.1:9300/statics/2025/07/02/ChatGPT_Image_2025年5月22日_21_07_13_20250702212258A002.png', 1, 0, 1, '2025-07-02 21:23:12', NULL, '2025-07-02 21:23:12');
INSERT INTO `mini_advert_config` VALUES (9, NULL, '1231', NULL, 'http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月22日_21_07_13_20250703001422A003.png', 1, 1, NULL, '2025-07-03 00:14:23', NULL, '2025-07-03 00:14:27');

-- ----------------------------
-- Table structure for mini_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `mini_blacklist`;
CREATE TABLE `mini_blacklist`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `plate_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号码',
  `begin_time` timestamp NULL DEFAULT NULL COMMENT '黑名单生效起始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '黑名单失效截止时间',
  `warehouse_id` bigint NULL DEFAULT NULL COMMENT '关联的场库ID',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车主姓名',
  `phone_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车主联系电话',
  `img_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联证据图片URL',
  `delete_flag` tinyint NULL DEFAULT 0 COMMENT '软删除标记（0=正常，1=已删除）',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '最后修改人ID',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '车辆黑名单管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_blacklist
-- ----------------------------
INSERT INTO `mini_blacklist` VALUES (4, '蒙EH0338', NULL, '2025-04-30 08:00:00', 2, '司卓平台白名单', '123123', NULL, 1, 158, '2024-04-22 19:43:23', 158, '2024-04-22 19:59:47');
INSERT INTO `mini_blacklist` VALUES (5, '沪CCU019', '2024-09-03 00:00:00', '2024-09-19 23:59:59', 2, NULL, NULL, NULL, 1, 149, '2024-09-30 20:15:33', 149, '2024-11-05 16:15:36');
INSERT INTO `mini_blacklist` VALUES (6, '沪CCU019', '2024-09-18 00:00:00', '2024-09-05 23:59:59', 2, NULL, NULL, NULL, 1, 149, '2024-09-30 20:16:53', 149, '2024-11-05 16:15:33');
INSERT INTO `mini_blacklist` VALUES (7, 'HA1UDC', '2024-09-01 00:00:00', '2024-09-02 23:59:59', 2, NULL, NULL, NULL, 1, 151, '2024-09-30 20:29:31', 151, '2024-12-12 16:43:54');
INSERT INTO `mini_blacklist` VALUES (8, 'test111', '2024-10-01 00:00:00', '2024-09-01 23:59:59', 13, NULL, NULL, NULL, 1, 169, '2024-10-15 11:22:27', 169, '2024-10-15 11:22:50');
INSERT INTO `mini_blacklist` VALUES (9, '沪FAX672', '2024-10-22 00:00:00', '2024-10-22 23:59:59', 1, NULL, NULL, NULL, 1, 149, '2024-10-22 14:44:54', 149, '2024-10-22 14:46:56');
INSERT INTO `mini_blacklist` VALUES (10, '沪AFX783', '2024-10-22 00:00:00', '2024-10-22 23:59:59', 1, NULL, NULL, 'https://test-img.lgfw24hours.com:7443/upload/parkingManage/2024-10-22/144725.jpg', 1, 149, '2024-10-22 14:47:28', 149, '2024-10-28 09:37:22');
INSERT INTO `mini_blacklist` VALUES (11, '沪CCU019', '2024-11-04 00:00:00', '2024-11-05 23:59:59', 2, NULL, NULL, NULL, 1, 150, '2024-11-04 10:48:17', 150, '2024-11-04 10:49:15');
INSERT INTO `mini_blacklist` VALUES (12, '沪CCU019', '2024-11-04 00:00:00', '2024-11-05 23:59:59', 21, NULL, NULL, NULL, 1, 150, '2024-11-04 10:50:25', 150, '2024-11-04 10:55:39');
INSERT INTO `mini_blacklist` VALUES (13, '2131', '2025-01-09 00:00:00', '2025-01-11 23:59:59', 2, NULL, NULL, 'https://test-img.lgfw24hours.com:7443/upload/parkingManage/2025-01-10/154419.png', 1, 169, '2025-01-10 15:44:24', 169, '2025-01-10 15:44:39');

-- ----------------------------
-- Table structure for mini_charging_standard
-- ----------------------------
DROP TABLE IF EXISTS `mini_charging_standard`;
CREATE TABLE `mini_charging_standard`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `standard_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收费标准编码',
  `standard_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收费标准名称',
  `free_duration_minutes` int NOT NULL DEFAULT 0 COMMENT '免费时长（分钟）',
  `max_daily_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '单日最高收费（元，NULL表示无上限）',
  `is_cycle_billing` tinyint NOT NULL DEFAULT 1 COMMENT '是否循环计费（0-否，1-是，超过24小时按此标准循环计费）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '收费标准描述',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态（1-启用，2-停用）',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序权重',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_standard_code`(`standard_code`) USING BTREE,
  INDEX `idx_status`(`status`, `delete_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '收费标准配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_charging_standard
-- ----------------------------
INSERT INTO `mini_charging_standard` VALUES (1, 'STANDARD_001', '鸿音广场收费标准', 30, 18.00, 1, '半小时内免费，0.5-3小时6元，3-6小时12元，6-24小时18元，超过24小时按此标准循环计费', 1, 1, 0, 1, '2025-06-12 05:28:11', 1, '2025-06-12 05:28:11');
INSERT INTO `mini_charging_standard` VALUES (2, 'STANDARD_002', 'LIN舍公寓收费标准', 30, 20.00, 1, '半小时内免费，0.5-3小时6元，3-6小时10元，6-12小时15元，12-24小时20元', 1, 2, 0, 1, '2025-06-12 05:28:11', 1, '2025-06-12 05:28:11');
INSERT INTO `mini_charging_standard` VALUES (3, 'STANDARD_003', '临港四期收费标准', 30, 15.00, 1, '半小时内免费，0.5-4小时5元，4-8小时10元，8-24小时15元', 1, 3, 0, 1, '2025-06-12 05:28:11', 1, '2025-06-12 05:28:11');
INSERT INTO `mini_charging_standard` VALUES (4, 'STANDARD_004', '临港科技城收费标准', 60, 15.00, 1, '一小时内免费，1-8小时5元，8-16小时10元，16-24小时15元', 1, 4, 0, 1, '2025-06-12 05:28:11', 1, '2025-06-12 05:28:11');
INSERT INTO `mini_charging_standard` VALUES (5, 'STANDARD_005', '临港一二期收费标准', 60, 10.00, 1, '一小时内免费，1-12小时5元，12-24小时10元', 1, 5, 0, 1, '2025-06-12 05:28:11', 1, '2025-06-12 05:28:11');
INSERT INTO `mini_charging_standard` VALUES (6, 'STANDARD_006', '临港三五期收费标准', 60, 10.00, 1, '一小时内免费，8小时内5元，8-24小时每8小时加5元', 1, 6, 0, 1, '2025-06-12 05:28:11', 1, '2025-06-12 05:28:11');
INSERT INTO `mini_charging_standard` VALUES (7, 'STANDARD_007', '松江南桥收费标准', 60, 15.00, 1, '一小时内免费，1-8小时10元，8-24小时15元', 1, 7, 0, 1, '2025-06-12 05:28:11', 1, '2025-06-12 05:28:11');

-- ----------------------------
-- Table structure for mini_charging_time_rule
-- ----------------------------
DROP TABLE IF EXISTS `mini_charging_time_rule`;
CREATE TABLE `mini_charging_time_rule`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `standard_id` bigint NOT NULL COMMENT '收费标准ID',
  `rule_order` int NOT NULL COMMENT '规则顺序（从1开始）',
  `start_minutes` int NOT NULL COMMENT '起始时间（分钟，从停车开始计算）',
  `end_minutes` int NOT NULL COMMENT '结束时间（分钟，从停车开始计算）',
  `fee_amount` decimal(10, 2) NOT NULL COMMENT '该时段收费金额（元）',
  `rule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规则名称（如：0.5-3小时）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_standard_id`(`standard_id`) USING BTREE,
  INDEX `idx_rule_order`(`standard_id`, `rule_order`) USING BTREE,
  CONSTRAINT `fk_charging_time_rule_standard` FOREIGN KEY (`standard_id`) REFERENCES `mini_charging_standard` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '收费时段规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_charging_time_rule
-- ----------------------------
INSERT INTO `mini_charging_time_rule` VALUES (1, 1, 1, 30, 180, 6.00, '0.5-3小时', '半小时后至3小时收费6元', '2025-06-12 05:28:18', '2025-06-12 05:28:18');
INSERT INTO `mini_charging_time_rule` VALUES (2, 1, 2, 180, 360, 12.00, '3-6小时', '3小时后至6小时收费12元', '2025-06-12 05:28:18', '2025-06-12 05:28:18');
INSERT INTO `mini_charging_time_rule` VALUES (3, 1, 3, 360, 1440, 18.00, '6-24小时', '6小时后至24小时收费18元', '2025-06-12 05:28:18', '2025-06-12 05:28:18');
INSERT INTO `mini_charging_time_rule` VALUES (4, 2, 1, 30, 180, 6.00, '0.5-3小时', '半小时后至3小时收费6元', '2025-06-12 05:28:25', '2025-06-12 05:28:25');
INSERT INTO `mini_charging_time_rule` VALUES (5, 2, 2, 180, 360, 10.00, '3-6小时', '3小时后至6小时收费10元', '2025-06-12 05:28:25', '2025-06-12 05:28:25');
INSERT INTO `mini_charging_time_rule` VALUES (6, 2, 3, 360, 720, 15.00, '6-12小时', '6小时后至12小时收费15元', '2025-06-12 05:28:25', '2025-06-12 05:28:25');
INSERT INTO `mini_charging_time_rule` VALUES (7, 2, 4, 720, 1440, 20.00, '12-24小时', '12小时后至24小时收费20元', '2025-06-12 05:28:25', '2025-06-12 05:28:25');
INSERT INTO `mini_charging_time_rule` VALUES (8, 3, 1, 30, 240, 5.00, '0.5-4小时', '半小时后至4小时收费5元', '2025-06-12 05:28:31', '2025-06-12 05:28:31');
INSERT INTO `mini_charging_time_rule` VALUES (9, 3, 2, 240, 480, 10.00, '4-8小时', '4小时后至8小时收费10元', '2025-06-12 05:28:31', '2025-06-12 05:28:31');
INSERT INTO `mini_charging_time_rule` VALUES (10, 3, 3, 480, 1440, 15.00, '8-24小时', '8小时后至24小时收费15元', '2025-06-12 05:28:31', '2025-06-12 05:28:31');
INSERT INTO `mini_charging_time_rule` VALUES (11, 4, 1, 60, 480, 5.00, '1-8小时', '1小时后至8小时收费5元', '2025-06-12 05:28:38', '2025-06-12 05:28:38');
INSERT INTO `mini_charging_time_rule` VALUES (12, 4, 2, 480, 960, 10.00, '8-16小时', '8小时后至16小时收费10元', '2025-06-12 05:28:38', '2025-06-12 05:28:38');
INSERT INTO `mini_charging_time_rule` VALUES (13, 4, 3, 960, 1440, 15.00, '16-24小时', '16小时后至24小时收费15元', '2025-06-12 05:28:38', '2025-06-12 05:28:38');
INSERT INTO `mini_charging_time_rule` VALUES (14, 5, 1, 60, 720, 5.00, '1-12小时', '1小时后至12小时收费5元', '2025-06-12 05:28:44', '2025-06-12 05:28:44');
INSERT INTO `mini_charging_time_rule` VALUES (15, 5, 2, 720, 1440, 10.00, '12-24小时', '12小时后至24小时收费10元', '2025-06-12 05:28:44', '2025-06-12 05:28:44');
INSERT INTO `mini_charging_time_rule` VALUES (16, 6, 1, 60, 480, 5.00, '1-8小时', '1小时后至8小时收费5元', '2025-06-12 05:28:50', '2025-06-12 05:28:50');
INSERT INTO `mini_charging_time_rule` VALUES (17, 6, 2, 480, 960, 5.00, '8-16小时', '8小时后至16小时再收费5元', '2025-06-12 05:28:50', '2025-06-12 05:28:50');
INSERT INTO `mini_charging_time_rule` VALUES (18, 6, 3, 960, 1440, 5.00, '16-24小时', '16小时后至24小时再收费5元', '2025-06-12 05:28:50', '2025-06-12 05:28:50');
INSERT INTO `mini_charging_time_rule` VALUES (19, 7, 1, 60, 480, 10.00, '1-8小时', '1小时后至8小时收费10元', '2025-06-12 05:28:57', '2025-06-12 05:28:57');
INSERT INTO `mini_charging_time_rule` VALUES (20, 7, 2, 480, 1440, 15.00, '8-24小时', '8小时后至24小时收费15元', '2025-06-12 05:28:57', '2025-06-12 05:28:57');

-- ----------------------------
-- Table structure for mini_coupon
-- ----------------------------
DROP TABLE IF EXISTS `mini_coupon`;
CREATE TABLE `mini_coupon`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '优惠券模板ID',
  `coupon_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '优惠券编码（唯一标识）',
  `coupon_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '优惠券名称',
  `coupon_type` tinyint NOT NULL COMMENT '优惠券类型（1-满减券，2-折扣券，3-无门槛减免券，4-免费停车券）',
  `discount_type` tinyint NOT NULL COMMENT '折扣方式（1-固定金额，2-百分比折扣）',
  `discount_value` decimal(10, 2) NOT NULL COMMENT '优惠值（金额或折扣百分比）',
  `threshold_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '使用门槛金额（0表示无门槛）',
  `max_discount_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高优惠金额（折扣券专用）',
  `applicable_scope` tinyint NOT NULL DEFAULT 1 COMMENT '适用范围（1-全场通用，2-指定停车场，3-指定会员等级）',
  `warehouse_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '适用停车场ID列表（JSON格式，applicable_scope=2时使用）',
  `member_levels` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '适用会员等级（逗号分隔，applicable_scope=3时使用）',
  `issue_type` tinyint NOT NULL DEFAULT 1 COMMENT '发放方式（1-手动发放，2-自动发放，3-用户领取，4-活动奖励）',
  `total_quantity` int NULL DEFAULT NULL COMMENT '发行总量（NULL表示无限制）',
  `issued_quantity` int NULL DEFAULT 0 COMMENT '已发放数量',
  `used_quantity` int NULL DEFAULT 0 COMMENT '已使用数量',
  `user_limit` int NULL DEFAULT 1 COMMENT '每人限领数量',
  `issue_start_time` datetime NOT NULL COMMENT '发放开始时间',
  `issue_end_time` datetime NOT NULL COMMENT '发放结束时间',
  `valid_type` tinyint NOT NULL DEFAULT 1 COMMENT '有效期类型（1-固定时间段，2-领取后N天有效）',
  `valid_days` int NULL DEFAULT NULL COMMENT '有效天数（valid_type=2时使用）',
  `valid_start_time` datetime NULL DEFAULT NULL COMMENT '有效期开始时间（valid_type=1时使用）',
  `valid_end_time` datetime NULL DEFAULT NULL COMMENT '有效期结束时间（valid_type=1时使用）',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态（1-待发放，2-发放中，3-已结束，4-已暂停）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '优惠券描述',
  `usage_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '使用规则说明',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序权重',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_coupon_code`(`coupon_code`) USING BTREE,
  INDEX `idx_status`(`status`, `delete_flag`) USING BTREE,
  INDEX `idx_issue_time`(`issue_start_time`, `issue_end_time`) USING BTREE,
  INDEX `idx_valid_time`(`valid_start_time`, `valid_end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '优惠券模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_coupon
-- ----------------------------
INSERT INTO `mini_coupon` VALUES (1, 'COUPON_001', '新用户专享5元优惠券', 1, 1, 5.00, 10.00, NULL, 1, NULL, NULL, 3, 1000, 0, 0, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 2, 30, NULL, NULL, 2, '新用户注册专享优惠券，满10元可用', '仅限新用户使用，不可与其他优惠同享', 1, 0, 1, '2025-06-12 19:53:59', 1, '2025-06-12 22:42:52');
INSERT INTO `mini_coupon` VALUES (2, 'COUPON_002', '停车8折优惠券', 2, 2, 80.00, 0.00, 20.00, 1, NULL, NULL, 1, 500, 0, 0, 2, '2024-01-01 00:00:00', '2024-06-30 23:59:59', 1, NULL, '2024-01-01 00:00:00', '2024-06-30 23:59:59', 2, '停车费用8折优惠，最高优惠20元', '适用于所有停车场，单次使用', 2, 0, 1, '2025-06-12 19:53:59', 1, '2025-06-12 19:53:59');
INSERT INTO `mini_coupon` VALUES (3, 'COUPON_003', 'VIP会员专享10元券', 1, 1, 10.00, 20.00, NULL, 3, NULL, '2,3,4,5', 2, NULL, 0, 0, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 2, 7, NULL, NULL, 2, 'VIP会员专享优惠券', '仅限银卡及以上会员使用', 3, 0, 1, '2025-06-12 19:53:59', 1, '2025-06-12 19:53:59');
INSERT INTO `mini_coupon` VALUES (4, 'COUPON_004', '免费停车1小时券', 4, 1, 60.00, 0.00, NULL, 1, NULL, NULL, 4, 200, 0, 0, 1, '2024-01-01 00:00:00', '2024-03-31 23:59:59', 1, NULL, '2024-01-01 00:00:00', '2024-03-31 23:59:59', 1, '免费停车1小时，活动奖励', '限时1小时内有效，超时按正常收费', 4, 0, 1, '2025-06-12 19:53:59', 1, '2025-06-12 19:53:59');
INSERT INTO `mini_coupon` VALUES (5, 'COUPON202506127435', '新用户专享5元优惠券_副本', 1, 1, 5.00, 10.00, NULL, 1, NULL, NULL, 3, 1000, 0, 0, 1, '2025-06-12 22:49:07', '2025-07-12 22:49:07', 2, 30, NULL, NULL, 1, '新用户注册专享优惠券，满10元可用', '仅限新用户使用，不可与其他优惠同享', 1, 0, 1, '2025-06-12 22:49:07', 1, '2025-06-12 23:30:59');
INSERT INTO `mini_coupon` VALUES (6, 'COUPON202506124285', '新用户专享5元优惠券_副本_副本', 1, 1, 7.00, 15.00, NULL, 1, NULL, NULL, 3, 1000, 0, 0, 1, '2025-06-12 22:49:14', '2025-07-12 22:49:14', 1, 30, '2025-06-13 00:00:00', '2025-09-05 00:00:00', 2, '新用户注册专享优惠券，满10元可用', '仅限新用户使用，不可与其他优惠同享', 1, 0, 1, '2025-06-12 22:49:14', 1, '2025-06-13 04:10:07');
INSERT INTO `mini_coupon` VALUES (7, 'COUPON202506137205', '新用户专享5元优惠券_副本_副本_副本', 1, 1, 7.00, 15.00, NULL, 1, NULL, NULL, 3, 1000, 0, 0, 1, '2025-06-13 11:48:27', '2025-07-13 11:48:27', 1, 30, '2025-06-13 11:48:27', '2025-07-13 11:48:27', 1, '新用户注册专享优惠券，满10元可用', '仅限新用户使用，不可与其他优惠同享', 1, 1, 1, '2025-06-13 11:48:27', NULL, '2025-07-02 23:32:12');
INSERT INTO `mini_coupon` VALUES (8, 'COUPON202506176848', '新用户专享5元优惠券_副本_副本_副本', 1, 1, 7.00, 15.00, NULL, 1, NULL, NULL, 3, 1000, 0, 0, 1, '2025-06-17 13:10:07', '2025-07-17 13:10:07', 1, 30, '2025-06-17 13:10:07', '2025-07-17 13:10:07', 1, '新用户注册专享优惠券，满10元可用', '仅限新用户使用，不可与其他优惠同享', 1, 0, 1, '2025-06-17 13:10:06', NULL, '2025-07-02 23:32:12');

-- ----------------------------
-- Table structure for mini_coupon_activity
-- ----------------------------
DROP TABLE IF EXISTS `mini_coupon_activity`;
CREATE TABLE `mini_coupon_activity`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `activity_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动编码',
  `activity_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动名称',
  `activity_type` tinyint NOT NULL COMMENT '活动类型（1-新用户注册，2-首次停车，3-会员充值，4-节日活动，5-满额赠送）',
  `trigger_condition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '触发条件（JSON格式）',
  `coupon_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联优惠券ID列表（JSON格式）',
  `activity_start_time` datetime NOT NULL COMMENT '活动开始时间',
  `activity_end_time` datetime NOT NULL COMMENT '活动结束时间',
  `participant_limit` int NULL DEFAULT NULL COMMENT '参与人数限制（NULL表示无限制）',
  `current_participants` int NULL DEFAULT 0 COMMENT '当前参与人数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '活动状态（1-待开始，2-进行中，3-已结束，4-已暂停）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动描述',
  `activity_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动规则',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序权重',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_activity_code`(`activity_code`) USING BTREE,
  INDEX `idx_activity_type`(`activity_type`) USING BTREE,
  INDEX `idx_status`(`status`, `delete_flag`) USING BTREE,
  INDEX `idx_activity_time`(`activity_start_time`, `activity_end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '优惠券活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_coupon_activity
-- ----------------------------
INSERT INTO `mini_coupon_activity` VALUES (1, 'ACTIVITY_001', '新年优惠券大放送', 4, '{\"description\": \"新年活动，全场优惠券免费领取\"}', '[1,2,4]', '2024-01-01 00:00:00', '2024-01-31 23:59:59', 1000, 156, 3, '新年活动，多种优惠券任您选择', '每人限领3张，先到先得', 1, 0, 1, '2025-06-12 19:54:19', 1, '2025-06-12 19:54:19');
INSERT INTO `mini_coupon_activity` VALUES (2, 'ACTIVITY_002', '新用户注册送券', 1, '{\"user_type\": \"new\", \"register_days\": 7}', '[1]', '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, 89, 2, '新用户注册即送优惠券', '注册7天内自动发放', 2, 0, 1, '2025-06-12 19:54:19', 1, '2025-06-12 19:54:19');

-- ----------------------------
-- Table structure for mini_coupon_usage_log
-- ----------------------------
DROP TABLE IF EXISTS `mini_coupon_usage_log`;
CREATE TABLE `mini_coupon_usage_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `coupon_user_id` bigint NOT NULL COMMENT '用户优惠券ID',
  `coupon_id` bigint NOT NULL COMMENT '优惠券模板ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `member_id` bigint NULL DEFAULT NULL COMMENT '会员ID',
  `coupon_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '优惠券编号',
  `usage_type` tinyint NOT NULL COMMENT '操作类型（1-领取，2-使用，3-过期，4-作废）',
  `parking_lot_id` bigint NULL DEFAULT NULL COMMENT '停车场ID',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单号',
  `original_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '原始金额',
  `discount_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `final_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终金额',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `operator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_coupon_user_id`(`coupon_user_id`) USING BTREE,
  INDEX `idx_coupon_id`(`coupon_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_coupon_no`(`coupon_no`) USING BTREE,
  INDEX `idx_usage_type`(`usage_type`) USING BTREE,
  INDEX `idx_operation_time`(`operation_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '优惠券使用记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_coupon_usage_log
-- ----------------------------

-- ----------------------------
-- Table structure for mini_coupon_user
-- ----------------------------
DROP TABLE IF EXISTS `mini_coupon_user`;
CREATE TABLE `mini_coupon_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
  `coupon_id` bigint NOT NULL COMMENT '优惠券模板ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID（微信用户）',
  `member_id` bigint NULL DEFAULT NULL COMMENT '会员ID（VIP会员）',
  `coupon_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '优惠券编号（唯一）',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
  `receive_time` datetime NOT NULL COMMENT '领取时间',
  `valid_start_time` datetime NOT NULL COMMENT '有效期开始时间',
  `valid_end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `use_status` tinyint NOT NULL DEFAULT 0 COMMENT '使用状态（0-未使用，1-已使用，2-已过期，3-已作废）',
  `use_time` datetime NULL DEFAULT NULL COMMENT '使用时间',
  `use_scene` tinyint NULL DEFAULT NULL COMMENT '使用场景（1-停车缴费，2-VIP套餐购买）',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联订单号',
  `parking_lot_id` bigint NULL DEFAULT NULL COMMENT '使用停车场ID',
  `original_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '原始金额',
  `discount_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `final_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终支付金额',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_coupon_no`(`coupon_no`) USING BTREE,
  INDEX `idx_coupon_id`(`coupon_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_phone`(`phone_number`) USING BTREE,
  INDEX `idx_use_status`(`use_status`) USING BTREE,
  INDEX `idx_valid_time`(`valid_start_time`, `valid_end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_coupon_user
-- ----------------------------
INSERT INTO `mini_coupon_user` VALUES (1, 1, 1, NULL, 'CPN202401010001', '13800138001', '2024-01-15 10:30:00', '2024-01-15 10:30:00', '2025-09-27 23:59:59', 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '测试用户优惠券', '2025-06-12 19:54:09', '2025-06-12 22:41:55');
INSERT INTO `mini_coupon_user` VALUES (2, 2, 2, 1, 'CPN202401010002', '13800138002', '2024-01-16 14:20:00', '2024-01-16 14:20:00', '2024-06-30 23:59:59', 1, '2024-01-20 16:45:00', 1, 'ORDER20240120001', 1, 25.00, 5.00, 20.00, '已使用', '2025-06-12 19:54:09', '2025-06-12 19:54:09');
INSERT INTO `mini_coupon_user` VALUES (3, 3, NULL, 2, 'CPN202401010003', '13800138003', '2024-01-17 09:15:00', '2024-01-17 09:15:00', '2024-01-24 23:59:59', 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'VIP会员优惠券', '2025-06-12 19:54:09', '2025-06-12 22:29:49');

-- ----------------------------
-- Table structure for mini_gate_control_record
-- ----------------------------
DROP TABLE IF EXISTS `mini_gate_control_record`;
CREATE TABLE `mini_gate_control_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `device_id` bigint NOT NULL COMMENT '设备ID',
  `device_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备编码',
  `gate_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '栏位编号',
  `warehouse_id` bigint NOT NULL COMMENT '场库ID（关联mini_warehouse_complex.id）',
  `operation_type` int NOT NULL COMMENT '操作类型(0开闸 1关闸 2状态查询)',
  `plate_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号',
  `request_data` json NULL COMMENT '请求数据',
  `response_data` json NULL COMMENT '响应数据',
  `result_status` int NOT NULL COMMENT '操作结果(1成功 0失败)',
  `response_time` int NULL DEFAULT NULL COMMENT '响应时间(毫秒)',
  `error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误代码',
  `error_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `operator_id` bigint NULL DEFAULT NULL COMMENT '操作员ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员姓名',
  `operation_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作来源',
  `client_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_device_id`(`device_id`) USING BTREE,
  INDEX `idx_device_code`(`device_code`) USING BTREE,
  INDEX `idx_gate_no`(`gate_no`) USING BTREE,
  INDEX `idx_warehouse_id`(`warehouse_id`) USING BTREE,
  INDEX `idx_plate_no`(`plate_no`) USING BTREE,
  INDEX `idx_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_result_status`(`result_status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '道闸设备控制记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_gate_control_record
-- ----------------------------

-- ----------------------------
-- Table structure for mini_gate_device_status
-- ----------------------------
DROP TABLE IF EXISTS `mini_gate_device_status`;
CREATE TABLE `mini_gate_device_status`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '状态ID',
  `device_id` bigint NOT NULL COMMENT '设备ID',
  `device_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备编码',
  `gate_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '栏位编号',
  `online_status` int NOT NULL COMMENT '在线状态(1在线 0离线)',
  `gate_status` int NULL DEFAULT NULL COMMENT '道闸状态(0关闭 1开启)',
  `temperature` decimal(5, 2) NULL DEFAULT NULL COMMENT '设备温度',
  `voltage` decimal(6, 2) NULL DEFAULT NULL COMMENT '电压',
  `signal_strength` int NULL DEFAULT NULL COMMENT '信号强度',
  `last_heartbeat` datetime NULL DEFAULT NULL COMMENT '最后心跳时间',
  `status_data` json NULL COMMENT '状态数据',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_device_id`(`device_id`) USING BTREE,
  INDEX `idx_device_code`(`device_code`) USING BTREE,
  INDEX `idx_gate_no`(`gate_no`) USING BTREE,
  INDEX `idx_online_status`(`online_status`) USING BTREE,
  INDEX `idx_last_heartbeat`(`last_heartbeat`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '道闸设备状态监控表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_gate_device_status
-- ----------------------------

-- ----------------------------
-- Table structure for mini_gate_protocol_config
-- ----------------------------
DROP TABLE IF EXISTS `mini_gate_protocol_config`;
CREATE TABLE `mini_gate_protocol_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `manufacturer_id` bigint NOT NULL COMMENT '厂商ID',
  `oem_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '厂商代码',
  `protocol_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '协议类型',
  `api_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'API地址',
  `api_port` int NULL DEFAULT NULL COMMENT 'API端口',
  `auth_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '认证密钥',
  `timeout_ms` int NULL DEFAULT 5000 COMMENT '超时时间(毫秒)',
  `retry_times` int NULL DEFAULT 3 COMMENT '重试次数',
  `config_data` json NULL COMMENT '配置数据',
  `status` int NULL DEFAULT 1 COMMENT '状态(1启用 0禁用)',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_manufacturer_id`(`manufacturer_id`) USING BTREE,
  INDEX `idx_oem_code`(`oem_code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '道闸厂商协议配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_gate_protocol_config
-- ----------------------------
INSERT INTO `mini_gate_protocol_config` VALUES (1, 2, 'sizhuo', 'HTTP_JSON', 'http://qr.it-wy.cn:83', 83, '20231226105133354', 5000, 3, '{\"api_endpoints\": {\"saveCar\": \"/transmit/vehicle/save\", \"payOrder\": \"/transmit/pay/notice\", \"gateControl\": \"/gateControl\"}, \"sign_algorithm\": \"MD5\", \"parking_id_mapping\": {\"xilan\": \"20231226105133356\", \"yunle\": \"20231226105133355\", \"nicheng\": \"20231226105133354\"}}', 1, NULL, '2025-06-12 12:06:04', NULL, '2025-06-12 12:06:04', '司卓道闸协议配置，包含停车场ID映射和API端点');

-- ----------------------------
-- Table structure for mini_invoice_title
-- ----------------------------
DROP TABLE IF EXISTS `mini_invoice_title`;
CREATE TABLE `mini_invoice_title`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `invoice_type` int NOT NULL COMMENT '发票类型0普票,1专票',
  `title_type` int NULL DEFAULT NULL COMMENT '抬头类型0个人,1单位',
  `invoice_title_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
  `unit_duty_paragraph` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位税号',
  `register_address` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册地址',
  `register_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册电话',
  `deposit_bank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '银行账号',
  `audit_status` int NULL DEFAULT NULL COMMENT '专票审核状态',
  `delete_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发票抬头表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_invoice_title
-- ----------------------------

-- ----------------------------
-- Table structure for mini_merchant
-- ----------------------------
DROP TABLE IF EXISTS `mini_merchant`;
CREATE TABLE `mini_merchant`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `merchant_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商铺名称',
  `warehouse_id` bigint NULL DEFAULT NULL COMMENT '场库id',
  `business_license` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '营业执照',
  `head_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人姓名',
  `head_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人身份证',
  `head_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人手机号',
  `move_time` timestamp NULL DEFAULT NULL COMMENT '入住时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `delete_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商户管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_merchant
-- ----------------------------
INSERT INTO `mini_merchant` VALUES (16, '上海高派酒店管理有限公司', 723055983177371648, '42000000202311010154', '郭以琳', '411282200209297060', '15138197330', '2021-12-20 00:00:00', NULL, 0, 1, '2024-03-13 10:37:23', 1, '2024-03-13 10:37:23');
INSERT INTO `mini_merchant` VALUES (17, '上海市浦东新区泥城镇橘巷火锅店', 723055983177371648, '92310115MA1LA3115U', '马魁进', '320624197611243811', '13564013818', '2017-09-26 00:00:00', NULL, 0, 1, '2024-03-13 10:38:09', 1, '2024-03-13 10:38:09');
INSERT INTO `mini_merchant` VALUES (18, '仕爵俱乐部', 723055983177371648, '42000000202310230099', '徐丹军', '310225198908155617', '18701928760', '2021-05-07 00:00:00', NULL, 0, 1, '2024-03-13 10:38:44', 1, '2024-03-13 10:38:44');
INSERT INTO `mini_merchant` VALUES (19, '旅嘉酒店管理有限公司', 723055983177371648, '91310000MAD0H0LW9D', '谢秀豹', '330324198210086376', '18217550610', '2023-11-01 00:00:00', NULL, 0, 1, '2024-03-13 10:39:21', 1, '2024-03-13 10:39:21');
INSERT INTO `mini_merchant` VALUES (20, '上海遇寓酒店管理有限公司', 723055983177371648, '27000000201904040756', '车兴家', '34240119880510411X', '18616160966', '2016-01-28 00:00:00', NULL, 0, 1, '2024-03-13 10:39:59', 1, '2024-03-13 10:39:59');
INSERT INTO `mini_merchant` VALUES (21, 'shanghu', 723055983177371648, 'shanghu', 'shanghu', 'shanghu', '17521094340', '2024-08-06 00:00:00', NULL, 0, 149, '2024-08-06 14:24:47', 169, '2025-01-10 15:24:54');
INSERT INTO `mini_merchant` VALUES (22, 'test', 2, 'test', 'test', 'test', '13035101125', '2024-12-11 00:00:00', NULL, 1, 175, '2024-12-11 16:02:55', 175, '2024-12-11 16:07:19');
INSERT INTO `mini_merchant` VALUES (24, '李某某', 723055983177371648, '91723089127339812', '十分的', '36233020021236', '122', '2025-06-27 12:28:15', NULL, 1, NULL, '2025-06-27 12:28:16', NULL, '2025-06-27 12:28:16');
INSERT INTO `mini_merchant` VALUES (25, '虹桥火车', 723055983177371648, '************-9123', '李某某', '362330200212093778', '***********', '2025-06-27 12:33:20', NULL, 1, NULL, '2025-06-27 12:33:48', NULL, '2025-06-27 12:33:48');
INSERT INTO `mini_merchant` VALUES (26, '打卡苏', 723055983177371648, '19028312-7381', '11222', '362330200212093788', '***********', '2025-06-27 12:45:26', NULL, 0, 1, '2025-06-27 12:45:34', 1, '2025-06-27 12:45:48');

-- ----------------------------
-- Table structure for mini_merchant_package
-- ----------------------------
DROP TABLE IF EXISTS `mini_merchant_package`;
CREATE TABLE `mini_merchant_package`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `warehouse_id` bigint NOT NULL COMMENT '停车场id',
  `package_price` decimal(11, 2) NULL DEFAULT NULL COMMENT '价格',
  `package_type` int NOT NULL DEFAULT 0 COMMENT '套餐类型（0金额券 1次数券 2时长券）',
  `deduction_price` decimal(11, 2) NULL DEFAULT NULL COMMENT '抵扣价格',
  `package_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套餐标题',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `delete_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商户套餐信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_merchant_package
-- ----------------------------
INSERT INTO `mini_merchant_package` VALUES (22, 723055983177371648, 0.01, 0, 18.00, '消费停车优惠券', NULL, 0, 1, '2024-01-07 13:40:46', 169, '2024-12-03 15:02:15');
INSERT INTO `mini_merchant_package` VALUES (23, 4, 1.00, 0, 1.00, '1', '1', 1, 175, '2024-12-11 16:13:09', 175, '2024-12-11 16:16:27');
INSERT INTO `mini_merchant_package` VALUES (24, 2, 223.00, 0, 223.00, '223', '223', 1, 174, '2024-12-12 15:50:43', 174, '2024-12-12 15:53:29');
INSERT INTO `mini_merchant_package` VALUES (25, 71, 12.00, 0, 10.00, '1128', '1', 1, 169, '2025-01-10 15:26:23', 169, '2025-01-10 15:27:18');
INSERT INTO `mini_merchant_package` VALUES (26, 71, 1.00, 0, 1.00, '1', NULL, 1, 169, '2025-01-10 15:40:42', 169, '2025-01-10 15:40:46');

-- ----------------------------
-- Table structure for mini_operator
-- ----------------------------
DROP TABLE IF EXISTS `mini_operator`;
CREATE TABLE `mini_operator`  (
  `id` bigint UNSIGNED NOT NULL COMMENT '主键ID',
  `company_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '运营商公司名称',
  `primary_contact_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主要联系人姓名',
  `primary_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主要联系人电话',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
  `status` tinyint UNSIGNED NULL DEFAULT 1 COMMENT '状态 1-正常 0-停用',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `company_name`(`company_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '运营商信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_operator
-- ----------------------------
INSERT INTO `mini_operator` VALUES (1871825000000000001, '上海申能物业管理有限公司', '张凌珉', '13671994216', '负责临港一期、临港二期等多个项目的物业管理', 1, 0, 1, '2023-08-02 21:23:37', 179, '2025-05-01 20:49:14');
INSERT INTO `mini_operator` VALUES (1871825000000000002, '上海聚悦资产管理有限公司', '朱银银', '18621795580', '负责泥城长租、浦江一期、浦江二期、南桥二期等项目', 1, 0, 150, '2024-10-23 09:55:10', 149, '2024-10-23 18:51:37');
INSERT INTO `mini_operator` VALUES (1871825000000000003, '上海临港漕河泾物业服务有限公司', '陶灵丽', '15821130102', '负责鸿音广场项目', 1, 0, 150, '2024-10-23 13:28:57', 1, '2025-06-09 13:12:36');
INSERT INTO `mini_operator` VALUES (1871825000000000004, '百搭公司哦', '123', '15723459765', '12', 1, 1, 1, '2025-06-09 13:13:03', NULL, '2025-06-09 13:13:39');

-- ----------------------------
-- Table structure for mini_parking_order
-- ----------------------------
DROP TABLE IF EXISTS `mini_parking_order`;
CREATE TABLE `mini_parking_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `warehouse_id` bigint NULL DEFAULT NULL COMMENT '场库id',
  `parking_manage_id` bigint NULL DEFAULT NULL COMMENT '车位id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `plate_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号',
  `begin_parking_time` timestamp NULL DEFAULT NULL COMMENT '开始停车时间',
  `end_parking_time` timestamp NULL DEFAULT NULL COMMENT '结束停车时间',
  `parking_duration` int NULL DEFAULT NULL COMMENT '停车时长',
  `payment_amount` decimal(11, 2) NULL DEFAULT NULL COMMENT '缴费金额',
  `discount_amount` decimal(11, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `actual_payment` decimal(11, 2) NULL DEFAULT NULL COMMENT '实付金额',
  `pay_type` int NULL DEFAULT NULL COMMENT '支付方式',
  `parking_reservation_id` bigint NULL DEFAULT NULL COMMENT '预约id',
  `invoice_id` bigint NULL DEFAULT NULL COMMENT '发票id',
  `trade_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id（商户订单号）',
  `pay_status` int NOT NULL DEFAULT 1 COMMENT '订单支付状态（1进行中 2支付中 5已支付）',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '无牌用户标识',
  `car_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车辆类型 临时车 月租车 免费车',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint NULL DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `delete_flag_index`(`delete_flag`) USING BTREE,
  INDEX `warehouse_id_index`(`warehouse_id`) USING BTREE,
  INDEX `plate_no_index`(`plate_no`) USING BTREE,
  INDEX `pay_status_index`(`pay_status`) USING BTREE,
  INDEX `idx_order_delete_pay_create`(`delete_flag`, `pay_status`, `create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 729304044857856000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '停车订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_parking_order
-- ----------------------------
INSERT INTO `mini_parking_order` VALUES (681435, 723055983177371648, NULL, 722315301991092224, '赣A11111', '2025-06-20 16:00:00', '2025-06-20 22:25:58', 386, 20.00, 0.00, 20.00, 2, NULL, NULL, '37Y120250620222557741187848P', 1, NULL, 'oi_4B7EopE5aoD07esmrJuvCN5GA', '临时车', 0, NULL, '2025-06-20 22:25:57', NULL, '2025-06-20 22:25:57');
INSERT INTO `mini_parking_order` VALUES (725607654533373952, 723055983177371648, NULL, 722315301991092224, '沪S67445', '2025-06-25 13:17:19', '2025-06-25 15:48:26', 152, 3.00, 0.00, 3.00, 2, NULL, NULL, '37Y120250625154826053615127P', 1, NULL, 'oi_4B7EopE5aoD07esmrJuvCN5GA', '临时车', 0, NULL, '2025-06-25 15:05:54', NULL, '2025-06-25 15:05:54');
INSERT INTO `mini_parking_order` VALUES (725893887683731456, 723055983177371648, NULL, 722315301991092224, '沪AS1234', '2025-06-26 09:32:33', '2025-06-26 10:03:09', 31, 1.00, 0.00, 1.00, 2, NULL, NULL, '37Y120250626100309281065388P', 5, '2025-06-26 10:27:25', 'oi_4B7EopE5aoD07esmrJuvCN5GA', '临时车', 0, NULL, '2025-06-26 10:03:09', NULL, '2025-07-05 20:24:11');
INSERT INTO `mini_parking_order` VALUES (729304044857856000, 728441935903199232, NULL, NULL, '沪AS1231', '2025-07-05 00:00:00', '2025-07-31 00:00:00', 1231, 123.00, 3.00, 120.00, 0, NULL, NULL, NULL, 0, NULL, NULL, '临时车', 1, NULL, '2025-07-05 19:53:54', NULL, '2025-07-05 19:53:54');

-- ----------------------------
-- Table structure for mini_special_user
-- ----------------------------
DROP TABLE IF EXISTS `mini_special_user`;
CREATE TABLE `mini_special_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '特殊用户id',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `phone_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `plate_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号',
  `user_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户类型（集团客户 VIP客户）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
  `delete_flag` tinyint NOT NULL DEFAULT 0,
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_special_user_delete_flag`(`delete_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '特殊用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_special_user
-- ----------------------------
INSERT INTO `mini_special_user` VALUES (1, '张三', '15772687594', '赣A11112', 'VIP客户', '123', 0, 1, '2025-07-03 08:26:21', 1, '2025-07-03 08:39:12');
INSERT INTO `mini_special_user` VALUES (2, '里没', '15765987540', '赣A11110', '集团客户', '孙菲菲违法', 0, NULL, '2025-07-03 08:39:39', NULL, '2025-07-05 19:55:58');

-- ----------------------------
-- Table structure for mini_special_user_car
-- ----------------------------
DROP TABLE IF EXISTS `mini_special_user_car`;
CREATE TABLE `mini_special_user_car`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `special_user_id` bigint NOT NULL COMMENT '小程序用户id',
  `warehouse_id` bigint NOT NULL COMMENT '场库id',
  `phone_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `plate_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '车牌号',
  `delete_flag` tinyint NOT NULL DEFAULT 0,
  `create_by` bigint NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '特殊用户优惠车辆表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_special_user_car
-- ----------------------------
INSERT INTO `mini_special_user_car` VALUES (1, 1, 723055983177371648, '15772687594', '赣A11112', 0, NULL, '2025-07-16 11:24:28', NULL, '2025-07-16 11:24:28');
INSERT INTO `mini_special_user_car` VALUES (2, 2, 723055983177371648, '15765987540', '赣A11110', 0, NULL, '2025-07-16 11:24:28', NULL, '2025-07-16 11:24:28');
INSERT INTO `mini_special_user_car` VALUES (3, 1, 728295218234920960, '15772687594', '赣A11112', 0, NULL, '2025-07-16 11:24:28', NULL, '2025-07-16 11:24:28');

-- ----------------------------
-- Table structure for mini_union_pay_config
-- ----------------------------
DROP TABLE IF EXISTS `mini_union_pay_config`;
CREATE TABLE `mini_union_pay_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `warehouse_id` bigint NOT NULL COMMENT '停车场id',
  `pay_type` int NULL DEFAULT NULL COMMENT '支付方式（1小程序 2C扫B 3H5）',
  `mid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'mid-商户号',
  `tid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'tid-银联参数',
  `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 723799006991486976 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '银联支付参数表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_union_pay_config
-- ----------------------------
INSERT INTO `mini_union_pay_config` VALUES (723796006092804096, 723055983177371648, 1, '12893710237013112', '123123123123111', '1231', 0, 1, 1, '2025-06-20 15:06:55', '2025-07-02 23:35:08');
INSERT INTO `mini_union_pay_config` VALUES (723799006991486976, 723055983177371648, 1, '213142354345345', '45345353453', '1', 1, 1, NULL, '2025-06-20 15:18:50', '2025-07-02 23:35:00');

-- ----------------------------
-- Table structure for mini_vip_package
-- ----------------------------
DROP TABLE IF EXISTS `mini_vip_package`;
CREATE TABLE `mini_vip_package`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `warehouse_id` bigint NOT NULL COMMENT '场库ID',
  `package_type` int NOT NULL COMMENT '套餐类型',
  `vip_type` tinyint NOT NULL DEFAULT 0 COMMENT '会员类型（0普通会员 1集团客户 2VIP客户 3团购会员）',
  `package_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '套餐名称',
  `package_price` decimal(11, 2) NOT NULL COMMENT '套餐价格',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注信息',
  `package_status` int NOT NULL DEFAULT 1 COMMENT '套餐状态（1-启用，2-停用）',
  `delete_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `prefer_begin_time` timestamp NULL DEFAULT NULL COMMENT '优惠开始时间 (废弃字段，默认为NULL）',
  `prefer_end_time` timestamp NULL DEFAULT NULL COMMENT '优惠结束时间 (废弃字段，默认为NULL）',
  `park_type` int NULL DEFAULT 1 COMMENT '停车类型 (废弃字段，默认为1）',
  `give_days` int NULL DEFAULT 0 COMMENT '赠送天数 (废弃字段，默认为0）',
  `package_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '套餐规则(废弃字段）',
  `rule_type` int NULL DEFAULT 1 COMMENT '规则类型（废弃字段，默认为1）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1013 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '套餐信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_vip_package
-- ----------------------------
INSERT INTO `mini_vip_package` VALUES (80, 723055983177371648, 30, 1, '月卡套餐', 200.00, '月度停车套餐，30天有效期', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (81, 723055983177371648, 90, 2, '季卡套餐', 540.00, '季度停车套餐，90天有效期，赠送7天', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 7, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (82, 723055983177371648, 365, 3, '年卡套餐', 2000.00, '年度停车套餐，365天有效期，赠送30天', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 30, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (83, 723055983177371648, 30, 1, 'VIP月卡', 300.00, 'VIP月度套餐，含专属车位', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 3, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (84, 1871825000000001001, 30, 1, '标准月卡', 180.00, '鸿音广场标准月卡', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (85, 1871825000000001001, 90, 2, '优惠季卡', 500.00, '鸿音广场季度优惠套餐', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 5, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (86, 1871825000000001001, 365, 3, '超值年卡', 1800.00, '鸿音广场年度超值套餐', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:34', NULL, NULL, 1, 20, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (87, 1871825000000001002, 30, 1, '住户月卡', 150.00, 'LIN舍公寓住户专享月卡', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:34', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (88, 1871825000000001002, 90, 2, '住户季卡', 420.00, 'LIN舍公寓住户季卡', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:34', NULL, NULL, 1, 3, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (89, 1871825000000001003, 30, 1, '业主月卡', 120.00, '泽辉苑业主月度套餐', 1, 1, 1, '2025-07-02 19:43:49', NULL, '2025-07-03 14:34:34', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (91, 1871825000000002002, 30, 0, '著雨苑月卡', 160.00, '著雨苑住户月度套餐', 1, 1, 1, '2025-07-02 19:45:54', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (92, 1871825000000002004, 90, 2, '著雨苑季卡', 450.00, '著雨苑住户季度套餐', 1, 1, 1, '2025-07-02 19:45:54', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 5, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (93, 1871825000000001004, 365, 3, '著雨苑年卡', 1600.00, '著雨苑住户年度套餐', 1, 1, 1, '2025-07-02 19:45:54', NULL, '2025-07-03 14:34:27', NULL, NULL, 1, 20, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1001, 1871825000000002002, 30, 0, '江山路月卡', 0.01, '普通会员月度套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-11 09:54:50', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1002, 723055983177371648, 90, 1, '江山路季卡', 541.00, '集团客户季度套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-11 10:18:45', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1003, 723055983177371648, 365, 2, '江山路年卡', 2000.00, 'VIP客户年度套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1004, 1871825000000001001, 30, 0, '鸿音广场月卡', 180.00, '普通会员月度套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1005, 1871825000000001001, 90, 3, '鸿音广场团购季卡', 450.00, '团购会员季度套餐', 2, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1006, 1871825000000001001, 365, 2, '鸿音广场VIP年卡', 1800.00, 'VIP客户年度套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1007, 1871825000000001004, 30, 0, '著雨苑月卡', 160.00, '普通会员月度套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1008, 1871825000000001004, 90, 1, '著雨苑集团季卡', 420.00, '集团客户季度套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1009, 1871825000000001004, 180, 2, '著雨苑VIP半年卡', 900.00, 'VIP客户半年套餐', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1010, 1871825000000002001, 30, 0, '鸿音主停车场月卡', 150.00, '子场库普通会员月卡', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1011, 1871825000000002003, 90, 1, '泽辉苑主停车场季卡', 400.00, '子场库集团客户季卡', 2, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1012, 1871825000000002004, 365, 3, '著雨苑主停车场团购年卡', 1500.00, '子场库团购会员年卡', 1, 0, 1, '2025-07-03 15:09:00', NULL, '2025-07-03 15:09:00', NULL, NULL, 1, 0, NULL, 1);
INSERT INTO `mini_vip_package` VALUES (1013, 1871825000000001003, 3, 0, '123', 0.01, NULL, 1, 0, NULL, '2025-07-11 09:54:08', NULL, '2025-07-11 09:55:50', NULL, NULL, 1, 0, NULL, 1);

-- ----------------------------
-- Table structure for mini_vip_package_user_detail
-- ----------------------------
DROP TABLE IF EXISTS `mini_vip_package_user_detail`;
CREATE TABLE `mini_vip_package_user_detail`  (
  `id` bigint NOT NULL COMMENT '会员信息ID',
  `warehouse_id` bigint NULL DEFAULT NULL COMMENT '停车场ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '关联用户ID',
  `phone_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
  `plate_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '车牌号',
  `vip_type` tinyint NULL DEFAULT 0 COMMENT '会员类型（0普通会员 1集团客户 2VIP客户 3团购会员）',
  `begin_vip_time` timestamp NOT NULL COMMENT 'VIP开始时间',
  `end_vip_time` timestamp NOT NULL COMMENT 'VIP结束时间',
  `parking_space_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车位编号',
  `package_id` bigint NULL DEFAULT NULL COMMENT '套餐ID',
  `dly_system_id` bigint NULL DEFAULT 0 COMMENT '德立云系统id',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
  `delete_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员用户详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_vip_package_user_detail
-- ----------------------------
INSERT INTO `mini_vip_package_user_detail` VALUES (1001, 723055983177371648, 1001, '13800138001', '京A12345', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1001, 0, '江山路月卡会员', 0, 1, '2025-07-02 19:45:14', NULL, '2025-07-03 15:09:30');
INSERT INTO `mini_vip_package_user_detail` VALUES (1002, 723055983177371648, 1002, '13800138002', '京B67890', 2, '2024-11-01 00:00:00', '2025-01-31 23:59:59', NULL, 1002, 0, '江山路季卡会员', 0, 1, '2025-07-02 19:45:14', NULL, '2025-07-03 15:09:43');
INSERT INTO `mini_vip_package_user_detail` VALUES (1003, 723055983177371648, 1003, '13800138003', '京C11111', 3, '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, 1003, 0, '江山路年卡会员', 0, 1, '2025-07-02 19:45:14', NULL, '2025-07-03 15:09:51');
INSERT INTO `mini_vip_package_user_detail` VALUES (1004, 723055983177371648, 1004, '13800138004', '京D77777', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1001, 0, '江山路VIP月卡会员', 0, 1, '2025-07-02 19:45:14', NULL, '2025-07-03 15:09:57');
INSERT INTO `mini_vip_package_user_detail` VALUES (1005, 1871825000000001001, 1005, '13800138005', '沪A22222', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1004, 0, '鸿音广场月卡会员', 0, 1, '2025-07-02 19:45:14', NULL, '2025-07-03 15:10:04');
INSERT INTO `mini_vip_package_user_detail` VALUES (1006, 1871825000000001001, 1006, '13800138006', '沪B33333', 2, '2024-10-01 00:00:00', '2024-12-31 23:59:59', NULL, 1006, 0, '鸿音广场季卡会员', 0, 1, '2025-07-02 19:45:14', NULL, '2025-07-03 15:10:12');
INSERT INTO `mini_vip_package_user_detail` VALUES (1007, 1871825000000001001, 1007, '13800138007', '沪C88888', 3, '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, 1005, 0, '鸿音广场年卡会员', 0, 1, '2025-07-02 19:45:14', NULL, '2025-07-03 15:10:23');
INSERT INTO `mini_vip_package_user_detail` VALUES (1008, 1871825000000001002, 1008, '13800138008', '粤A44444', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1004, 0, 'LIN舍公寓住户会员', 0, 1, '2025-07-02 19:45:38', NULL, '2025-07-03 15:10:30');
INSERT INTO `mini_vip_package_user_detail` VALUES (1009, 1871825000000001002, 1009, '13800138009', '粤B99999', 2, '2024-10-01 00:00:00', '2024-12-31 23:59:59', NULL, 1006, 0, 'LIN舍公寓季卡住户', 0, 1, '2025-07-02 19:45:38', NULL, '2025-07-03 15:10:37');
INSERT INTO `mini_vip_package_user_detail` VALUES (1010, 1871825000000001003, 1010, '13800138010', '粤C55555', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1001, 0, '泽辉苑业主会员', 0, 1, '2025-07-02 19:45:38', NULL, '2025-07-03 15:10:44');
INSERT INTO `mini_vip_package_user_detail` VALUES (1011, 1871825000000001003, 1011, '13800138011', '粤D66666', 3, '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, 1003, 0, '泽辉苑年卡业主', 0, 1, '2025-07-02 19:45:38', NULL, '2025-07-03 15:10:51');
INSERT INTO `mini_vip_package_user_detail` VALUES (1012, 1871825000000001004, 1012, '13800138012', '川A77777', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1007, 0, '著雨苑临时会员', 0, 1, '2025-07-02 19:45:38', NULL, '2025-07-03 15:10:58');
INSERT INTO `mini_vip_package_user_detail` VALUES (1013, 1871825000000001004, 1016, '13800138016', '川B11111', 2, '2024-12-01 00:00:00', '2025-02-28 23:59:59', NULL, 1008, 0, '著雨苑季卡会员', 0, 1, '2025-07-02 19:48:14', NULL, '2025-07-03 15:11:04');
INSERT INTO `mini_vip_package_user_detail` VALUES (1014, 1871825000000001004, 1017, '13800138017', '川C22222', 3, '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, 1009, 0, '著雨苑年卡会员', 0, 1, '2025-07-02 19:48:14', NULL, '2025-07-03 15:11:10');
INSERT INTO `mini_vip_package_user_detail` VALUES (1015, 723055983177371648, 1018, '13800138018', '京G12345', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1001, 0, '江山路团购会员', 0, 1, '2025-07-02 19:51:49', NULL, '2025-07-03 15:11:17');
INSERT INTO `mini_vip_package_user_detail` VALUES (1016, 1871825000000001001, 1019, '13800138019', '沪E67890', 1, '2024-12-01 00:00:00', '2024-12-31 23:59:59', NULL, 1004, 0, '鸿音广场团购会员', 0, 1, '2025-07-02 19:51:49', NULL, '2025-07-03 15:11:24');
INSERT INTO `mini_vip_package_user_detail` VALUES (825000000001001, 22, 337, '18772928536', '京TEST02', 0, '2025-01-07 00:00:00', '2025-02-12 23:59:59', NULL, NULL, 0, NULL, 1, NULL, '2025-01-08 12:13:08', NULL, '2025-07-01 14:14:51');
INSERT INTO `mini_vip_package_user_detail` VALUES (8250000000010014, 1871825000000001001, 337, '13400000003', '京TEST03', 0, '2025-02-11 00:00:00', '2025-02-25 23:59:59', NULL, 1004, 0, NULL, 0, NULL, '2025-02-11 14:20:41', NULL, '2025-07-03 15:11:34');
INSERT INTO `mini_vip_package_user_detail` VALUES (82500000000100112, 1871825000000001001, NULL, '13400000004', '京TEST04', 0, '2025-02-11 14:45:26', '2025-02-15 23:59:59', NULL, 1004, 0, NULL, 0, NULL, '2025-02-11 14:44:28', NULL, '2025-07-03 15:11:40');
INSERT INTO `mini_vip_package_user_detail` VALUES (82500000000100123, 1871825000000001001, 75, '19720267939', '沪CCU019', 0, '2025-02-11 00:00:00', '2025-05-01 23:59:59', NULL, 1005, 0, NULL, 0, NULL, '2025-01-14 10:31:27', NULL, '2025-07-03 15:11:47');
INSERT INTO `mini_vip_package_user_detail` VALUES (82500000000100144, 1871825000000001001, NULL, '13400000005', '京TEST05', 0, '2025-02-11 00:00:00', '2025-02-11 23:59:59', NULL, 1004, 0, NULL, 0, NULL, '2025-02-11 14:46:39', NULL, '2025-07-03 15:11:52');
INSERT INTO `mini_vip_package_user_detail` VALUES (82500000000100155, 723055983177371648, NULL, NULL, '赣A11111', 0, '2025-07-01 13:21:40', '2025-07-31 00:00:00', NULL, 1001, 0, NULL, 0, NULL, '2025-07-01 13:22:32', NULL, '2025-07-03 15:12:00');
INSERT INTO `mini_vip_package_user_detail` VALUES (82500000000100166, 1871825000000001001, NULL, '13800000001', '测试001', 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', NULL, 1004, 0, NULL, 0, NULL, '2025-07-01 13:47:36', NULL, '2025-07-03 15:12:07');
INSERT INTO `mini_vip_package_user_detail` VALUES (82500000000100177, 723055983177371648, NULL, '15270342658', '沪S67445', 0, '2025-07-01 00:00:00', '2025-07-01 13:48:16', NULL, 1001, 0, '123', 0, NULL, '2025-07-01 13:48:24', NULL, '2025-07-03 15:12:13');

-- ----------------------------
-- Table structure for mini_vip_transact_record
-- ----------------------------
DROP TABLE IF EXISTS `mini_vip_transact_record`;
CREATE TABLE `mini_vip_transact_record`  (
  `id` bigint NOT NULL COMMENT '主键ID',
  `warehouse_id` bigint NOT NULL COMMENT '停车场ID',
  `package_id` bigint NULL DEFAULT NULL COMMENT '套餐ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `phone_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
  `plate_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '车牌号',
  `begin_vip_time` timestamp NOT NULL COMMENT 'VIP开始时间',
  `end_vip_time` timestamp NULL DEFAULT NULL COMMENT 'VIP结束时间',
  `package_price` decimal(11, 2) NOT NULL COMMENT '套餐价格',
  `discount_amount` decimal(11, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `actual_payment` decimal(11, 2) NULL DEFAULT NULL COMMENT '实际支付金额',
  `trade_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '交易ID',
  `pay_status` int NULL DEFAULT NULL COMMENT '支付状态（1-待支付，2-支付成功，3-支付失败）',
  `invoice_id` bigint NULL DEFAULT NULL COMMENT '发票ID',
  `parking_space_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '停车位号',
  `group_buy_record_id` bigint NULL DEFAULT NULL COMMENT '团购记录ID',
  `choose_time` timestamp NULL DEFAULT NULL COMMENT '选择时间',
  `delete_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人ID',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `vip_type` tinyint NULL DEFAULT 0 COMMENT '会员类型（0普通会员 1集团客户 2VIP客户 3团购会员）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_phone`(`phone_number`) USING BTREE,
  INDEX `idx_plate`(`plate_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员交易记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_vip_transact_record
-- ----------------------------
INSERT INTO `mini_vip_transact_record` VALUES (2001, 723055983177371648, 80, 1001, '13800138001', '京A12345', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 200.00, 0.00, 200.00, 'TXN202412010001', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:46:59', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2002, 723055983177371648, 81, 1002, '13800138002', '京B67890', '2024-11-01 00:00:00', '2025-01-31 23:59:59', 540.00, 40.00, 500.00, 'TXN202411010001', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:46:59', NULL, '2025-07-02 20:08:13', 2);
INSERT INTO `mini_vip_transact_record` VALUES (2003, 723055983177371648, 82, 1003, '13800138003', '京C11111', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 2000.00, 200.00, 1800.00, 'TXN202401010001', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:46:59', NULL, '2025-07-02 20:08:13', 3);
INSERT INTO `mini_vip_transact_record` VALUES (2004, 723055983177371648, 83, 1004, '13800138004', '京D77777', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 300.00, 0.00, 300.00, 'TXN202412010002', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:46:59', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2005, 1871825000000001001, 84, 1005, '13800138005', '沪A22222', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 180.00, 0.00, 180.00, 'TXN202412010003', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:46:59', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2006, 1871825000000001001, 85, 1006, '13800138006', '沪B33333', '2024-10-01 00:00:00', '2024-12-31 23:59:59', 500.00, 50.00, 450.00, 'TXN202410010001', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:46:59', NULL, '2025-07-02 20:08:13', 2);
INSERT INTO `mini_vip_transact_record` VALUES (2007, 1871825000000001001, 86, 1007, '13800138007', '沪C88888', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1800.00, 100.00, 1700.00, 'TXN202401010002', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:46:59', NULL, '2025-07-02 20:08:13', 3);
INSERT INTO `mini_vip_transact_record` VALUES (2008, 1871825000000001002, 87, 1008, '13800138008', '粤A44444', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 150.00, 0.00, 150.00, 'TXN202412010004', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:47:14', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2009, 1871825000000001002, 88, 1009, '13800138009', '粤B99999', '2024-10-01 00:00:00', '2024-12-31 23:59:59', 420.00, 20.00, 400.00, 'TXN202410010002', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:47:14', NULL, '2025-07-02 20:08:13', 2);
INSERT INTO `mini_vip_transact_record` VALUES (2010, 1871825000000001003, 89, 1010, '13800138010', '粤C55555', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 120.00, 0.00, 120.00, 'TXN202412010005', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:47:14', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2011, 1871825000000001003, 90, 1011, '13800138011', '粤D66666', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1200.00, 50.00, 1150.00, 'TXN202401010003', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:47:14', NULL, '2025-07-02 20:08:13', 3);
INSERT INTO `mini_vip_transact_record` VALUES (2012, 1871825000000001004, 91, 1012, '13800138012', '川A77777', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 160.00, 0.00, 160.00, 'TXN202412010006', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:47:14', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2013, 723055983177371648, 80, 1001, '13800138001', '京A12345', '2025-01-01 00:00:00', '2025-01-31 23:59:59', 200.00, 10.00, 190.00, 'TXN202501010001', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:47:14', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2014, 1871825000000001001, 84, 1005, '13800138005', '沪A22222', '2025-01-01 00:00:00', '2025-01-31 23:59:59', 180.00, 0.00, 180.00, 'TXN202501010002', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:47:14', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2015, 723055983177371648, 80, 1013, '13800138013', '京E88888', '2024-12-15 00:00:00', '2025-01-14 23:59:59', 200.00, 0.00, 200.00, 'TXN202412150001', 0, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:48:04', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2016, 1871825000000001001, 84, 1014, '13800138014', '沪D99999', '2024-12-15 00:00:00', '2025-01-14 23:59:59', 180.00, 0.00, 180.00, 'TXN202412150002', 0, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:48:04', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2017, 723055983177371648, 81, 1015, '13800138015', '京F00000', '2024-11-15 00:00:00', '2025-02-14 23:59:59', 540.00, 0.00, 540.00, 'TXN202411150001', 2, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:48:04', NULL, '2025-07-02 20:08:13', 2);
INSERT INTO `mini_vip_transact_record` VALUES (2018, 1871825000000001004, 92, 1016, '13800138016', '川B11111', '2024-12-01 00:00:00', '2025-02-28 23:59:59', 450.00, 30.00, 420.00, 'TXN202412010007', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:48:04', NULL, '2025-07-02 20:08:13', 2);
INSERT INTO `mini_vip_transact_record` VALUES (2019, 1871825000000001004, 93, 1017, '13800138017', '川C22222', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1600.00, 100.00, 1500.00, 'TXN202401010004', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:48:04', NULL, '2025-07-02 20:08:13', 3);
INSERT INTO `mini_vip_transact_record` VALUES (2020, 723055983177371648, 80, 1018, '13800138018', '京G12345', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 200.00, 50.00, 150.00, 'TXN202412010008', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:52:02', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (2021, 1871825000000001001, 84, 1019, '13800138019', '沪E67890', '2024-12-01 00:00:00', '2024-12-31 23:59:59', 180.00, 30.00, 150.00, 'TXN202412010009', 1, NULL, NULL, NULL, NULL, 0, 1, '2025-07-02 19:52:02', NULL, '2025-07-02 20:08:13', 1);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000001, 723055983177371648, 69, NULL, '13700137001', '沪F55555', '2024-12-25 08:00:00', '2024-12-26 08:00:00', 20.00, 0.00, 20.00, 'TXN202412250001', 0, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:32:34', NULL, '2025-07-01 18:37:40', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000002, 1871825000000001001, 74, NULL, '13700137002', '京F66666', '2024-12-25 10:30:00', '2024-12-26 10:30:00', 25.00, 5.00, 20.00, 'TXN202412250002', 2, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:32:34', NULL, '2025-07-01 18:37:56', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000003, 723055983177371648, 72, NULL, '13700137003', '沪G77777', '2024-12-24 15:45:00', '2025-03-24 15:45:00', 1200.00, 100.00, 1100.00, 'TXN202412240001', 3, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:32:34', NULL, '2025-07-01 18:38:32', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000004, 1871825000000001001, 77, NULL, '13900139001', '京C11111', '2024-11-20 09:15:00', '2024-12-20 09:15:00', 520.00, 20.00, 500.00, 'TXN202411200001', 5, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:31:47', NULL, '2025-07-01 18:39:02', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000005, 1871825000000001001, 78, NULL, '13900139002', '京D22222', '2024-10-10 16:45:00', '2024-12-10 16:45:00', 980.00, 80.00, 900.00, 'TXN202410100001', 5, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:31:47', NULL, '2025-07-01 18:39:24', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000006, 1871825000000001001, 75, NULL, '13900139003', '京E33333', '2024-12-20 11:20:00', '2024-12-27 11:20:00', 150.00, 10.00, 140.00, 'TXN202412200001', 5, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:31:47', NULL, '2025-07-01 18:39:40', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000007, 723055983177371648, 71, NULL, '13800138001', '沪A12345', '2024-12-01 10:00:00', '2025-01-01 10:00:00', 480.00, 50.00, 430.00, 'TXN202412010001', 5, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:31:29', NULL, '2025-07-01 18:39:59', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000008, 723055983177371648, 70, NULL, '13800138002', '沪B67890', '2024-12-15 14:30:00', '2024-12-22 14:30:00', 120.00, 0.00, 120.00, 'TXN202412150001', 5, NULL, NULL, NULL, NULL, 0, 1, '2025-07-01 18:31:29', NULL, '2025-07-01 19:02:05', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000009, 1871825000000001001, 4, 337, '18772928537', '京TEST31', '2025-01-02 17:12:07', '2026-01-04 23:59:59', 0.01, 0.01, 0.00, '37Y120250102171206882684192P', 5, NULL, '1', NULL, '2025-01-02 00:00:00', 0, NULL, '2025-01-02 17:12:06', NULL, '2025-07-01 19:02:23', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000010, 1871825000000001001, 4, 337, '18772928537', '苏DSA321', '2024-11-06 15:32:27', '2025-11-06 23:59:59', 0.00, 0.00, 0.00, '37Y120241106153226699429674P', 5, NULL, '1', NULL, NULL, 0, NULL, '2024-11-06 15:32:26', NULL, '2025-07-01 19:02:32', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000011, 1871825000000001001, 4, 325, '13117365200', '苏EFZ0015', '2024-08-09 14:57:02', '2024-09-09 23:59:59', 0.01, 0.00, 0.01, '37Y120240809145702361904384P', 5, NULL, '0', NULL, '2024-08-09 00:00:00', 0, NULL, '2024-08-09 14:57:02', NULL, '2025-07-01 19:02:40', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000012, 1871825000000001001, 69, 75, '19720267939', '苏AFX521', '2024-07-09 21:39:23', '2024-09-06 23:59:59', 6.01, 6.00, 6.01, '37Y1202407092139232653702250', 5, NULL, '0', NULL, NULL, 0, NULL, '2024-07-09 21:39:23', NULL, '2025-07-01 19:02:52', 0);
INSERT INTO `mini_vip_transact_record` VALUES (1751365200001000013, 1871825000000001001, 69, 75, '19720267939', '苏AFX521', '2024-07-09 17:48:21', '2024-09-05 23:59:59', 6.00, 6.00, 6.00, '37Y1202407091748213268462414', 5, NULL, '0', NULL, NULL, 0, NULL, '2024-07-09 17:48:21', NULL, '2025-07-01 19:03:03', 0);

-- ----------------------------
-- Table structure for mini_warehouse
-- ----------------------------
DROP TABLE IF EXISTS `mini_warehouse`;
CREATE TABLE `mini_warehouse`  (
  `id` bigint NOT NULL,
  `project_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父级ID，0表示顶级场库',
  `warehouse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '场库名称',
  `operator_id` bigint NULL DEFAULT NULL COMMENT '所属运营商id',
  `carousel_images` json NULL COMMENT '轮播图片路径数组',
  `smarts_type` int NOT NULL DEFAULT 0 COMMENT '智慧场库等级0普通场库 1G1智慧场库 2G2智慧场库 3G3智慧场库',
  `total_parking` int NULL DEFAULT NULL COMMENT '总车位数',
  `province_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
  `city_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
  `area_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区',
  `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `longitude` decimal(13, 7) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(13, 7) NULL DEFAULT NULL COMMENT '纬度',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `status` int NULL DEFAULT NULL COMMENT '0-未营业，1-已营业  ',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint NULL DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `vip_park_sign` tinyint NOT NULL DEFAULT 0 COMMENT 'vip车位管理标识0 未接入 1接入（废弃字段，默认为NULL）',
  `contact_info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系方式（废弃字段，默认为NULL）',
  `passage_num` int NULL DEFAULT NULL COMMENT '通道数（废弃字段，默认为0）',
  `charging_station_num` int NULL DEFAULT NULL COMMENT '充电桩数量（废弃字段，默认为0）',
  `reservation_num` int NULL DEFAULT NULL COMMENT '预约车位数(废弃字段，默认为0)',
  `responsible_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '责任人（废弃字段，默认为NULL）',
  `vendor_id` int NULL DEFAULT NULL COMMENT '道闸品牌ID，关联sys_dict_data(gate_vendor)',
  `physical_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物理场库ID，用于API调用',
  `protocol_config_id` bigint NULL DEFAULT NULL COMMENT '关联mini_gate_protocol_config表',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_vendor_id`(`vendor_id`) USING BTREE,
  INDEX `idx_physical_id`(`physical_id`) USING BTREE,
  INDEX `idx_protocol_config_id`(`protocol_config_id`) USING BTREE,
  INDEX `idx_delete_flag`(`delete_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_warehouse
-- ----------------------------
INSERT INTO `mini_warehouse` VALUES (723055983177371648, '江山路', 0, '江山路', 1871825000000000001, '[\"http://127.0.0.1:9300/statics/2025/06/19/banner1_20250619140541A001.png\"]', 0, 120, '310000', '310100', '310115', '额放松放松', 121.9197360, 30.8977990, '这是江山路的备注', 1, 1, 0, NULL, '2025-06-18 14:06:20', NULL, '2025-06-20 15:10:45', 0, '15240346295', 2, 0, 0, '张三', NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (728295218234920960, '123156', 1871825000000001001, '123', 1871825000000000001, NULL, 0, 5, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 0, NULL, '2025-07-03 01:05:11', NULL, '2025-07-03 10:47:06', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (728441884367785984, NULL, 1871825000000001001, '12313', 1871825000000000002, NULL, 0, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 0, NULL, '2025-07-03 10:47:59', NULL, '2025-07-03 10:47:59', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (728441935903199232, '1231', 0, '123123123', 1871825000000000003, NULL, 0, 5, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 0, NULL, '2025-07-03 10:48:11', NULL, '2025-07-03 12:45:12', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000001001, '鸿音广场', 0, '鸿音广场', 1871825000000000002, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, NULL, '2025-07-01 10:03:15', NULL, '2025-07-01 10:03:15', 0, NULL, NULL, NULL, NULL, NULL, 4, '1', 1);
INSERT INTO `mini_warehouse` VALUES (1871825000000001002, 'LIN舍公寓', 0, 'LIN舍公寓', 1871825000000000003, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, NULL, '2025-07-01 10:03:15', NULL, '2025-07-01 10:03:15', 0, NULL, NULL, NULL, NULL, NULL, 1, '2', 1);
INSERT INTO `mini_warehouse` VALUES (1871825000000001003, '泽辉苑', 0, '泽辉苑', 1871825000000000001, '[\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_53_20_20250703103942A005.png\"]', 0, 5, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 0, NULL, '2025-07-01 10:03:15', NULL, '2025-07-03 10:39:43', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000001004, '著雨苑', 0, '著雨苑', 1871825000000000002, '[\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_12_11_20250703103907A004.png\"]', 0, 5, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 0, NULL, '2025-07-01 10:03:15', NULL, '2025-07-03 10:39:10', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000001005, '林彩苑', 0, '林彩苑', 1871825000000000003, '[\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_30_24_20250703103900A003.png\"]', 0, 3, '120000', '120100', '120101', '213123123123123', NULL, NULL, NULL, 1, 1, 0, NULL, '2025-07-01 10:03:15', NULL, '2025-07-03 11:01:27', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000002001, NULL, 1871825000000001001, '鸿音广场-主停车场', 1871825000000000002, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, NULL, '2023-08-02 21:25:19', NULL, '2025-06-12 05:29:37', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000002002, NULL, 1871825000000001002, 'LIN舍公寓-主停车场', 1871825000000000003, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, NULL, '2023-08-04 22:52:49', NULL, '2025-06-12 05:29:43', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000002003, NULL, 1871825000000001003, '泽辉苑-主停车场', 1871825000000000001, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, NULL, '2024-05-21 15:21:59', NULL, '2025-06-12 05:29:49', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000002004, NULL, 1871825000000001004, '著雨苑-主停车场', 1871825000000000002, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, NULL, '2024-05-21 15:22:33', NULL, '2025-06-12 05:29:49', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mini_warehouse` VALUES (1871825000000002005, NULL, 1871825000000001005, '林彩苑-主停车场', 1871825000000000003, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, NULL, '2024-05-21 15:23:01', NULL, '2025-06-12 05:29:49', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for mini_warehouse_manager
-- ----------------------------
DROP TABLE IF EXISTS `mini_warehouse_manager`;
CREATE TABLE `mini_warehouse_manager`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operator_id` bigint UNSIGNED NOT NULL COMMENT '所属运营商ID',
  `warehouse_id` bigint UNSIGNED NOT NULL,
  `manager_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '管理人员姓名',
  `manager_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '管理人员电话',
  `is_primary` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否主要负责人 0-否 1-是',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
  `status` tinyint UNSIGNED NULL DEFAULT 1 COMMENT '状态 1-正常 0-停用',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `operator_id`(`operator_id`) USING BTREE,
  INDEX `warehouse_id`(`warehouse_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '场库管理人员信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_warehouse_manager
-- ----------------------------
INSERT INTO `mini_warehouse_manager` VALUES (1, 1871825000000000001, 723055983177371648, '张凌珉', '13671994216', 1, '负责临港一期凌波苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-19 14:47:18');
INSERT INTO `mini_warehouse_manager` VALUES (2, 1871825000000000001, 723055983177371648, '张凌珉', '13671994216', 1, '负责临港一期海云苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-19 14:32:19');
INSERT INTO `mini_warehouse_manager` VALUES (3, 1871825000000000001, 723055983177371648, '张凌珉', '13671994216', 1, '负责临港二期春晓苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-19 14:32:19');
INSERT INTO `mini_warehouse_manager` VALUES (4, 1871825000000000001, 723055983177371648, '张凌珉', '13671994216', 1, '负责临港二期雨浥苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-19 14:32:19');
INSERT INTO `mini_warehouse_manager` VALUES (5, 1871825000000000001, 723055983177371648, '沈晓春', '15821699203', 1, '负责临港三期新元盛璟苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-19 14:32:19');
INSERT INTO `mini_warehouse_manager` VALUES (6, 1, 4, '沈屹', '13524654733', 1, '负责临港四期泽辉苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (7, 1, 5, '沈屹', '13524654733', 1, '负责临港四期著雨苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (8, 1, 6, '沈屹', '13524654733', 1, '负责临港四期林彩苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (9, 1, 7, '沈屹', '13524654733', 1, '负责临港四期芳菲苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (10, 1, 25, '施俊华', '15901887959', 1, '负责临港五期熙景苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (11, 1, 26, '施俊华', '15901887959', 1, '负责临港五期新雨苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (12, 1, 27, '施俊华', '15901887959', 1, '负责临港五期清涛苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (13, 1, 28, '施俊华', '15901887959', 1, '负责临港五期露华苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (14, 1, 23, '刘燕', '13918541152', 1, '负责南桥一期汇丰名都', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (15, 1, 9, '沙从广', '18918765257', 1, '负责临港科技城云乐苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (16, 1, 10, '沙从广', '18918765257', 1, '负责临港科技城熙岚苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (17, 1, 11, '沙从广', '18918765257', 1, '负责临港科技城星凝苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (18, 1, 12, '沙从广', '18918765257', 1, '负责临港科技城月微苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (19, 1, 13, '沙从广', '18918765257', 1, '负责临港科技城朝露苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (20, 1, 22, '徐燕俊', '13916632734', 1, '负责松江公租房华亭茗苑', 1, 0, 1, '2025-06-09 12:54:54', 1, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (21, 2, 2, '朱银银', '18621795580', 1, '负责泥城长租LIN舍公寓', 1, 0, 150, '2025-06-09 12:54:54', 150, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (22, 2, 14, '陈蓉蓉', '18017393043', 1, '负责浦江一期新元畅想苑', 1, 0, 150, '2025-06-09 12:54:54', 150, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (23, 2, 15, '蒋琦', '13901609179', 1, '负责浦江二期新元理想苑', 1, 0, 150, '2025-06-09 12:54:54', 150, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (24, 2, 16, '宋林强', '18616167322', 1, '负责南桥二期广丰名都', 1, 0, 150, '2025-06-09 12:54:54', 150, '2025-06-09 12:54:54');
INSERT INTO `mini_warehouse_manager` VALUES (25, 3, 1, '陶灵丽', '15821130102', 1, '负责鸿音广场', 1, 0, 150, '2025-06-09 12:54:54', 150, '2025-06-09 12:54:54');

-- ----------------------------
-- Table structure for mini_whitelist
-- ----------------------------
DROP TABLE IF EXISTS `mini_whitelist`;
CREATE TABLE `mini_whitelist`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `warehouse_id` bigint NULL DEFAULT NULL COMMENT '关联场库ID',
  `phone_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车主手机号码',
  `begin_time` timestamp NULL DEFAULT NULL COMMENT '白名单生效起始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '白名单失效截止时间',
  `plate_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号码',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车主姓名',
  `dly_system_id` bigint NULL DEFAULT NULL COMMENT '第三方系统关联ID',
  `park_type` int NULL DEFAULT NULL COMMENT '停放区域类型(0=地面,1=地下)',
  `white_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '白名单类型(集团车辆/特殊优惠等)',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注说明',
  `delete_flag` tinyint NULL DEFAULT 0 COMMENT '软删除标记(0=正常,1=已删除)',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '最后修改人ID',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 379 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '车辆白名单管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mini_whitelist
-- ----------------------------
INSERT INTO `mini_whitelist` VALUES (372, 2, '59666661825', '2025-01-01 00:00:00', '2025-01-05 23:59:59', '京TEST01', '张斌', 0, 1, NULL, NULL, 1, 169, '2025-01-03 13:12:34', 169, '2025-01-03 13:31:51');
INSERT INTO `mini_whitelist` VALUES (373, 2, '15664771691', '2025-01-01 00:00:00', '2025-01-10 23:59:59', '京TEST01', '在', 0, 1, NULL, NULL, 1, 169, '2025-01-03 14:14:17', 169, '2025-01-03 14:16:43');
INSERT INTO `mini_whitelist` VALUES (374, 2, '12203221008', '2025-01-01 08:00:00', '2025-01-05 23:59:59', '京TEST01', '在', 0, 1, NULL, NULL, 1, 169, '2025-01-03 14:28:36', 169, '2025-01-03 14:30:15');
INSERT INTO `mini_whitelist` VALUES (375, 2, '33596386954', '2025-01-01 00:00:00', '2025-01-10 23:59:59', '京TEST01', '在', 0, 1, NULL, NULL, 1, 169, '2025-01-03 14:45:26', 169, '2025-01-03 14:46:02');
INSERT INTO `mini_whitelist` VALUES (376, 7, '15300919670', '2025-01-10 00:00:00', '2025-01-11 23:59:59', '1', '1', 0, 1, '集团车辆', NULL, 1, 149, '2025-01-10 13:39:02', 149, '2025-01-10 13:55:06');
INSERT INTO `mini_whitelist` VALUES (377, 2, '15301807490', '2025-01-01 00:00:00', '2025-01-12 23:59:59', 'zzb', 'zzbb', 0, 0, '集团车辆', '', 1, 169, '2025-01-10 15:42:44', 169, '2025-01-10 15:43:42');
INSERT INTO `mini_whitelist` VALUES (378, 22, '77703204387', '2025-01-30 00:00:00', '2025-01-31 23:59:59', 'z', 'z', 0, 0, '集团车辆', NULL, 1, 169, '2025-01-10 15:46:22', 169, '2025-01-10 15:46:40');
INSERT INTO `mini_whitelist` VALUES (379, 2, '79437598211', '2025-01-03 00:00:00', '2025-01-05 23:59:59', '京TEST01', '张斌', 0, 1, NULL, NULL, 1, 169, '2025-01-13 09:46:05', 169, '2025-01-13 09:46:26');

-- ----------------------------
-- Table structure for sys_area
-- ----------------------------
DROP TABLE IF EXISTS `sys_area`;
CREATE TABLE `sys_area`  (
  `area_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域代码',
  `area_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域名称',
  `parent_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级代码',
  `area_level` tinyint NOT NULL COMMENT '区域级别：1省份，2城市，3区县',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`area_code`) USING BTREE,
  INDEX `idx_parent_code`(`parent_code`) USING BTREE,
  INDEX `idx_area_level`(`area_level`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '行政区域表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_area
-- ----------------------------
INSERT INTO `sys_area` VALUES ('110000', '北京市', NULL, 1, 1, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('110100', '北京市', '110000', 2, 1, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('110101', '东城区', '110100', 3, 1, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110102', '西城区', '110100', 3, 2, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110105', '朝阳区', '110100', 3, 3, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110106', '丰台区', '110100', 3, 4, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110107', '石景山区', '110100', 3, 5, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110108', '海淀区', '110100', 3, 6, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110109', '门头沟区', '110100', 3, 7, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110111', '房山区', '110100', 3, 8, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110112', '通州区', '110100', 3, 9, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110113', '顺义区', '110100', 3, 10, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110114', '昌平区', '110100', 3, 11, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110115', '大兴区', '110100', 3, 12, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110116', '怀柔区', '110100', 3, 13, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110117', '平谷区', '110100', 3, 14, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110118', '密云区', '110100', 3, 15, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('110119', '延庆区', '110100', 3, 16, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('120000', '天津市', NULL, 1, 2, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('120100', '天津市', '120000', 2, 1, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('120101', '和平区', '120100', 3, 1, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120102', '河东区', '120100', 3, 2, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120103', '河西区', '120100', 3, 3, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120104', '南开区', '120100', 3, 4, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120105', '河北区', '120100', 3, 5, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120106', '红桥区', '120100', 3, 6, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120110', '东丽区', '120100', 3, 7, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120111', '西青区', '120100', 3, 8, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120112', '津南区', '120100', 3, 9, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120113', '北辰区', '120100', 3, 10, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120114', '武清区', '120100', 3, 11, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120115', '宝坻区', '120100', 3, 12, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120116', '滨海新区', '120100', 3, 13, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120117', '宁河区', '120100', 3, 14, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120118', '静海区', '120100', 3, 15, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('120119', '蓟州区', '120100', 3, 16, '0', '2025-06-18 12:32:38', '2025-06-18 12:32:38');
INSERT INTO `sys_area` VALUES ('130000', '河北省', NULL, 1, 3, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('130100', '石家庄市', '130000', 2, 1, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130200', '唐山市', '130000', 2, 2, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130300', '秦皇岛市', '130000', 2, 3, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130400', '邯郸市', '130000', 2, 4, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130500', '邢台市', '130000', 2, 5, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130600', '保定市', '130000', 2, 6, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130700', '张家口市', '130000', 2, 7, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130800', '承德市', '130000', 2, 8, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('130900', '沧州市', '130000', 2, 9, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('131000', '廊坊市', '130000', 2, 10, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('131100', '衡水市', '130000', 2, 11, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('140000', '山西省', NULL, 1, 4, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('140100', '太原市', '140000', 2, 1, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140200', '大同市', '140000', 2, 2, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140300', '阳泉市', '140000', 2, 3, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140400', '长治市', '140000', 2, 4, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140500', '晋城市', '140000', 2, 5, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140600', '朔州市', '140000', 2, 6, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140700', '晋中市', '140000', 2, 7, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140800', '运城市', '140000', 2, 8, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('140900', '忻州市', '140000', 2, 9, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('141000', '临汾市', '140000', 2, 10, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('141100', '吕梁市', '140000', 2, 11, '0', '2025-06-18 12:27:27', '2025-06-18 12:27:27');
INSERT INTO `sys_area` VALUES ('150000', '内蒙古自治区', NULL, 1, 5, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('150100', '呼和浩特市', '150000', 2, 1, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150200', '包头市', '150000', 2, 2, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150300', '乌海市', '150000', 2, 3, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150400', '赤峰市', '150000', 2, 4, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150500', '通辽市', '150000', 2, 5, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150600', '鄂尔多斯市', '150000', 2, 6, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150700', '呼伦贝尔市', '150000', 2, 7, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150800', '巴彦淖尔市', '150000', 2, 8, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('150900', '乌兰察布市', '150000', 2, 9, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('152200', '兴安盟', '150000', 2, 10, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('152500', '锡林郭勒盟', '150000', 2, 11, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('152900', '阿拉善盟', '150000', 2, 12, '0', '2025-06-18 12:27:38', '2025-06-18 12:27:38');
INSERT INTO `sys_area` VALUES ('210000', '辽宁省', NULL, 1, 6, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('210100', '沈阳市', '210000', 2, 1, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210200', '大连市', '210000', 2, 2, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210300', '鞍山市', '210000', 2, 3, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210400', '抚顺市', '210000', 2, 4, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210500', '本溪市', '210000', 2, 5, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210600', '丹东市', '210000', 2, 6, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210700', '锦州市', '210000', 2, 7, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210800', '营口市', '210000', 2, 8, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('210900', '阜新市', '210000', 2, 9, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('211000', '辽阳市', '210000', 2, 10, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('211100', '盘锦市', '210000', 2, 11, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('211200', '铁岭市', '210000', 2, 12, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('211300', '朝阳市', '210000', 2, 13, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('211400', '葫芦岛市', '210000', 2, 14, '0', '2025-06-18 12:27:48', '2025-06-18 12:27:48');
INSERT INTO `sys_area` VALUES ('220000', '吉林省', NULL, 1, 7, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('220100', '长春市', '220000', 2, 1, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('220200', '吉林市', '220000', 2, 2, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('220300', '四平市', '220000', 2, 3, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('220400', '辽源市', '220000', 2, 4, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('220500', '通化市', '220000', 2, 5, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('220600', '白山市', '220000', 2, 6, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('220700', '松原市', '220000', 2, 7, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('220800', '白城市', '220000', 2, 8, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('222400', '延边朝鲜族自治州', '220000', 2, 9, '0', '2025-06-18 12:27:56', '2025-06-18 12:27:56');
INSERT INTO `sys_area` VALUES ('230000', '黑龙江省', NULL, 1, 8, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('230100', '哈尔滨市', '230000', 2, 1, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230200', '齐齐哈尔市', '230000', 2, 2, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230300', '鸡西市', '230000', 2, 3, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230400', '鹤岗市', '230000', 2, 4, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230500', '双鸭山市', '230000', 2, 5, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230600', '大庆市', '230000', 2, 6, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230700', '伊春市', '230000', 2, 7, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230800', '佳木斯市', '230000', 2, 8, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('230900', '七台河市', '230000', 2, 9, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('231000', '牡丹江市', '230000', 2, 10, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('231100', '黑河市', '230000', 2, 11, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('231200', '绥化市', '230000', 2, 12, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('232700', '大兴安岭地区', '230000', 2, 13, '0', '2025-06-18 12:28:07', '2025-06-18 12:28:07');
INSERT INTO `sys_area` VALUES ('310000', '上海市', NULL, 1, 9, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('310100', '上海市', '310000', 2, 1, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('310101', '黄浦区', '310100', 3, 1, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310104', '徐汇区', '310100', 3, 2, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310105', '长宁区', '310100', 3, 3, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310106', '静安区', '310100', 3, 4, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310107', '普陀区', '310100', 3, 5, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310109', '虹口区', '310100', 3, 6, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310110', '杨浦区', '310100', 3, 7, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310112', '闵行区', '310100', 3, 8, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310113', '宝山区', '310100', 3, 9, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310114', '嘉定区', '310100', 3, 10, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310115', '浦东新区', '310100', 3, 11, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310116', '金山区', '310100', 3, 12, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310117', '松江区', '310100', 3, 13, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310118', '青浦区', '310100', 3, 14, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310120', '奉贤区', '310100', 3, 15, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('310151', '崇明区', '310100', 3, 16, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('320000', '江苏省', NULL, 1, 10, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('320100', '南京市', '320000', 2, 1, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320200', '无锡市', '320000', 2, 2, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320300', '徐州市', '320000', 2, 3, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320400', '常州市', '320000', 2, 4, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320500', '苏州市', '320000', 2, 5, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320600', '南通市', '320000', 2, 6, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320700', '连云港市', '320000', 2, 7, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320800', '淮安市', '320000', 2, 8, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('320900', '盐城市', '320000', 2, 9, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('321000', '扬州市', '320000', 2, 10, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('321100', '镇江市', '320000', 2, 11, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('321200', '泰州市', '320000', 2, 12, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('321300', '宿迁市', '320000', 2, 13, '0', '2025-06-18 12:28:16', '2025-06-18 12:28:16');
INSERT INTO `sys_area` VALUES ('330000', '浙江省', NULL, 1, 11, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('330100', '杭州市', '330000', 2, 1, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330200', '宁波市', '330000', 2, 2, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330300', '温州市', '330000', 2, 3, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330400', '嘉兴市', '330000', 2, 4, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330500', '湖州市', '330000', 2, 5, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330600', '绍兴市', '330000', 2, 6, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330700', '金华市', '330000', 2, 7, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330800', '衢州市', '330000', 2, 8, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('330900', '舟山市', '330000', 2, 9, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('331000', '台州市', '330000', 2, 10, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('331100', '丽水市', '330000', 2, 11, '0', '2025-06-18 12:28:26', '2025-06-18 12:28:26');
INSERT INTO `sys_area` VALUES ('340000', '安徽省', NULL, 1, 12, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('340100', '合肥市', '340000', 2, 1, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('340200', '芜湖市', '340000', 2, 2, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('340300', '蚌埠市', '340000', 2, 3, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('340400', '淮南市', '340000', 2, 4, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('340500', '马鞍山市', '340000', 2, 5, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('340600', '淮北市', '340000', 2, 6, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('340700', '铜陵市', '340000', 2, 7, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('340800', '安庆市', '340000', 2, 8, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341000', '黄山市', '340000', 2, 9, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341100', '滁州市', '340000', 2, 10, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341200', '阜阳市', '340000', 2, 11, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341300', '宿州市', '340000', 2, 12, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341500', '六安市', '340000', 2, 13, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341600', '亳州市', '340000', 2, 14, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341700', '池州市', '340000', 2, 15, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('341800', '宣城市', '340000', 2, 16, '0', '2025-06-18 12:28:37', '2025-06-18 12:28:37');
INSERT INTO `sys_area` VALUES ('350000', '福建省', NULL, 1, 13, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('350100', '福州市', '350000', 2, 1, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350200', '厦门市', '350000', 2, 2, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350300', '莆田市', '350000', 2, 3, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350400', '三明市', '350000', 2, 4, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350500', '泉州市', '350000', 2, 5, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350600', '漳州市', '350000', 2, 6, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350700', '南平市', '350000', 2, 7, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350800', '龙岩市', '350000', 2, 8, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('350900', '宁德市', '350000', 2, 9, '0', '2025-06-18 12:28:46', '2025-06-18 12:28:46');
INSERT INTO `sys_area` VALUES ('360000', '江西省', NULL, 1, 14, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('360100', '南昌市', '360000', 2, 1, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360200', '景德镇市', '360000', 2, 2, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360300', '萍乡市', '360000', 2, 3, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360400', '九江市', '360000', 2, 4, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360500', '新余市', '360000', 2, 5, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360600', '鹰潭市', '360000', 2, 6, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360700', '赣州市', '360000', 2, 7, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360800', '吉安市', '360000', 2, 8, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('360900', '宜春市', '360000', 2, 9, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('361000', '抚州市', '360000', 2, 10, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('361100', '上饶市', '360000', 2, 11, '0', '2025-06-18 12:28:55', '2025-06-18 12:28:55');
INSERT INTO `sys_area` VALUES ('370000', '山东省', NULL, 1, 15, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('370100', '济南市', '370000', 2, 1, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('370200', '青岛市', '370000', 2, 2, '0', '2025-06-18 11:56:51', '2025-06-18 11:56:51');
INSERT INTO `sys_area` VALUES ('370202', '市南区', '370200', 3, 1, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370203', '市北区', '370200', 3, 2, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370211', '黄岛区', '370200', 3, 3, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370212', '崂山区', '370200', 3, 4, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370213', '李沧区', '370200', 3, 5, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370214', '城阳区', '370200', 3, 6, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370215', '即墨区', '370200', 3, 7, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370281', '胶州市', '370200', 3, 8, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370283', '平度市', '370200', 3, 9, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370285', '莱西市', '370200', 3, 10, '0', '2025-06-18 11:57:04', '2025-06-18 11:57:04');
INSERT INTO `sys_area` VALUES ('370300', '淄博市', '370000', 2, 3, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('370400', '枣庄市', '370000', 2, 4, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('370500', '东营市', '370000', 2, 5, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('370600', '烟台市', '370000', 2, 6, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('370700', '潍坊市', '370000', 2, 7, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('370800', '济宁市', '370000', 2, 8, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('370900', '泰安市', '370000', 2, 9, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('371000', '威海市', '370000', 2, 10, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('371100', '日照市', '370000', 2, 11, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('371300', '临沂市', '370000', 2, 12, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('371400', '德州市', '370000', 2, 13, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('371500', '聊城市', '370000', 2, 14, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('371600', '滨州市', '370000', 2, 15, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('371700', '菏泽市', '370000', 2, 16, '0', '2025-06-18 12:29:08', '2025-06-18 12:29:08');
INSERT INTO `sys_area` VALUES ('410000', '河南省', NULL, 1, 16, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('410100', '郑州市', '410000', 2, 1, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410200', '开封市', '410000', 2, 2, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410300', '洛阳市', '410000', 2, 3, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410400', '平顶山市', '410000', 2, 4, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410500', '安阳市', '410000', 2, 5, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410600', '鹤壁市', '410000', 2, 6, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410700', '新乡市', '410000', 2, 7, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410800', '焦作市', '410000', 2, 8, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('410900', '濮阳市', '410000', 2, 9, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411000', '许昌市', '410000', 2, 10, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411100', '漯河市', '410000', 2, 11, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411200', '三门峡市', '410000', 2, 12, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411300', '南阳市', '410000', 2, 13, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411400', '商丘市', '410000', 2, 14, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411500', '信阳市', '410000', 2, 15, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411600', '周口市', '410000', 2, 16, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('411700', '驻马店市', '410000', 2, 17, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('419001', '济源市', '410000', 2, 18, '0', '2025-06-18 12:29:20', '2025-06-18 12:29:20');
INSERT INTO `sys_area` VALUES ('420000', '湖北省', NULL, 1, 17, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('420100', '武汉市', '420000', 2, 1, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('420200', '黄石市', '420000', 2, 2, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('420300', '十堰市', '420000', 2, 3, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('420500', '宜昌市', '420000', 2, 4, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('420600', '襄阳市', '420000', 2, 5, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('420700', '鄂州市', '420000', 2, 6, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('420800', '荆门市', '420000', 2, 7, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('420900', '孝感市', '420000', 2, 8, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('421000', '荆州市', '420000', 2, 9, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('421100', '黄冈市', '420000', 2, 10, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('421200', '咸宁市', '420000', 2, 11, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('421300', '随州市', '420000', 2, 12, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('429004', '仙桃市', '420000', 2, 13, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('429005', '潜江市', '420000', 2, 14, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('429006', '天门市', '420000', 2, 15, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('429021', '神农架林区', '420000', 2, 16, '0', '2025-06-18 12:29:31', '2025-06-18 12:29:31');
INSERT INTO `sys_area` VALUES ('430000', '湖南省', NULL, 1, 18, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('430100', '长沙市', '430000', 2, 1, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430200', '株洲市', '430000', 2, 2, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430300', '湘潭市', '430000', 2, 3, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430400', '衡阳市', '430000', 2, 4, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430500', '邵阳市', '430000', 2, 5, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430600', '岳阳市', '430000', 2, 6, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430700', '常德市', '430000', 2, 7, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430800', '张家界市', '430000', 2, 8, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('430900', '益阳市', '430000', 2, 9, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('431000', '郴州市', '430000', 2, 10, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('431100', '永州市', '430000', 2, 11, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('431200', '怀化市', '430000', 2, 12, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('431300', '娄底市', '430000', 2, 13, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('433100', '湘西土家族苗族自治州', '430000', 2, 14, '0', '2025-06-18 12:29:42', '2025-06-18 12:29:42');
INSERT INTO `sys_area` VALUES ('440000', '广东省', NULL, 1, 19, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('440100', '广州市', '440000', 2, 1, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440103', '荔湾区', '440100', 3, 1, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440104', '越秀区', '440100', 3, 2, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440105', '海珠区', '440100', 3, 3, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440106', '天河区', '440100', 3, 4, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440111', '白云区', '440100', 3, 5, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440112', '黄埔区', '440100', 3, 6, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440113', '番禺区', '440100', 3, 7, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440114', '花都区', '440100', 3, 8, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440115', '南沙区', '440100', 3, 9, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440117', '从化区', '440100', 3, 10, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440118', '增城区', '440100', 3, 11, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440200', '韶关市', '440000', 2, 2, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440300', '深圳市', '440000', 2, 3, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440303', '罗湖区', '440300', 3, 1, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440304', '福田区', '440300', 3, 2, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440305', '南山区', '440300', 3, 3, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440306', '宝安区', '440300', 3, 4, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440307', '龙岗区', '440300', 3, 5, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440308', '盐田区', '440300', 3, 6, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440309', '龙华区', '440300', 3, 7, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440310', '坪山区', '440300', 3, 8, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440311', '光明区', '440300', 3, 9, '0', '2025-06-18 11:56:00', '2025-06-18 11:56:00');
INSERT INTO `sys_area` VALUES ('440400', '珠海市', '440000', 2, 4, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440500', '汕头市', '440000', 2, 5, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440600', '佛山市', '440000', 2, 6, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440700', '江门市', '440000', 2, 7, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440800', '湛江市', '440000', 2, 8, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('440900', '茂名市', '440000', 2, 9, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441200', '肇庆市', '440000', 2, 10, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441300', '惠州市', '440000', 2, 11, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441400', '梅州市', '440000', 2, 12, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441500', '汕尾市', '440000', 2, 13, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441600', '河源市', '440000', 2, 14, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441700', '阳江市', '440000', 2, 15, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441800', '清远市', '440000', 2, 16, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('441900', '东莞市', '440000', 2, 17, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('442000', '中山市', '440000', 2, 18, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('445100', '潮州市', '440000', 2, 19, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('445200', '揭阳市', '440000', 2, 20, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('445300', '云浮市', '440000', 2, 21, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('450000', '广西壮族自治区', NULL, 1, 20, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('450100', '南宁市', '450000', 2, 1, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450200', '柳州市', '450000', 2, 2, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450300', '桂林市', '450000', 2, 3, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450400', '梧州市', '450000', 2, 4, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450500', '北海市', '450000', 2, 5, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450600', '防城港市', '450000', 2, 6, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450700', '钦州市', '450000', 2, 7, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450800', '贵港市', '450000', 2, 8, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('450900', '玉林市', '450000', 2, 9, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('451000', '百色市', '450000', 2, 10, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('451100', '贺州市', '450000', 2, 11, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('451200', '河池市', '450000', 2, 12, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('451300', '来宾市', '450000', 2, 13, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('451400', '崇左市', '450000', 2, 14, '0', '2025-06-18 12:29:52', '2025-06-18 12:29:52');
INSERT INTO `sys_area` VALUES ('460000', '海南省', NULL, 1, 21, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('460100', '海口市', '460000', 2, 1, '0', '2025-06-18 12:29:59', '2025-06-18 12:29:59');
INSERT INTO `sys_area` VALUES ('460200', '三亚市', '460000', 2, 2, '0', '2025-06-18 12:29:59', '2025-06-18 12:29:59');
INSERT INTO `sys_area` VALUES ('460300', '三沙市', '460000', 2, 3, '0', '2025-06-18 12:29:59', '2025-06-18 12:29:59');
INSERT INTO `sys_area` VALUES ('460400', '儋州市', '460000', 2, 4, '0', '2025-06-18 12:29:59', '2025-06-18 12:29:59');
INSERT INTO `sys_area` VALUES ('500000', '重庆市', NULL, 1, 22, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('500100', '重庆市', '500000', 2, 1, '0', '2025-06-18 11:55:46', '2025-06-18 11:55:46');
INSERT INTO `sys_area` VALUES ('500101', '万州区', '500100', 3, 1, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500102', '涪陵区', '500100', 3, 2, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500103', '渝中区', '500100', 3, 3, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500104', '大渡口区', '500100', 3, 4, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500105', '江北区', '500100', 3, 5, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500106', '沙坪坝区', '500100', 3, 6, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500107', '九龙坡区', '500100', 3, 7, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500108', '南岸区', '500100', 3, 8, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500109', '北碚区', '500100', 3, 9, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500110', '綦江区', '500100', 3, 10, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500111', '大足区', '500100', 3, 11, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500112', '渝北区', '500100', 3, 12, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500113', '巴南区', '500100', 3, 13, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500114', '黔江区', '500100', 3, 14, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500115', '长寿区', '500100', 3, 15, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500116', '江津区', '500100', 3, 16, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500117', '合川区', '500100', 3, 17, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500118', '永川区', '500100', 3, 18, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500119', '南川区', '500100', 3, 19, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500120', '璧山区', '500100', 3, 20, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500151', '铜梁区', '500100', 3, 21, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500152', '潼南区', '500100', 3, 22, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500153', '荣昌区', '500100', 3, 23, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500154', '开州区', '500100', 3, 24, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500155', '梁平区', '500100', 3, 25, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500156', '武隆区', '500100', 3, 26, '0', '2025-06-18 12:32:51', '2025-06-18 12:32:51');
INSERT INTO `sys_area` VALUES ('500200', '县', '500000', 2, 2, '0', '2025-06-18 12:30:06', '2025-06-18 12:30:06');
INSERT INTO `sys_area` VALUES ('510000', '四川省', NULL, 1, 23, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('510100', '成都市', '510000', 2, 1, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('510300', '自贡市', '510000', 2, 2, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('510400', '攀枝花市', '510000', 2, 3, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('510500', '泸州市', '510000', 2, 4, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('510600', '德阳市', '510000', 2, 5, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('510700', '绵阳市', '510000', 2, 6, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('510800', '广元市', '510000', 2, 7, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('510900', '遂宁市', '510000', 2, 8, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511000', '内江市', '510000', 2, 9, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511100', '乐山市', '510000', 2, 10, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511300', '南充市', '510000', 2, 11, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511400', '眉山市', '510000', 2, 12, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511500', '宜宾市', '510000', 2, 13, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511600', '广安市', '510000', 2, 14, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511700', '达州市', '510000', 2, 15, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511800', '雅安市', '510000', 2, 16, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('511900', '巴中市', '510000', 2, 17, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('512000', '资阳市', '510000', 2, 18, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('513200', '阿坝藏族羌族自治州', '510000', 2, 19, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('513300', '甘孜藏族自治州', '510000', 2, 20, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('513400', '凉山彝族自治州', '510000', 2, 21, '0', '2025-06-18 12:30:19', '2025-06-18 12:30:19');
INSERT INTO `sys_area` VALUES ('520000', '贵州省', NULL, 1, 24, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('520100', '贵阳市', '520000', 2, 1, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('520200', '六盘水市', '520000', 2, 2, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('520300', '遵义市', '520000', 2, 3, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('520400', '安顺市', '520000', 2, 4, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('520500', '毕节市', '520000', 2, 5, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('520600', '铜仁市', '520000', 2, 6, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('522300', '黔西南布依族苗族自治州', '520000', 2, 7, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('522600', '黔东南苗族侗族自治州', '520000', 2, 8, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('522700', '黔南布依族苗族自治州', '520000', 2, 9, '0', '2025-06-18 12:30:28', '2025-06-18 12:30:28');
INSERT INTO `sys_area` VALUES ('530000', '云南省', NULL, 1, 25, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('530100', '昆明市', '530000', 2, 1, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('530300', '曲靖市', '530000', 2, 2, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('530400', '玉溪市', '530000', 2, 3, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('530500', '保山市', '530000', 2, 4, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('530600', '昭通市', '530000', 2, 5, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('530700', '丽江市', '530000', 2, 6, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('530800', '普洱市', '530000', 2, 7, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('530900', '临沧市', '530000', 2, 8, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('532300', '楚雄彝族自治州', '530000', 2, 9, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('532500', '红河哈尼族彝族自治州', '530000', 2, 10, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('532600', '文山壮族苗族自治州', '530000', 2, 11, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('532800', '西双版纳傣族自治州', '530000', 2, 12, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('532900', '大理白族自治州', '530000', 2, 13, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('533100', '德宏傣族景颇族自治州', '530000', 2, 14, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('533300', '怒江傈僳族自治州', '530000', 2, 15, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('533400', '迪庆藏族自治州', '530000', 2, 16, '0', '2025-06-18 12:30:42', '2025-06-18 12:30:42');
INSERT INTO `sys_area` VALUES ('540000', '西藏自治区', NULL, 1, 26, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('540100', '拉萨市', '540000', 2, 1, '0', '2025-06-18 12:30:49', '2025-06-18 12:30:49');
INSERT INTO `sys_area` VALUES ('540200', '日喀则市', '540000', 2, 2, '0', '2025-06-18 12:30:49', '2025-06-18 12:30:49');
INSERT INTO `sys_area` VALUES ('540300', '昌都市', '540000', 2, 3, '0', '2025-06-18 12:30:49', '2025-06-18 12:30:49');
INSERT INTO `sys_area` VALUES ('540400', '林芝市', '540000', 2, 4, '0', '2025-06-18 12:30:49', '2025-06-18 12:30:49');
INSERT INTO `sys_area` VALUES ('540500', '山南市', '540000', 2, 5, '0', '2025-06-18 12:30:49', '2025-06-18 12:30:49');
INSERT INTO `sys_area` VALUES ('542400', '那曲市', '540000', 2, 6, '0', '2025-06-18 12:30:49', '2025-06-18 12:30:49');
INSERT INTO `sys_area` VALUES ('542500', '阿里地区', '540000', 2, 7, '0', '2025-06-18 12:30:49', '2025-06-18 12:30:49');
INSERT INTO `sys_area` VALUES ('610000', '陕西省', NULL, 1, 27, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('610100', '西安市', '610000', 2, 1, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610200', '铜川市', '610000', 2, 2, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610300', '宝鸡市', '610000', 2, 3, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610400', '咸阳市', '610000', 2, 4, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610500', '渭南市', '610000', 2, 5, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610600', '延安市', '610000', 2, 6, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610700', '汉中市', '610000', 2, 7, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610800', '榆林市', '610000', 2, 8, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('610900', '安康市', '610000', 2, 9, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('611000', '商洛市', '610000', 2, 10, '0', '2025-06-18 12:31:33', '2025-06-18 12:31:33');
INSERT INTO `sys_area` VALUES ('620000', '甘肃省', NULL, 1, 28, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('620100', '兰州市', '620000', 2, 1, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620200', '嘉峪关市', '620000', 2, 2, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620300', '金昌市', '620000', 2, 3, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620400', '白银市', '620000', 2, 4, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620500', '天水市', '620000', 2, 5, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620600', '武威市', '620000', 2, 6, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620700', '张掖市', '620000', 2, 7, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620800', '平凉市', '620000', 2, 8, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('620900', '酒泉市', '620000', 2, 9, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('621000', '庆阳市', '620000', 2, 10, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('621100', '定西市', '620000', 2, 11, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('621200', '陇南市', '620000', 2, 12, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('622900', '临夏回族自治州', '620000', 2, 13, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('623000', '甘南藏族自治州', '620000', 2, 14, '0', '2025-06-18 12:31:44', '2025-06-18 12:31:44');
INSERT INTO `sys_area` VALUES ('630000', '青海省', NULL, 1, 29, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('630100', '西宁市', '630000', 2, 1, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('630200', '海东市', '630000', 2, 2, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('632200', '海北藏族自治州', '630000', 2, 3, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('632300', '黄南藏族自治州', '630000', 2, 4, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('632500', '海南藏族自治州', '630000', 2, 5, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('632600', '果洛藏族自治州', '630000', 2, 6, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('632700', '玉树藏族自治州', '630000', 2, 7, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('632800', '海西蒙古族藏族自治州', '630000', 2, 8, '0', '2025-06-18 12:31:53', '2025-06-18 12:31:53');
INSERT INTO `sys_area` VALUES ('640000', '宁夏回族自治区', NULL, 1, 30, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('640100', '银川市', '640000', 2, 1, '0', '2025-06-18 12:32:00', '2025-06-18 12:32:00');
INSERT INTO `sys_area` VALUES ('640200', '石嘴山市', '640000', 2, 2, '0', '2025-06-18 12:32:00', '2025-06-18 12:32:00');
INSERT INTO `sys_area` VALUES ('640300', '吴忠市', '640000', 2, 3, '0', '2025-06-18 12:32:00', '2025-06-18 12:32:00');
INSERT INTO `sys_area` VALUES ('640400', '固原市', '640000', 2, 4, '0', '2025-06-18 12:32:00', '2025-06-18 12:32:00');
INSERT INTO `sys_area` VALUES ('640500', '中卫市', '640000', 2, 5, '0', '2025-06-18 12:32:00', '2025-06-18 12:32:00');
INSERT INTO `sys_area` VALUES ('650000', '新疆维吾尔自治区', NULL, 1, 31, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('650100', '乌鲁木齐市', '650000', 2, 1, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('650200', '克拉玛依市', '650000', 2, 2, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('650400', '吐鲁番市', '650000', 2, 3, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('650500', '哈密市', '650000', 2, 4, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('652300', '昌吉回族自治州', '650000', 2, 5, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('652700', '博尔塔拉蒙古自治州', '650000', 2, 6, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('652800', '巴音郭楞蒙古自治州', '650000', 2, 7, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('652900', '阿克苏地区', '650000', 2, 8, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('653000', '克孜勒苏柯尔克孜自治州', '650000', 2, 9, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('653100', '喀什地区', '650000', 2, 10, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('653200', '和田地区', '650000', 2, 11, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('654000', '伊犁哈萨克自治州', '650000', 2, 12, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('654200', '塔城地区', '650000', 2, 13, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('654300', '阿勒泰地区', '650000', 2, 14, '0', '2025-06-18 12:32:11', '2025-06-18 12:32:11');
INSERT INTO `sys_area` VALUES ('710000', '台湾省', NULL, 1, 32, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('810000', '香港特别行政区', NULL, 1, 33, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');
INSERT INTO `sys_area` VALUES ('820000', '澳门特别行政区', NULL, 1, 34, '0', '2025-06-18 11:55:33', '2025-06-18 11:55:33');

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:21:59', '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:21:59', '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:21:59', '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:21:59', '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:21:59', '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (6, '用户管理-初始密码修改策略', 'sys.account.initPasswordModify', '1', 'Y', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:21:59', '0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框');
INSERT INTO `sys_config` VALUES (7, '用户管理-账号密码更新周期', 'sys.account.passwordValidateDays', '0', 'Y', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:21:59', '密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 110 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '临港捷运', 0, '若依', '15888888888', '<EMAIL>', '0', 0, 1, '2025-06-04 11:01:40', 1, '2025-06-04 14:23:05');
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '深圳总公司', 1, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (102, 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (103, 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (105, 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', 2, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:23:15');
INSERT INTO `sys_dept` VALUES (110, 100, '0,100', '111', 0, NULL, NULL, NULL, '0', 0, 1, '2025-07-02 11:04:53', 1, '2025-07-02 23:23:15');

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 273 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '停用状态');
INSERT INTO `sys_dict_data` VALUES (100, 1, '生效', '1', 'advert_status', '', 'primary', 'Y', '0', 1, '2025-06-06 13:52:17', 1, '2025-07-02 23:24:07', '广告生效状态');
INSERT INTO `sys_dict_data` VALUES (101, 2, '失效', '2', 'advert_status', '', 'danger', 'N', '0', 1, '2025-06-06 13:52:17', 1, '2025-07-02 23:24:07', '广告失效状态');
INSERT INTO `sys_dict_data` VALUES (102, 1, '正式环境', 'prod', 'manufacturer_env_type', '', 'success', 'Y', '0', 1, '2025-06-11 11:59:12', 1, '2025-07-02 23:24:07', '生产环境API地址');
INSERT INTO `sys_dict_data` VALUES (103, 2, '测试环境', 'test', 'manufacturer_env_type', '', 'warning', 'N', '0', 1, '2025-06-11 11:59:12', 1, '2025-07-02 23:24:07', '测试环境API地址');
INSERT INTO `sys_dict_data` VALUES (104, 1, '营业中', '1', 'warehouse_status', '', 'success', 'Y', '0', 1, '2025-06-18 11:57:19', 1, '2025-07-02 23:24:07', '场库正常营业状态');
INSERT INTO `sys_dict_data` VALUES (105, 2, '未营业', '0', 'warehouse_status', '', 'warning', 'N', '0', 1, '2025-06-18 11:57:19', 1, '2025-06-18 12:01:16', '场库未营业状态');
INSERT INTO `sys_dict_data` VALUES (106, 1, '普通场库', '0', 'warehouse_smart_level', '', 'info', 'Y', '0', 1, '2025-06-18 11:57:34', 1, '2025-07-02 23:24:07', '普通停车场库');
INSERT INTO `sys_dict_data` VALUES (107, 2, '智能场库', '1', 'warehouse_smart_level', '', 'primary', 'N', '0', 1, '2025-06-18 11:57:34', 1, '2025-07-02 23:24:07', '具备基础智能功能的场库');
INSERT INTO `sys_dict_data` VALUES (108, 3, '智慧场库', '2', 'warehouse_smart_level', '', 'success', 'N', '0', 1, '2025-06-18 11:57:34', 1, '2025-07-02 23:24:07', '高度智能化的场库');
INSERT INTO `sys_dict_data` VALUES (111, 1, '待处理', '0', 'exception_order_handle_status', '', 'danger', 'Y', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单待处理状态');
INSERT INTO `sys_dict_data` VALUES (112, 2, '处理中', '1', 'exception_order_handle_status', '', 'warning', 'N', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单处理中状态');
INSERT INTO `sys_dict_data` VALUES (113, 3, '已处理', '2', 'exception_order_handle_status', '', 'success', 'N', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单已处理状态');
INSERT INTO `sys_dict_data` VALUES (114, 4, '已忽略', '3', 'exception_order_handle_status', '', 'info', 'N', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单已忽略状态');
INSERT INTO `sys_dict_data` VALUES (115, 1, '支付异常', '1', 'exception_order_type', '', 'danger', 'Y', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '支付相关异常');
INSERT INTO `sys_dict_data` VALUES (116, 2, '时间异常', '2', 'exception_order_type', '', 'warning', 'N', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '时间计算异常');
INSERT INTO `sys_dict_data` VALUES (117, 3, '金额异常', '3', 'exception_order_type', '', 'info', 'N', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '金额计算异常');
INSERT INTO `sys_dict_data` VALUES (118, 4, '其他异常', '4', 'exception_order_type', '', 'primary', 'N', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '其他类型异常');
INSERT INTO `sys_dict_data` VALUES (119, 1, '进行中', '0', 'parking_order_status', '', 'warning', 'Y', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '车辆已入场，订单进行中');
INSERT INTO `sys_dict_data` VALUES (120, 2, '已完成', '1', 'parking_order_status', '', 'success', 'N', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '车辆已出场，订单已完成');
INSERT INTO `sys_dict_data` VALUES (121, 3, '已取消', '2', 'parking_order_status', '', 'danger', 'N', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '订单已取消');
INSERT INTO `sys_dict_data` VALUES (122, 4, '异常订单', '3', 'parking_order_status', '', 'info', 'N', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '存在异常的订单');
INSERT INTO `sys_dict_data` VALUES (130, 1, '地上停车场', '1', 'parking_lot_type', '', 'primary', 'Y', '0', 1, '2025-06-20 15:26:51', 1, '2025-07-02 23:24:07', '地上停车场类型');
INSERT INTO `sys_dict_data` VALUES (131, 2, '地下停车场', '2', 'parking_lot_type', '', 'success', 'N', '0', 1, '2025-06-20 15:26:51', 1, '2025-07-02 23:24:07', '地下停车场类型');
INSERT INTO `sys_dict_data` VALUES (132, 1, '正常使用', '1', 'parking_lot_status', '', 'success', 'Y', '0', 1, '2025-06-20 15:27:05', 1, '2025-07-02 23:24:07', '停车场正常使用状态');
INSERT INTO `sys_dict_data` VALUES (133, 2, '暂停使用', '2', 'parking_lot_status', '', 'warning', 'N', '0', 1, '2025-06-20 15:27:05', 1, '2025-07-02 23:24:07', '停车场暂停使用状态');
INSERT INTO `sys_dict_data` VALUES (134, 3, '维护中', '3', 'parking_lot_status', '', 'danger', 'N', '0', 1, '2025-06-20 15:27:05', 1, '2025-07-02 23:24:07', '停车场维护中状态');
INSERT INTO `sys_dict_data` VALUES (140, 1, '启用', '1', 'vip_package_status', '', 'success', 'Y', '0', 1, '2025-06-20 15:29:15', 1, '2025-07-02 23:24:07', '套餐启用状态');
INSERT INTO `sys_dict_data` VALUES (141, 2, '停用', '2', 'vip_package_status', '', 'warning', 'N', '0', 1, '2025-06-20 15:29:15', 1, '2025-07-02 23:24:07', '套餐停用状态');
INSERT INTO `sys_dict_data` VALUES (143, 1, '满减券', '1', 'coupon_type', '', 'success', 'Y', '0', 1, '2025-06-20 15:29:29', 1, '2025-07-02 23:24:07', '满减优惠券');
INSERT INTO `sys_dict_data` VALUES (144, 2, '折扣券', '2', 'coupon_type', '', 'warning', 'N', '0', 1, '2025-06-20 15:29:29', 1, '2025-07-02 23:24:07', '折扣优惠券');
INSERT INTO `sys_dict_data` VALUES (145, 3, '无门槛减免券', '3', 'coupon_type', '', 'info', 'N', '0', 1, '2025-06-20 15:29:29', 1, '2025-07-02 23:24:07', '无门槛减免券');
INSERT INTO `sys_dict_data` VALUES (146, 4, '免费停车券', '4', 'coupon_type', '', 'danger', 'N', '0', 1, '2025-06-20 15:29:29', 1, '2025-07-02 23:24:07', '免费停车券');
INSERT INTO `sys_dict_data` VALUES (147, 1, '待发放', '1', 'coupon_status', '', 'info', 'Y', '0', 1, '2025-06-20 15:29:42', 1, '2025-07-02 23:24:07', '优惠券待发放状态');
INSERT INTO `sys_dict_data` VALUES (148, 2, '发放中', '2', 'coupon_status', '', 'success', 'N', '0', 1, '2025-06-20 15:29:42', 1, '2025-07-02 23:24:07', '优惠券发放中状态');
INSERT INTO `sys_dict_data` VALUES (149, 3, '已结束', '3', 'coupon_status', '', 'danger', 'N', '0', 1, '2025-06-20 15:29:42', 1, '2025-07-02 23:24:07', '优惠券已结束状态');
INSERT INTO `sys_dict_data` VALUES (150, 4, '已暂停', '4', 'coupon_status', '', 'warning', 'N', '0', 1, '2025-06-20 15:29:42', 1, '2025-07-02 23:24:07', '优惠券已暂停状态');
INSERT INTO `sys_dict_data` VALUES (151, 1, '未使用', '0', 'coupon_use_status', '', 'info', 'Y', '0', 1, '2025-06-20 15:37:09', 1, '2025-07-02 23:24:07', '优惠券未使用状态');
INSERT INTO `sys_dict_data` VALUES (152, 2, '已使用', '1', 'coupon_use_status', '', 'success', 'N', '0', 1, '2025-06-20 15:37:09', 1, '2025-07-02 23:24:07', '优惠券已使用状态');
INSERT INTO `sys_dict_data` VALUES (153, 3, '已过期', '2', 'coupon_use_status', '', 'danger', 'N', '0', 1, '2025-06-20 15:37:09', 1, '2025-07-02 23:24:07', '优惠券已过期状态');
INSERT INTO `sys_dict_data` VALUES (154, 4, '已作废', '3', 'coupon_use_status', '', 'warning', 'N', '0', 1, '2025-06-20 15:37:09', 1, '2025-07-02 23:24:07', '优惠券已作废状态');
INSERT INTO `sys_dict_data` VALUES (155, 1, '停车缴费', '1', 'coupon_use_scene', '', 'primary', 'Y', '0', 1, '2025-06-20 15:37:27', 1, '2025-07-02 23:24:07', '停车缴费场景');
INSERT INTO `sys_dict_data` VALUES (156, 2, 'VIP套餐购买', '2', 'coupon_use_scene', '', 'success', 'N', '0', 1, '2025-06-20 15:37:27', 1, '2025-07-02 23:24:07', 'VIP套餐购买场景');
INSERT INTO `sys_dict_data` VALUES (157, 1, '正常', '1', 'operator_status', '', 'success', 'Y', '0', 1, '2025-06-20 15:37:41', 1, '2025-07-02 23:24:07', '运营商正常状态');
INSERT INTO `sys_dict_data` VALUES (158, 2, '停用', '0', 'operator_status', '', 'danger', 'N', '0', 1, '2025-06-20 15:37:41', 1, '2025-07-02 23:24:07', '运营商停用状态');
INSERT INTO `sys_dict_data` VALUES (159, 0, '未支付', '0', 'pay_status', '', 'warning', 'Y', '0', 1, '2025-06-20 15:42:07', 1, '2025-06-26 10:13:47', '未支付状态');
INSERT INTO `sys_dict_data` VALUES (160, 5, '已支付', '5', 'pay_status', '', 'success', 'N', '0', 1, '2025-06-20 15:42:07', 1, '2025-06-26 10:12:53', '已支付状态');
INSERT INTO `sys_dict_data` VALUES (161, 3, '支付失败', '3', 'pay_status', '', 'danger', 'N', '0', 1, '2025-06-20 15:42:07', 1, '2025-06-26 10:14:36', '支付失败状态');
INSERT INTO `sys_dict_data` VALUES (162, 4, '已退款', '4', 'pay_status', '', 'info', 'N', '0', 1, '2025-06-20 15:42:07', 1, '2025-06-26 10:14:40', '已退款状态');
INSERT INTO `sys_dict_data` VALUES (163, 2, '微信支付', '2', 'pay_method', '', 'success', 'Y', '0', 1, '2025-06-20 15:42:20', 1, '2025-06-26 10:09:57', '微信支付方式');
INSERT INTO `sys_dict_data` VALUES (164, 1, '支付宝', '1', 'pay_method', '', 'primary', 'N', '0', 1, '2025-06-20 15:42:20', 1, '2025-06-26 10:09:51', '支付宝支付方式');
INSERT INTO `sys_dict_data` VALUES (165, 3, '现金', '3', 'pay_method', '', 'warning', 'N', '0', 1, '2025-06-20 15:42:20', 1, '2025-07-02 23:24:07', '现金支付方式');
INSERT INTO `sys_dict_data` VALUES (166, 4, '免费', '4', 'pay_method', '', 'info', 'N', '0', 1, '2025-06-20 15:42:20', 1, '2025-07-02 23:24:07', '免费支付方式');
INSERT INTO `sys_dict_data` VALUES (167, 0, '银联支付', '0', 'pay_method', '', 'info', 'N', '0', 1, '2025-06-26 09:27:35', 1, '2025-07-02 23:24:07', '银联支付方式');
INSERT INTO `sys_dict_data` VALUES (168, 1, '低', '1', 'priority_level', '', 'info', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '低优先级');
INSERT INTO `sys_dict_data` VALUES (169, 2, '中', '2', 'priority_level', '', 'warning', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '中优先级');
INSERT INTO `sys_dict_data` VALUES (170, 3, '高', '3', 'priority_level', '', 'danger', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '高优先级');
INSERT INTO `sys_dict_data` VALUES (171, 4, '紧急', '4', 'priority_level', '', 'danger', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '紧急优先级');
INSERT INTO `sys_dict_data` VALUES (172, 1, '系统自动检测', '1', 'source_type', '', 'primary', 'N', '0', 1, '2025-06-26 09:31:00', 1, '2025-07-02 23:24:07', '系统自动检测');
INSERT INTO `sys_dict_data` VALUES (173, 2, '人工上报', '2', 'source_type', '', 'warning', 'N', '0', 1, '2025-06-26 09:31:00', 1, '2025-07-02 23:24:07', '人工上报');
INSERT INTO `sys_dict_data` VALUES (174, 3, '用户投诉', '3', 'source_type', '', 'danger', 'N', '0', 1, '2025-06-26 09:31:00', 1, '2025-07-02 23:24:07', '用户投诉');
INSERT INTO `sys_dict_data` VALUES (175, 1, '进行中', '1', 'pay_status', NULL, 'primary', 'N', '0', 1, '2025-06-26 10:13:26', 1, '2025-06-26 10:13:40', '进行中的状态');
INSERT INTO `sys_dict_data` VALUES (176, 2, '支付中', '2', 'pay_status', NULL, 'warning', 'N', '0', 1, '2025-06-26 10:14:22', 1, '2025-07-02 23:24:07', '支付中状态');
INSERT INTO `sys_dict_data` VALUES (177, 1, '普通车牌', '1', 'plate_type', '', 'primary', 'N', '0', 1, '2025-06-26 10:29:04', 1, '2025-07-02 23:24:07', '7位普通车牌');
INSERT INTO `sys_dict_data` VALUES (178, 2, '新能源车牌', '2', 'plate_type', '', 'success', 'N', '0', 1, '2025-06-26 10:29:04', 1, '2025-07-02 23:24:07', '8位新能源车牌');
INSERT INTO `sys_dict_data` VALUES (182, 1, '金额券', '0', 'merchant_package_type', '', 'primary', 'Y', '0', 1, '2025-06-30 16:01:53', 1, '2025-07-02 23:24:07', '金额券套餐');
INSERT INTO `sys_dict_data` VALUES (183, 2, '次数券', '1', 'merchant_package_type', '', 'success', 'N', '0', 1, '2025-06-30 16:02:01', 1, '2025-07-02 23:24:07', '次数券套餐');
INSERT INTO `sys_dict_data` VALUES (184, 3, '时长券', '2', 'merchant_package_type', '', 'warning', 'N', '0', 1, '2025-06-30 16:02:11', 1, '2025-07-02 23:24:07', '时长券套餐');
INSERT INTO `sys_dict_data` VALUES (186, 1, '一天', '1', 'vip_package_type', '', 'primary', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '1天套餐');
INSERT INTO `sys_dict_data` VALUES (187, 2, '三天', '3', 'vip_package_type', '', 'primary', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '3天套餐');
INSERT INTO `sys_dict_data` VALUES (188, 3, '五天', '5', 'vip_package_type', '', 'primary', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '5天套餐');
INSERT INTO `sys_dict_data` VALUES (189, 4, '七天', '7', 'vip_package_type', '', 'success', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '7天套餐');
INSERT INTO `sys_dict_data` VALUES (190, 5, '十天', '10', 'vip_package_type', '', 'success', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '10天套餐');
INSERT INTO `sys_dict_data` VALUES (191, 6, '十五天', '15', 'vip_package_type', '', 'success', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '15天套餐');
INSERT INTO `sys_dict_data` VALUES (192, 7, '包月', '30', 'vip_package_type', '', 'info', 'Y', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '30天套餐');
INSERT INTO `sys_dict_data` VALUES (193, 8, '两个月', '60', 'vip_package_type', '', 'info', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '60天套餐');
INSERT INTO `sys_dict_data` VALUES (194, 9, '包季', '90', 'vip_package_type', '', 'warning', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '90天套餐');
INSERT INTO `sys_dict_data` VALUES (195, 10, '半年', '180', 'vip_package_type', '', 'warning', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '180天套餐');
INSERT INTO `sys_dict_data` VALUES (196, 11, '九个月', '270', 'vip_package_type', '', 'warning', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '270天套餐');
INSERT INTO `sys_dict_data` VALUES (197, 12, '包年', '365', 'vip_package_type', '', 'danger', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '365天套餐');
INSERT INTO `sys_dict_data` VALUES (198, 13, '自定义天数', '0', 'vip_package_type', '', 'default', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '自定义天数套餐，管理员可设置');
INSERT INTO `sys_dict_data` VALUES (199, 2, '集团客户', '1', 'vip_member_type', '', 'info', 'Y', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 01:04:49', '集团客户');
INSERT INTO `sys_dict_data` VALUES (200, 3, 'VIP客户', '2', 'vip_member_type', '', 'primary', 'N', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 00:37:52', 'VIP客户');
INSERT INTO `sys_dict_data` VALUES (201, 4, '团购会员', '3', 'vip_member_type', '', 'success', 'N', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 00:38:04', '团购会员');
INSERT INTO `sys_dict_data` VALUES (202, 1, '普通会员', '0', 'vip_member_type', '', 'warning', 'N', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 00:37:19', '普通会员');
INSERT INTO `sys_dict_data` VALUES (203, 1, '正常', '0', 'wx_user_status', '', 'success', 'Y', '0', 1, '2025-07-04 10:18:44', NULL, '2025-07-04 10:18:44', '正常状态');
INSERT INTO `sys_dict_data` VALUES (204, 2, '停用', '1', 'wx_user_status', '', 'danger', 'N', '0', 1, '2025-07-04 10:18:44', NULL, '2025-07-04 10:18:44', '停用状态');
INSERT INTO `sys_dict_data` VALUES (205, 1, '普通用户', '0', 'wx_user_type', '', 'primary', 'Y', '0', 1, '2025-07-04 10:18:57', NULL, '2025-07-04 10:18:57', '普通用户');
INSERT INTO `sys_dict_data` VALUES (206, 2, '集团客户', '1', 'wx_user_type', '', 'warning', 'N', '0', 1, '2025-07-04 10:18:57', NULL, '2025-07-04 10:18:57', '集团客户');
INSERT INTO `sys_dict_data` VALUES (207, 3, 'VIP客户', '2', 'wx_user_type', '', 'success', 'N', '0', 1, '2025-07-04 10:18:57', NULL, '2025-07-04 10:18:57', 'VIP客户');
INSERT INTO `sys_dict_data` VALUES (208, 1, '道闸控制', '1', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'GATE_CONTROL');
INSERT INTO `sys_dict_data` VALUES (209, 2, '无牌车进场', '2', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'NO_PLATE_ENTRY');
INSERT INTO `sys_dict_data` VALUES (210, 3, '车辆登记', '3', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'VEHICLE_REGISTRATION');
INSERT INTO `sys_dict_data` VALUES (211, 4, '支付处理', '4', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'PAYMENT_PROCESSING');
INSERT INTO `sys_dict_data` VALUES (212, 5, '车辆删除', '5', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'VEHICLE_DELETION');
INSERT INTO `sys_dict_data` VALUES (213, 6, '健康检查', '6', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'HEALTH_CHECK');
INSERT INTO `sys_dict_data` VALUES (214, 7, '黑名单管理', '7', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'BLACKLIST_MANAGEMENT');
INSERT INTO `sys_dict_data` VALUES (215, 8, '白名单管理', '8', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'WHITELIST_MANAGEMENT');
INSERT INTO `sys_dict_data` VALUES (216, 1, '司卓道闸', '1', 'gate_vendor', '', 'primary', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'SIZHUO');
INSERT INTO `sys_dict_data` VALUES (217, 2, '稳畅道闸', '2', 'gate_vendor', '', 'success', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'WENCHANG');
INSERT INTO `sys_dict_data` VALUES (218, 3, '捷顺道闸', '3', 'gate_vendor', '', 'warning', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'JIESHUN');
INSERT INTO `sys_dict_data` VALUES (219, 4, '泓音道闸', '4', 'gate_vendor', '', 'info', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'HONGYIN');
INSERT INTO `sys_dict_data` VALUES (220, 5, '欣创园道闸', '5', 'gate_vendor', '', 'danger', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'XINCHUANGYUAN');
INSERT INTO `sys_dict_data` VALUES (221, 6, '立方道闸', '6', 'gate_vendor', '', 'default', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'LIFANG');
INSERT INTO `sys_dict_data` VALUES (222, 1, '司卓-道闸控制', '/transmit/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_1');
INSERT INTO `sys_dict_data` VALUES (223, 2, '司卓-无牌车进场', '/transmit/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_2');
INSERT INTO `sys_dict_data` VALUES (224, 3, '司卓-车辆登记', '/transmit/vehicle/save', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_3');
INSERT INTO `sys_dict_data` VALUES (225, 4, '司卓-支付处理', '/transmit/pay/notice', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_4');
INSERT INTO `sys_dict_data` VALUES (226, 5, '司卓-车辆删除', '/transmit/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_5');
INSERT INTO `sys_dict_data` VALUES (227, 6, '司卓-健康检查', '/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_6');
INSERT INTO `sys_dict_data` VALUES (228, 7, '司卓-黑名单管理', '/transmit/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_7');
INSERT INTO `sys_dict_data` VALUES (229, 8, '司卓-白名单管理', '/transmit/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_8');
INSERT INTO `sys_dict_data` VALUES (230, 11, '稳畅-道闸控制', '/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_1');
INSERT INTO `sys_dict_data` VALUES (231, 12, '稳畅-无牌车进场', '/vehicle/noplate', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_2');
INSERT INTO `sys_dict_data` VALUES (232, 13, '稳畅-车辆登记', '/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_3');
INSERT INTO `sys_dict_data` VALUES (233, 14, '稳畅-支付处理', '/payment/process', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_4');
INSERT INTO `sys_dict_data` VALUES (234, 15, '稳畅-车辆删除', '/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_5');
INSERT INTO `sys_dict_data` VALUES (235, 16, '稳畅-健康检查', '/health/check', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_6');
INSERT INTO `sys_dict_data` VALUES (236, 17, '稳畅-黑名单管理', '/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_7');
INSERT INTO `sys_dict_data` VALUES (237, 18, '稳畅-白名单管理', '/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_8');
INSERT INTO `sys_dict_data` VALUES (238, 21, '捷顺-道闸控制', '/api/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_1');
INSERT INTO `sys_dict_data` VALUES (239, 22, '捷顺-无牌车进场', '/api/vehicle/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_2');
INSERT INTO `sys_dict_data` VALUES (240, 23, '捷顺-车辆登记', '/api/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_3');
INSERT INTO `sys_dict_data` VALUES (241, 24, '捷顺-支付处理', '/api/payment/notify', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_4');
INSERT INTO `sys_dict_data` VALUES (242, 25, '捷顺-车辆删除', '/api/vehicle/remove', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_5');
INSERT INTO `sys_dict_data` VALUES (243, 26, '捷顺-健康检查', '/api/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_6');
INSERT INTO `sys_dict_data` VALUES (244, 27, '捷顺-黑名单管理', '/api/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_7');
INSERT INTO `sys_dict_data` VALUES (245, 28, '捷顺-白名单管理', '/api/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_8');
INSERT INTO `sys_dict_data` VALUES (246, 31, '泓音-道闸控制', '/parking/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_1');
INSERT INTO `sys_dict_data` VALUES (247, 32, '泓音-无牌车进场', '/parking/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_2');
INSERT INTO `sys_dict_data` VALUES (248, 33, '泓音-车辆登记', '/parking/vehicle/save', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_3');
INSERT INTO `sys_dict_data` VALUES (249, 34, '泓音-支付处理', '/parking/payment/notify', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_4');
INSERT INTO `sys_dict_data` VALUES (250, 35, '泓音-车辆删除', '/parking/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_5');
INSERT INTO `sys_dict_data` VALUES (251, 36, '泓音-健康检查', '/parking/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_6');
INSERT INTO `sys_dict_data` VALUES (252, 37, '泓音-黑名单管理', '/parking/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_7');
INSERT INTO `sys_dict_data` VALUES (253, 38, '泓音-白名单管理', '/parking/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_8');
INSERT INTO `sys_dict_data` VALUES (254, 41, '欣创园-道闸控制', '/xcy/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_1');
INSERT INTO `sys_dict_data` VALUES (255, 42, '欣创园-无牌车进场', '/xcy/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_2');
INSERT INTO `sys_dict_data` VALUES (256, 43, '欣创园-车辆登记', '/xcy/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_3');
INSERT INTO `sys_dict_data` VALUES (257, 44, '欣创园-支付处理', '/xcy/payment/process', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_4');
INSERT INTO `sys_dict_data` VALUES (258, 45, '欣创园-车辆删除', '/xcy/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_5');
INSERT INTO `sys_dict_data` VALUES (259, 46, '欣创园-健康检查', '/xcy/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_6');
INSERT INTO `sys_dict_data` VALUES (260, 47, '欣创园-黑名单管理', '/xcy/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_7');
INSERT INTO `sys_dict_data` VALUES (261, 48, '欣创园-白名单管理', '/xcy/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_8');
INSERT INTO `sys_dict_data` VALUES (262, 51, '立方-道闸控制', '/lifang/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_1');
INSERT INTO `sys_dict_data` VALUES (263, 52, '立方-无牌车进场', '/lifang/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_2');
INSERT INTO `sys_dict_data` VALUES (264, 53, '立方-车辆登记', '/lifang/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_3');
INSERT INTO `sys_dict_data` VALUES (265, 54, '立方-支付处理', '/lifang/payment/notify', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_4');
INSERT INTO `sys_dict_data` VALUES (266, 55, '立方-车辆删除', '/lifang/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_5');
INSERT INTO `sys_dict_data` VALUES (267, 56, '立方-健康检查', '/lifang/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_6');
INSERT INTO `sys_dict_data` VALUES (268, 57, '立方-黑名单管理', '/lifang/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_7');
INSERT INTO `sys_dict_data` VALUES (269, 58, '立方-白名单管理', '/lifang/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_8');
INSERT INTO `sys_dict_data` VALUES (270, 1, '未识别车牌', '0', 'error_data_code', '', 'danger', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '车牌识别失败');
INSERT INTO `sys_dict_data` VALUES (271, 2, '入场时无出场记录', '1', 'error_data_code', '', 'warning', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '入场时发现无对应出场记录');
INSERT INTO `sys_dict_data` VALUES (272, 3, '出场时无入场记录', '2', 'error_data_code', '', 'warning', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '出场时发现无对应入场记录');
INSERT INTO `sys_dict_data` VALUES (273, 4, '收费异常', '3', 'error_data_code', '', 'danger', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '停车费用计算或收取异常');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 134 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:56', '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (100, '广告状态', 'advert_status', '0', 1, '2025-06-06 13:52:02', 1, '2025-07-02 23:24:56', '广告配置状态列表');
INSERT INTO `sys_dict_type` VALUES (101, '厂商环境类型', 'manufacturer_env_type', '0', 1, '2025-06-11 11:59:04', 1, '2025-07-02 23:24:56', '道闸厂商API环境类型：正式环境、测试环境');
INSERT INTO `sys_dict_type` VALUES (102, '场库状态', 'warehouse_status', '0', 1, '2025-06-18 11:57:11', 1, '2025-07-02 23:24:56', '场库营业状态');
INSERT INTO `sys_dict_type` VALUES (103, '智慧场库等级', 'warehouse_smart_level', '0', 1, '2025-06-18 11:57:25', 1, '2025-07-02 23:24:56', '智慧场库等级分类');
INSERT INTO `sys_dict_type` VALUES (105, '异常订单处理状态', 'exception_order_handle_status', '0', 1, '2025-06-19 15:04:29', 1, '2025-07-02 23:24:56', '异常订单处理状态列表');
INSERT INTO `sys_dict_type` VALUES (106, '异常订单类型', 'exception_order_type', '0', 1, '2025-06-19 15:04:44', 1, '2025-07-02 23:24:56', '异常订单类型列表');
INSERT INTO `sys_dict_type` VALUES (107, '停车订单状态', 'parking_order_status', '0', 1, '2025-06-19 15:05:02', 1, '2025-07-02 23:24:56', '停车订单状态列表');
INSERT INTO `sys_dict_type` VALUES (110, '停车场类型', 'parking_lot_type', '0', 1, '2025-06-20 15:26:42', 1, '2025-07-02 23:24:56', '停车场类型分类');
INSERT INTO `sys_dict_type` VALUES (111, '停车场状态', 'parking_lot_status', '0', 1, '2025-06-20 15:26:57', 1, '2025-07-02 23:24:56', '停车场使用状态');
INSERT INTO `sys_dict_type` VALUES (112, 'VIP套餐类型', 'vip_package_type', '0', 1, '2025-06-20 15:28:50', 1, '2025-07-02 23:24:56', 'VIP套餐类型分类');
INSERT INTO `sys_dict_type` VALUES (113, 'VIP套餐状态', 'vip_package_status', '0', 1, '2025-06-20 15:29:08', 1, '2025-07-02 23:24:56', 'VIP套餐状态');
INSERT INTO `sys_dict_type` VALUES (114, '优惠券类型', 'coupon_type', '0', 1, '2025-06-20 15:29:21', 1, '2025-07-02 23:24:56', '优惠券类型分类');
INSERT INTO `sys_dict_type` VALUES (115, '优惠券状态', 'coupon_status', '0', 1, '2025-06-20 15:29:35', 1, '2025-07-02 23:24:56', '优惠券发放状态');
INSERT INTO `sys_dict_type` VALUES (116, '优惠券使用状态', 'coupon_use_status', '0', 1, '2025-06-20 15:37:00', 1, '2025-07-02 23:24:56', '优惠券使用状态');
INSERT INTO `sys_dict_type` VALUES (117, '优惠券使用场景', 'coupon_use_scene', '0', 1, '2025-06-20 15:37:19', 1, '2025-07-02 23:24:56', '优惠券使用场景');
INSERT INTO `sys_dict_type` VALUES (118, '运营商状态', 'operator_status', '0', 1, '2025-06-20 15:37:34', 1, '2025-07-02 23:24:56', '运营商状态');
INSERT INTO `sys_dict_type` VALUES (119, '支付状态', 'pay_status', '0', 1, '2025-06-20 15:41:58', 1, '2025-07-02 23:24:56', '订单支付状态');
INSERT INTO `sys_dict_type` VALUES (120, '支付方式', 'pay_method', '0', 1, '2025-06-20 15:41:58', 1, '2025-07-02 23:24:56', '支付方式类型');
INSERT INTO `sys_dict_type` VALUES (121, '优先级', 'priority_level', '0', 1, '2025-06-26 09:30:36', 1, '2025-07-02 23:24:56', '异常订单优先级字典');
INSERT INTO `sys_dict_type` VALUES (122, '来源类型', 'source_type', '0', 1, '2025-06-26 09:30:51', 1, '2025-07-02 23:24:56', '异常订单来源类型字典');
INSERT INTO `sys_dict_type` VALUES (123, '车牌类型', 'plate_type', '0', 1, '2025-06-26 10:28:55', 1, '2025-07-02 23:24:56', '车牌类型字典');
INSERT INTO `sys_dict_type` VALUES (126, '商户套餐类型', 'merchant_package_type', '0', 1, '2025-06-30 16:01:40', 1, '2025-07-02 23:24:56', '商户套餐类型列表');
INSERT INTO `sys_dict_type` VALUES (127, '会员类型', 'vip_member_type', '0', 1, '2025-07-02 09:15:39', 1, '2025-07-02 23:24:56', '会员类型字典');
INSERT INTO `sys_dict_type` VALUES (128, '小程序用户状态', 'wx_user_status', '0', 1, '2025-07-04 10:18:31', NULL, '2025-07-04 10:18:31', '小程序用户状态列表');
INSERT INTO `sys_dict_type` VALUES (129, '小程序用户类型', 'wx_user_type', '0', 1, '2025-07-04 10:18:51', NULL, '2025-07-04 10:18:51', '小程序用户类型列表');
INSERT INTO `sys_dict_type` VALUES (131, '道闸操作类型', 'gate_operation_type', '0', 1, '2025-07-11 08:16:27', NULL, '2025-07-11 08:16:27', '道闸系统支持的操作类型（数字化）');
INSERT INTO `sys_dict_type` VALUES (132, '道闸品牌', 'gate_vendor', '0', 1, '2025-07-11 08:16:45', NULL, '2025-07-11 08:16:45', '支持的道闸品牌列表（数字化）');
INSERT INTO `sys_dict_type` VALUES (133, '道闸API路径', 'gate_api_path', '0', 1, '2025-07-11 08:17:01', NULL, '2025-07-11 08:17:01', '各品牌道闸的API路径配置');
INSERT INTO `sys_dict_type` VALUES (134, '错误数据码', 'error_data_code', '0', 1, '2025-07-11 16:18:47', NULL, '2025-07-11 16:18:47', '错误数据日志错误码列表');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:25:29', '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:25:29', '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:25:29', '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '提示信息',
  `access_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`access_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 227 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 12:59:42');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 13:09:33');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 14:41:10');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 14:41:18');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 14:50:03');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 14:52:02');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:05:58');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:05:59');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:06:09');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:06:09');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:10:44');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:10:44');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:11:23');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:11:23');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:14:34');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:14:35');
INSERT INTO `sys_logininfor` VALUES (116, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:15:02');
INSERT INTO `sys_logininfor` VALUES (117, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:15:02');
INSERT INTO `sys_logininfor` VALUES (118, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:21:31');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:21:31');
INSERT INTO `sys_logininfor` VALUES (120, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:22:02');
INSERT INTO `sys_logininfor` VALUES (121, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:22:02');
INSERT INTO `sys_logininfor` VALUES (122, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:24:43');
INSERT INTO `sys_logininfor` VALUES (123, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:24:44');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:26:55');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:26:55');
INSERT INTO `sys_logininfor` VALUES (126, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:27:58');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:27:59');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:34:28');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:34:29');
INSERT INTO `sys_logininfor` VALUES (130, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:36:50');
INSERT INTO `sys_logininfor` VALUES (131, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:36:51');
INSERT INTO `sys_logininfor` VALUES (132, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:37:46');
INSERT INTO `sys_logininfor` VALUES (133, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:37:47');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:38:47');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:38:47');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:51:50');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:51:51');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:53:12');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-04 23:53:12');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-04 23:54:37');
INSERT INTO `sys_logininfor` VALUES (141, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-06 11:57:21');
INSERT INTO `sys_logininfor` VALUES (142, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-09 12:34:33');
INSERT INTO `sys_logininfor` VALUES (143, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-09 13:07:21');
INSERT INTO `sys_logininfor` VALUES (144, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-09 13:12:18');
INSERT INTO `sys_logininfor` VALUES (145, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-09 13:22:25');
INSERT INTO `sys_logininfor` VALUES (146, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-09 13:30:32');
INSERT INTO `sys_logininfor` VALUES (147, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-10 10:17:38');
INSERT INTO `sys_logininfor` VALUES (148, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-10 21:15:41');
INSERT INTO `sys_logininfor` VALUES (149, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-11 09:05:52');
INSERT INTO `sys_logininfor` VALUES (150, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-12 02:28:32');
INSERT INTO `sys_logininfor` VALUES (151, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-12 05:08:25');
INSERT INTO `sys_logininfor` VALUES (152, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-12 11:08:07');
INSERT INTO `sys_logininfor` VALUES (153, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-12 17:39:54');
INSERT INTO `sys_logininfor` VALUES (154, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-13 04:09:20');
INSERT INTO `sys_logininfor` VALUES (155, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-13 11:20:06');
INSERT INTO `sys_logininfor` VALUES (156, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-13 11:45:13');
INSERT INTO `sys_logininfor` VALUES (157, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 09:50:08');
INSERT INTO `sys_logininfor` VALUES (158, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 14:24:16');
INSERT INTO `sys_logininfor` VALUES (159, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-16 14:33:10');
INSERT INTO `sys_logininfor` VALUES (160, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 14:33:14');
INSERT INTO `sys_logininfor` VALUES (161, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 14:41:21');
INSERT INTO `sys_logininfor` VALUES (162, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 14:49:24');
INSERT INTO `sys_logininfor` VALUES (163, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-16 15:01:43');
INSERT INTO `sys_logininfor` VALUES (164, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 15:01:47');
INSERT INTO `sys_logininfor` VALUES (165, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 18:38:08');
INSERT INTO `sys_logininfor` VALUES (166, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-16 19:13:27');
INSERT INTO `sys_logininfor` VALUES (167, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-17 09:21:53');
INSERT INTO `sys_logininfor` VALUES (168, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-18 09:19:09');
INSERT INTO `sys_logininfor` VALUES (169, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 12:30:11');
INSERT INTO `sys_logininfor` VALUES (170, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 15:11:49');
INSERT INTO `sys_logininfor` VALUES (171, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 11:14:18');
INSERT INTO `sys_logininfor` VALUES (172, '', '127.0.0.1', '0', '退出成功', '2025-06-20 18:43:06');
INSERT INTO `sys_logininfor` VALUES (173, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 18:43:11');
INSERT INTO `sys_logininfor` VALUES (174, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-21 14:36:03');
INSERT INTO `sys_logininfor` VALUES (175, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-24 10:24:45');
INSERT INTO `sys_logininfor` VALUES (176, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-25 12:44:22');
INSERT INTO `sys_logininfor` VALUES (177, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 09:13:14');
INSERT INTO `sys_logininfor` VALUES (178, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 12:18:31');
INSERT INTO `sys_logininfor` VALUES (179, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 13:42:18');
INSERT INTO `sys_logininfor` VALUES (180, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 14:34:02');
INSERT INTO `sys_logininfor` VALUES (181, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 14:58:31');
INSERT INTO `sys_logininfor` VALUES (182, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 19:04:58');
INSERT INTO `sys_logininfor` VALUES (183, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 19:38:00');
INSERT INTO `sys_logininfor` VALUES (184, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 10:04:14');
INSERT INTO `sys_logininfor` VALUES (185, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-30 12:41:26');
INSERT INTO `sys_logininfor` VALUES (186, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-01 09:49:01');
INSERT INTO `sys_logininfor` VALUES (187, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-01 15:19:19');
INSERT INTO `sys_logininfor` VALUES (188, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-01 15:19:23');
INSERT INTO `sys_logininfor` VALUES (189, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-01 18:03:53');
INSERT INTO `sys_logininfor` VALUES (190, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-02 11:04:09');
INSERT INTO `sys_logininfor` VALUES (191, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-02 11:48:22');
INSERT INTO `sys_logininfor` VALUES (192, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-02 12:47:18');
INSERT INTO `sys_logininfor` VALUES (193, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-02 18:07:31');
INSERT INTO `sys_logininfor` VALUES (194, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-02 19:46:55');
INSERT INTO `sys_logininfor` VALUES (195, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 00:13:49');
INSERT INTO `sys_logininfor` VALUES (196, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 08:15:45');
INSERT INTO `sys_logininfor` VALUES (197, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 08:47:09');
INSERT INTO `sys_logininfor` VALUES (198, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 10:14:53');
INSERT INTO `sys_logininfor` VALUES (199, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 10:45:41');
INSERT INTO `sys_logininfor` VALUES (200, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 11:00:57');
INSERT INTO `sys_logininfor` VALUES (201, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 12:36:49');
INSERT INTO `sys_logininfor` VALUES (202, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 13:08:43');
INSERT INTO `sys_logininfor` VALUES (203, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 15:47:29');
INSERT INTO `sys_logininfor` VALUES (204, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 15:52:32');
INSERT INTO `sys_logininfor` VALUES (205, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-04 09:18:21');
INSERT INTO `sys_logininfor` VALUES (206, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-04 09:29:48');
INSERT INTO `sys_logininfor` VALUES (207, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-04 11:14:59');
INSERT INTO `sys_logininfor` VALUES (208, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-05 17:47:54');
INSERT INTO `sys_logininfor` VALUES (209, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-05 17:55:17');
INSERT INTO `sys_logininfor` VALUES (210, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-05 18:15:50');
INSERT INTO `sys_logininfor` VALUES (211, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-08 14:53:51');
INSERT INTO `sys_logininfor` VALUES (212, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-08 15:20:25');
INSERT INTO `sys_logininfor` VALUES (213, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-08 18:17:26');
INSERT INTO `sys_logininfor` VALUES (214, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 09:46:26');
INSERT INTO `sys_logininfor` VALUES (215, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 11:35:26');
INSERT INTO `sys_logininfor` VALUES (216, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 20:36:51');
INSERT INTO `sys_logininfor` VALUES (217, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-10 10:29:51');
INSERT INTO `sys_logininfor` VALUES (218, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-10 13:12:39');
INSERT INTO `sys_logininfor` VALUES (219, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-11 09:44:25');
INSERT INTO `sys_logininfor` VALUES (220, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-11 15:44:43');
INSERT INTO `sys_logininfor` VALUES (221, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-11 15:59:25');
INSERT INTO `sys_logininfor` VALUES (222, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-13 10:34:34');
INSERT INTO `sys_logininfor` VALUES (223, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-13 16:22:25');
INSERT INTO `sys_logininfor` VALUES (224, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-16 02:02:15');
INSERT INTO `sys_logininfor` VALUES (225, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-16 10:00:51');
INSERT INTO `sys_logininfor` VALUES (226, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-16 10:17:25');
INSERT INTO `sys_logininfor` VALUES (227, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-16 10:17:30');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2155 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 1, '2025-06-04 11:01:40', 1, '2025-06-04 13:14:24', '系统监控目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '在线用户菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'system/operlog/index', '', '', 1, 0, 'C', '0', '0', 'system:operlog:list', 'form', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'system/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'system:logininfor:list', 'logininfor', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:unlock', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2000, '广告配置管理', 2006, 1, 'advertConfig', 'operation/advertConfig/index', NULL, '', 1, 0, 'C', '0', '0', 'system:advertConfig:list', 'guide', 1, '2025-06-06 13:51:35', 1, '2025-07-02 23:26:19', '广告配置信息菜单');
INSERT INTO `sys_menu` VALUES (2001, '广告配置查询', 2000, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:query', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2002, '广告配置新增', 2000, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:add', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2003, '广告配置修改', 2000, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:edit', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2004, '广告配置删除', 2000, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:remove', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2005, '广告配置导出', 2000, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:export', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2006, '运营管理', 0, 4, 'operation', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 1, '2025-06-06 14:10:37', 1, '2025-06-27 10:05:42', '运营管理目录');
INSERT INTO `sys_menu` VALUES (2007, '平台管理', 0, 7, 'platform', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'table', 1, '2025-06-09 12:37:01', 1, '2025-07-04 10:19:20', '平台管理目录');
INSERT INTO `sys_menu` VALUES (2008, '运营商管理', 2007, 1, 'operator', 'platform/operator/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:operator:list', 'peoples', 1, '2025-06-09 12:37:07', 1, '2025-07-02 23:26:19', '运营商管理菜单');
INSERT INTO `sys_menu` VALUES (2009, '场库管理', 2007, 2, 'warehouse', 'platform/warehouse/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:warehouse:list', 'build', 1, '2025-06-09 12:37:13', 1, '2025-07-02 23:26:19', '场库管理菜单');
INSERT INTO `sys_menu` VALUES (2010, '场库管理人员', 2007, 4, 'warehouseManager', 'platform/warehouseManager/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:warehouseManager:list', 'user', 1, '2025-06-09 12:37:20', 1, '2025-07-02 23:26:19', '场库管理人员菜单');
INSERT INTO `sys_menu` VALUES (2011, '运营商查询', 2008, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:query', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2012, '运营商新增', 2008, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:add', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2013, '运营商修改', 2008, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:edit', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2014, '运营商删除', 2008, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:remove', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2015, '运营商导出', 2008, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:export', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2016, '场库查询', 2009, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:query', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2017, '场库新增', 2009, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:add', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2018, '场库修改', 2009, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:edit', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2019, '场库删除', 2009, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:remove', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2020, '场库导出', 2009, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:export', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2021, '管理人员查询', 2010, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:query', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2022, '管理人员新增', 2010, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:add', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2023, '管理人员修改', 2010, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:edit', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2024, '管理人员删除', 2010, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:remove', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2025, '管理人员导出', 2010, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:export', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2026, '会员管理', 0, 6, 'vip', '', '', '', 1, 0, 'M', '0', '0', '', 'money', 1, '2025-06-09 14:45:53', 1, '2025-07-04 10:19:14', '会员管理目录');
INSERT INTO `sys_menu` VALUES (2030, '会员套餐配置', 2026, 1, 'vip-package', 'vip/package/index', '', '', 1, 0, 'C', '0', '0', 'vip:package:list', 'shopping', 1, '2025-06-09 14:46:17', 1, '2025-07-02 23:26:19', '会员套餐配置菜单');
INSERT INTO `sys_menu` VALUES (2031, '会员信息管理', 2026, 2, 'vip-member', 'vip/member/index', '', '', 1, 0, 'C', '0', '0', 'vip:member:list', 'user', 1, '2025-06-09 14:46:24', 1, '2025-07-02 23:26:19', '会员信息管理菜单');
INSERT INTO `sys_menu` VALUES (2032, '会员交易记录', 2026, 3, 'transaction', 'vip/transaction/index', '', '', 1, 0, 'C', '0', '0', 'vip:transaction:list', 'money', 1, '2025-06-09 14:46:42', 1, '2025-07-02 23:26:19', '会员交易记录菜单');
INSERT INTO `sys_menu` VALUES (2033, '会员套餐查询', 2030, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:query', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2034, '会员套餐新增', 2030, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:add', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2035, '会员套餐修改', 2030, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:edit', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2036, '会员套餐删除', 2030, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:remove', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2037, '会员套餐导出', 2030, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:export', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2038, '会员信息查询', 2031, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:query', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2039, '会员信息新增', 2031, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:add', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2040, '会员信息修改', 2031, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:edit', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2041, '会员信息删除', 2031, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:remove', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2042, '会员信息导出', 2031, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:export', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2043, '交易记录查询', 2032, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:query', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2044, '交易记录新增', 2032, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:add', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2045, '交易记录修改', 2032, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:edit', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2046, '交易记录删除', 2032, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:remove', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2047, '交易记录导出', 2032, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:export', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2048, '交易退款处理', 2032, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:refund', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2082, '优惠券管理', 0, 8, 'coupon', NULL, '', '', 1, 0, 'M', '0', '0', '', 'coupon', 1, '2025-06-12 20:04:37', 1, '2025-07-04 10:19:24', '优惠券管理目录');
INSERT INTO `sys_menu` VALUES (2083, '优惠券模板', 2082, 1, 'template', 'coupon/template/index', '', '', 1, 0, 'C', '0', '0', 'coupon:template:list', 'ticket', 1, '2025-06-12 20:04:44', 1, '2025-07-02 23:26:19', '优惠券模板菜单');
INSERT INTO `sys_menu` VALUES (2084, '用户优惠券', 2082, 2, 'coupon-user', 'coupon/user/index', '', 'CouponUser', 1, 0, 'C', '0', '0', 'coupon:user:list', 'user', 1, '2025-06-12 20:04:44', 1, '2025-07-02 23:26:19', '用户优惠券菜单');
INSERT INTO `sys_menu` VALUES (2085, '优惠券模板查询', 2083, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:template:query', '#', 1, '2025-06-12 20:04:52', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2086, '优惠券模板新增', 2083, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:template:add', '#', 1, '2025-06-12 20:04:52', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2087, '优惠券模板修改', 2083, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:template:edit', '#', 1, '2025-06-12 20:04:52', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2088, '优惠券模板删除', 2083, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:template:remove', '#', 1, '2025-06-12 20:04:52', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2089, '优惠券模板导出', 2083, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:template:export', '#', 1, '2025-06-12 20:04:52', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2090, '用户优惠券查询', 2084, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:query', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2091, '用户优惠券新增', 2084, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:add', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2092, '用户优惠券修改', 2084, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:edit', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2093, '用户优惠券删除', 2084, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:remove', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2094, '用户优惠券导出', 2084, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:export', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2095, '发放优惠券', 2084, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:issue', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2096, '使用优惠券', 2084, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:use', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2097, '作废优惠券', 2084, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'coupon:user:void', '#', 1, '2025-06-12 20:05:05', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2098, '订单管理', 0, 5, 'order', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'shopping', 1, '2025-06-19 12:18:27', 1, '2025-07-04 10:19:11', '订单管理目录');
INSERT INTO `sys_menu` VALUES (2099, '停车订单管理', 2098, 1, 'parkingOrder', 'order/parkingOrder/index', NULL, 'ParkingOrder', 1, 0, 'C', '0', '0', 'order:parkingOrder:list', 'parking-order', 1, '2025-06-19 12:18:40', 1, '2025-07-02 23:26:19', '停车订单管理菜单');
INSERT INTO `sys_menu` VALUES (2100, '停车订单查询', 2099, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:query', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2101, '停车订单新增', 2099, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:add', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2102, '停车订单修改', 2099, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:edit', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2103, '停车订单删除', 2099, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:remove', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2104, '停车订单导出', 2099, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:export', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2105, '错误数据日志管理', 2098, 2, 'exceptionOrder', 'order/exceptionOrder/index', NULL, 'ExceptionOrder', 1, 0, 'C', '0', '0', 'system:errorDataLog:list', 'exception-order', 1, '2025-06-19 12:19:05', 1, '2025-07-11 16:22:13', '错误数据日志管理菜单');
INSERT INTO `sys_menu` VALUES (2106, '错误数据日志查询', 2105, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:errorDataLog:query', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-11 16:22:20', '');
INSERT INTO `sys_menu` VALUES (2109, '错误数据日志删除', 2105, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:errorDataLog:remove', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-11 16:22:26', '');
INSERT INTO `sys_menu` VALUES (2110, '错误数据日志导出', 2105, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:errorDataLog:export', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-11 16:22:35', '');
INSERT INTO `sys_menu` VALUES (2112, '银联配置管理', 2007, 3, 'unionPayConfig', 'platform/unionPayConfig/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:unionPayConfig:list', 'money', 1, '2025-06-20 14:53:28', 1, '2025-07-02 23:26:19', '银联配置管理菜单');
INSERT INTO `sys_menu` VALUES (2113, '银联配置查询', 2112, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:query', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2114, '银联配置新增', 2112, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:add', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2115, '银联配置修改', 2112, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:edit', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2116, '银联配置删除', 2112, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:remove', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2117, '银联配置导出', 2112, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:export', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2118, '商户管理', 0, 9, 'merchant', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'merchant-management', 1, '2025-06-26 18:41:56', 1, '2025-07-04 10:19:28', '商户管理目录');
INSERT INTO `sys_menu` VALUES (2119, '商户信息', 2118, 1, 'info', 'merchant/info/index', NULL, '', 1, 0, 'C', '0', '0', 'merchant:info:list', 'merchant-info', 1, '2025-06-26 18:42:39', 1, '2025-06-26 18:42:39', '商户信息菜单');
INSERT INTO `sys_menu` VALUES (2120, '商户套餐', 2118, 2, 'package', 'merchant/package/index', NULL, '', 1, 0, 'C', '0', '0', 'merchant:package:list', 'merchant-package', 1, '2025-06-26 18:42:39', 1, '2025-06-26 18:42:39', '商户套餐菜单');
INSERT INTO `sys_menu` VALUES (2121, '商户会员', 2118, 3, 'member', 'merchant/member/index', NULL, '', 1, 0, 'C', '0', '0', 'merchant:member:list', 'merchant-member', 1, '2025-06-26 18:42:39', 1, '2025-06-26 18:42:39', '商户会员菜单');
INSERT INTO `sys_menu` VALUES (2122, '商户购劵', 2118, 4, 'voucher', 'merchant/voucher/index', NULL, '', 1, 0, 'C', '0', '0', 'merchant:voucher:list', 'merchant-voucher', 1, '2025-06-26 18:42:39', 1, '2025-06-26 18:42:39', '商户购劵菜单');
INSERT INTO `sys_menu` VALUES (2123, '扫码购券', 2118, 5, 'scan', 'merchant/scan/index', NULL, '', 1, 0, 'C', '0', '0', 'merchant:scan:list', 'scan-voucher', 1, '2025-06-26 18:42:39', 1, '2025-06-26 18:42:39', '扫码购券菜单');
INSERT INTO `sys_menu` VALUES (2124, '特殊会员', 2026, 4, 'special-user', 'vip/specialUser/index', NULL, '', 1, 0, 'C', '0', '0', 'special:user:list', 'user', 1, '2025-07-03 08:10:57', NULL, '2025-07-03 08:10:57', '特殊会员菜单');
INSERT INTO `sys_menu` VALUES (2125, '特殊会员查询', 2124, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:query', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2126, '特殊会员新增', 2124, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:add', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2127, '特殊会员修改', 2124, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:edit', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2128, '特殊会员删除', 2124, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:remove', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2129, '特殊会员导出', 2124, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:export', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2130, '车主管理', 0, 5, 'owner', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'user', 1, '2025-07-04 10:19:37', NULL, '2025-07-04 10:19:37', '车主管理目录');
INSERT INTO `sys_menu` VALUES (2131, '小程序用户', 2130, 1, 'wxuser', 'owner/wxuser/index', NULL, '', 1, 0, 'C', '0', '0', 'owner:wxuser:list', 'peoples', 1, '2025-07-04 10:19:48', NULL, '2025-07-04 10:19:48', '小程序用户菜单');
INSERT INTO `sys_menu` VALUES (2132, '小程序用户查询', 2131, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:query', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2133, '小程序用户新增', 2131, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:add', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2134, '小程序用户修改', 2131, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:edit', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2135, '小程序用户删除', 2131, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:remove', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2136, '小程序用户导出', 2131, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:export', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2137, '查询用户车辆', 2131, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:car', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2138, '白名单管理', 2130, 2, 'whitelist', 'owner/whitelist/index', '', '', 1, 0, 'C', '0', '0', 'owner:whitelist:list', 'peoples', 1, '2025-07-08 15:22:59', 1, '2025-07-08 20:37:34', '白名单管理菜单');
INSERT INTO `sys_menu` VALUES (2139, '白名单查询', 2138, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:query', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2140, '白名单新增', 2138, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:add', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2141, '白名单修改', 2138, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:edit', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2142, '白名单删除', 2138, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:remove', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2143, '白名单导出', 2138, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:export', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2144, '黑名单管理', 2130, 3, 'blacklist', 'owner/blacklist/index', NULL, '', 1, 0, 'C', '0', '0', 'owner:blacklist:list', 'lock', 1, '2025-07-08 20:26:03', NULL, '2025-07-08 20:33:18', '黑名单管理菜单');
INSERT INTO `sys_menu` VALUES (2145, '黑名单查询', 2144, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:query', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2146, '黑名单新增', 2144, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:add', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2147, '黑名单修改', 2144, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:edit', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2148, '黑名单删除', 2144, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:remove', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2149, '黑名单导出', 2144, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:export', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2150, '车辆出入场记录管理', 2098, 3, 'gateParkingInfo', 'order/gateParkingInfo/index', NULL, 'GateParkingInfo', 1, 0, 'C', '0', '0', 'order:gateParkingInfo:list', 'parking', 1, '2025-07-11 13:30:30', 1, '2025-07-11 14:44:52', '车辆出入场记录管理菜单');
INSERT INTO `sys_menu` VALUES (2151, '车辆出入场记录查询', 2150, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:query', '#', 1, '2025-07-11 13:30:44', 1, '2025-07-11 13:30:44', '');
INSERT INTO `sys_menu` VALUES (2152, '车辆出入场记录新增', 2150, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:add', '#', 1, '2025-07-11 13:30:50', 1, '2025-07-11 13:30:50', '');
INSERT INTO `sys_menu` VALUES (2153, '车辆出入场记录修改', 2150, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:edit', '#', 1, '2025-07-11 13:30:58', 1, '2025-07-11 13:30:58', '');
INSERT INTO `sys_menu` VALUES (2154, '车辆出入场记录删除', 2150, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:remove', '#', 1, '2025-07-11 13:31:06', 1, '2025-07-11 13:31:06', '');
INSERT INTO `sys_menu` VALUES (2155, '车辆出入场记录导出', 2150, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:export', '#', 1, '2025-07-11 13:31:11', 1, '2025-07-11 13:31:11', '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:42', '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:42', '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 360 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (100, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-04 11:01:40\",\"icon\":\"monitor\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2,\"menuName\":\"系统监控\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"monitor\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:13:47', 32);
INSERT INTO `sys_oper_log` VALUES (101, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-04 11:01:40\",\"icon\":\"monitor\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2,\"menuName\":\"系统监控\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"monitor\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:14:24', 7);
INSERT INTO `sys_oper_log` VALUES (102, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-06-04 11:01:40\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,3,114,115,1055,1056,1058,1057,1059,1060,116,4],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:19:58', 40);
INSERT INTO `sys_oper_log` VALUES (103, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1054', '127.0.0.1', '', '1054', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:22:58', 10);
INSERT INTO `sys_oper_log` VALUES (104, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1053', '127.0.0.1', '', '1053', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:23:00', 13);
INSERT INTO `sys_oper_log` VALUES (105, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1052', '127.0.0.1', '', '1052', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:23:02', 8);
INSERT INTO `sys_oper_log` VALUES (106, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1051', '127.0.0.1', '', '1051', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:23:05', 10);
INSERT INTO `sys_oper_log` VALUES (107, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1050', '127.0.0.1', '', '1050', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:23:06', 6);
INSERT INTO `sys_oper_log` VALUES (108, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1049', '127.0.0.1', '', '1049', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:23:08', 6);
INSERT INTO `sys_oper_log` VALUES (109, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/110', '127.0.0.1', '', '110', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:23:49', 18);
INSERT INTO `sys_oper_log` VALUES (110, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-06-04 11:01:40\",\"icon\":\"sentinel\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":111,\"menuName\":\"Sentinel控制台\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2,\"path\":\"http://localhost:9100\",\"perms\":\"monitor:sentinel:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:28:51', 7);
INSERT INTO `sys_oper_log` VALUES (111, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-06-04 11:01:40\",\"icon\":\"sentinel\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":111,\"menuName\":\"Sentinel控制台\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2,\"path\":\"http://localhost:8718\",\"perms\":\"monitor:sentinel:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:30:15', 8);
INSERT INTO `sys_oper_log` VALUES (112, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/112', '127.0.0.1', '', '112', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:32:23', 7);
INSERT INTO `sys_oper_log` VALUES (113, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/113', '127.0.0.1', '', '113', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 13:44:46', 5);
INSERT INTO `sys_oper_log` VALUES (114, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/111', '127.0.0.1', '', '111', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:03:00', 5);
INSERT INTO `sys_oper_log` VALUES (115, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-06-04 11:01:40\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,3,114,115,1055,1056,1058,1057,1059,1060,116],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:07:02', 15);
INSERT INTO `sys_oper_log` VALUES (116, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/4', '127.0.0.1', '', '4', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:07:23', 15);
INSERT INTO `sys_oper_log` VALUES (117, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/109', '127.0.0.1', '', '109', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:18:40', 8);
INSERT INTO `sys_oper_log` VALUES (118, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/108', '127.0.0.1', '', '108', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:18:42', 6);
INSERT INTO `sys_oper_log` VALUES (119, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/102', '127.0.0.1', '', '102', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:18:44', 6);
INSERT INTO `sys_oper_log` VALUES (120, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/107', '127.0.0.1', '', '107', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:18:45', 11);
INSERT INTO `sys_oper_log` VALUES (121, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/106', '127.0.0.1', '', '106', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:18:47', 8);
INSERT INTO `sys_oper_log` VALUES (122, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/105', '127.0.0.1', '', '105', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-06-04 14:18:48', 2);
INSERT INTO `sys_oper_log` VALUES (123, '用户管理', 2, 'com.ruoyi.system.controller.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/user', '127.0.0.1', '', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-06-04 11:01:40\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":105,\"deptName\":\"测试部门\",\"leader\":\"若依\",\"orderNum\":3,\"params\":{},\"parentId\":101,\"status\":\"0\"},\"deptId\":100,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-04 11:01:40\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[2],\"pwdUpdateDate\":\"2025-06-04 11:01:40\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:18:57', 26);
INSERT INTO `sys_oper_log` VALUES (124, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/105', '127.0.0.1', '', '105', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:19:00', 7);
INSERT INTO `sys_oper_log` VALUES (125, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/104', '127.0.0.1', '', '104', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:19:01', 6);
INSERT INTO `sys_oper_log` VALUES (126, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/103', '127.0.0.1', '', '103', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-06-04 14:19:03', 2);
INSERT INTO `sys_oper_log` VALUES (127, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/103', '127.0.0.1', '', '103', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-06-04 14:21:00', 3);
INSERT INTO `sys_oper_log` VALUES (128, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/103', '127.0.0.1', '', '103', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:22:18', 6);
INSERT INTO `sys_oper_log` VALUES (129, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/101', '127.0.0.1', '', '101', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:22:22', 7);
INSERT INTO `sys_oper_log` VALUES (130, '部门管理', 2, 'com.ruoyi.system.controller.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0\",\"children\":[],\"createBy\":\"admin\",\"createTime\":\"2025-06-04 11:01:40\",\"delFlag\":\"0\",\"deptId\":100,\"deptName\":\"上海临港捷运交通有限公司\",\"email\":\"<EMAIL>\",\"leader\":\"若依\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:22:44', 9);
INSERT INTO `sys_oper_log` VALUES (131, '部门管理', 2, 'com.ruoyi.system.controller.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0\",\"children\":[],\"createBy\":\"admin\",\"createTime\":\"2025-06-04 11:01:40\",\"delFlag\":\"0\",\"deptId\":100,\"deptName\":\"临港捷运\",\"email\":\"<EMAIL>\",\"leader\":\"若依\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:23:05', 7);
INSERT INTO `sys_oper_log` VALUES (132, '用户管理', 2, 'com.ruoyi.system.controller.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/user', '127.0.0.1', '', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-06-04 11:01:40\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"临港捷运\",\"leader\":\"若依\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"status\":\"0\"},\"deptId\":100,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-04 11:01:40\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"管理员\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[1],\"pwdUpdateDate\":\"2025-06-04 11:01:40\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:24:14', 13);
INSERT INTO `sys_oper_log` VALUES (133, '岗位管理', 3, 'com.ruoyi.system.controller.SysPostController.remove()', 'DELETE', 1, 'admin', NULL, '/post/4', '127.0.0.1', '', '[4]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:24:31', 9);
INSERT INTO `sys_oper_log` VALUES (134, '岗位管理', 3, 'com.ruoyi.system.controller.SysPostController.remove()', 'DELETE', 1, 'admin', NULL, '/post/3', '127.0.0.1', '', '[3]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:24:33', 13);
INSERT INTO `sys_oper_log` VALUES (135, '岗位管理', 3, 'com.ruoyi.system.controller.SysPostController.remove()', 'DELETE', 1, 'admin', NULL, '/post/2', '127.0.0.1', '', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:24:34', 12);
INSERT INTO `sys_oper_log` VALUES (136, '岗位管理', 2, 'com.ruoyi.system.controller.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/post', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-04 11:01:40\",\"flag\":false,\"params\":{},\"postCode\":\"admin\",\"postId\":1,\"postName\":\"管理员\",\"postSort\":1,\"remark\":\"\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:24:51', 11);
INSERT INTO `sys_oper_log` VALUES (137, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-06-04 11:01:40\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,3,114,115,1055,1056,1058,1057,1059,1060,116],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"管理员\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-04 14:29:04', 14);
INSERT INTO `sys_oper_log` VALUES (138, '菜单管理', 2, 'com.lgjy.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-06 14:10:37\",\"icon\":\"shopping\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2006,\"menuName\":\"运营管理\",\"menuType\":\"M\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-06 14:18:53', 33);
INSERT INTO `sys_oper_log` VALUES (139, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"会员专享优惠\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":4,\"params\":{},\"picUrl\":\"https://www.example.com/images/vip.jpg\",\"remark\":\"会员专享广告\",\"status\":1,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-06 13:58:40\",\"url\":\"https://www.example.com/vip\"}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Incorrect integer value: \'admin\' for column \'update_by\' at row 1\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniAdvertConfigMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniAdvertConfigMapper.updateMiniAdvertConfig-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update mini_advert_config          SET remark = ?,             advert_title = ?,             url = ?,             pic_url = ?,             status = ?,             delete_flag = ?,             create_by = ?,             create_time = ?,             update_by = ?,             update_time = ?          where id = ?\r\n### Cause: java.sql.SQLException: Incorrect integer value: \'admin\' for column \'update_by\' at row 1\n; uncategorized SQLException; SQL state [HY000]; error code [1366]; Incorrect integer value: \'admin\' for column \'update_by\' at row 1; nested exception is java.sql.SQLException: Incorrect integer value: \'admin\' for column \'update_by\' at row 1', '2025-06-06 14:19:14', 89);
INSERT INTO `sys_oper_log` VALUES (140, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"限时抢购活动1\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":5,\"params\":{},\"picUrl\":\"https://www.example.com/images/flash-sale.jpg\",\"remark\":\"限时促销广告\",\"status\":1,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-06 13:58:40\",\"url\":\"https://www.example.com/flash-sale\"}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Incorrect integer value: \'admin\' for column \'update_by\' at row 1\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniAdvertConfigMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniAdvertConfigMapper.updateMiniAdvertConfig-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update mini_advert_config          SET remark = ?,             advert_title = ?,             url = ?,             pic_url = ?,             status = ?,             delete_flag = ?,             create_by = ?,             create_time = ?,             update_by = ?,             update_time = ?          where id = ?\r\n### Cause: java.sql.SQLException: Incorrect integer value: \'admin\' for column \'update_by\' at row 1\n; uncategorized SQLException; SQL state [HY000]; error code [1366]; Incorrect integer value: \'admin\' for column \'update_by\' at row 1; nested exception is java.sql.SQLException: Incorrect integer value: \'admin\' for column \'update_by\' at row 1', '2025-06-06 14:20:06', 6);
INSERT INTO `sys_oper_log` VALUES (141, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"限时抢购活动\",\"createBy\":\"admin\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":5,\"params\":{},\"picUrl\":\"https://www.example.com/images/flash-sale.jpg\",\"remark\":\"限时促销广告\",\"status\":2,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-06 14:21:55\",\"url\":\"https://www.example.com/flash-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-06 14:24:35', 13);
INSERT INTO `sys_oper_log` VALUES (142, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"限时抢购活动\",\"createBy\":\"admin\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":5,\"params\":{},\"picUrl\":\"https://www.example.com/images/flash-sale.jpg\",\"remark\":\"限时促销广告\",\"status\":1,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-06 14:21:55\",\"url\":\"https://www.example.com/flash-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-06 14:24:38', 11);
INSERT INTO `sys_oper_log` VALUES (143, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"限时抢购活动\",\"createBy\":\"admin\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":5,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/06/06/男1_20250606142446A001.png\",\"remark\":\"限时促销广告\",\"status\":1,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-06 14:21:55\",\"url\":\"https://www.example.com/flash-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-06 14:24:47', 15);
INSERT INTO `sys_oper_log` VALUES (144, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"限时抢购活动1\",\"createBy\":\"admin\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":5,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/06/06/男1_20250606142446A001.png\",\"remark\":\"限时促销广告\",\"status\":1,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-06 14:21:55\",\"url\":\"https://www.example.com/flash-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-06 14:24:55', 10);
INSERT INTO `sys_oper_log` VALUES (145, '广告配置信息', 3, 'com.lgjy.system.controller.MiniAdvertConfigController.remove()', 'DELETE', 1, 'admin', NULL, '/advertConfig/2', '127.0.0.1', '', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-06 15:36:38', 39);
INSERT INTO `sys_oper_log` VALUES (146, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"优化测试广告1\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 15:07:27\",\"deleteFlag\":0,\"id\":7,\"params\":{},\"picUrl\":\"https://test.com/pic.jpg\",\"remark\":\"测试更新操作\",\"status\":1,\"updateBy\":\"1\",\"updateTime\":\"2025-06-06 15:07:42\",\"url\":\"https://test.com\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-06 15:46:48', 42);
INSERT INTO `sys_oper_log` VALUES (147, '运营商信息', 2, 'com.lgjy.system.controller.MiniOperatorController.edit()', 'PUT', 1, 'admin', NULL, '/platform/operator', '127.0.0.1', '', '{\"companyName\":\"上海临港漕河泾物业服务有限公司\",\"createBy\":\"150\",\"createTime\":\"2024-10-23 13:28:57\",\"deleted\":0,\"id\":3,\"params\":{},\"primaryContactName\":\"陶灵丽1\",\"primaryContactPhone\":\"15821130102\",\"remark\":\"负责鸿音广场项目\",\"status\":1,\"updateBy\":\"1\",\"updateTime\":\"2025-05-01 20:50:24\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-09 13:12:25', 26);
INSERT INTO `sys_oper_log` VALUES (148, '运营商信息', 2, 'com.lgjy.system.controller.MiniOperatorController.edit()', 'PUT', 1, 'admin', NULL, '/platform/operator', '127.0.0.1', '', '{\"companyName\":\"上海临港漕河泾物业服务有限公司\",\"createBy\":\"150\",\"createTime\":\"2024-10-23 13:28:57\",\"deleted\":0,\"id\":3,\"params\":{},\"primaryContactName\":\"陶灵丽\",\"primaryContactPhone\":\"15821130102\",\"remark\":\"负责鸿音广场项目\",\"status\":0,\"updateBy\":\"1\",\"updateTime\":\"2025-06-09 13:12:25\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-09 13:12:31', 15);
INSERT INTO `sys_oper_log` VALUES (149, '运营商信息', 2, 'com.lgjy.system.controller.MiniOperatorController.edit()', 'PUT', 1, 'admin', NULL, '/platform/operator', '127.0.0.1', '', '{\"companyName\":\"上海临港漕河泾物业服务有限公司\",\"createBy\":\"150\",\"createTime\":\"2024-10-23 13:28:57\",\"deleted\":0,\"id\":3,\"params\":{},\"primaryContactName\":\"陶灵丽\",\"primaryContactPhone\":\"15821130102\",\"remark\":\"负责鸿音广场项目\",\"status\":1,\"updateBy\":\"1\",\"updateTime\":\"2025-06-09 13:12:31\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-09 13:12:36', 10);
INSERT INTO `sys_oper_log` VALUES (150, '运营商信息', 1, 'com.lgjy.system.controller.MiniOperatorController.add()', 'POST', 1, 'admin', NULL, '/platform/operator', '127.0.0.1', '', '{\"companyName\":\"百搭公司哦\",\"createBy\":\"1\",\"deleted\":0,\"id\":4,\"params\":{},\"primaryContactName\":\"123\",\"primaryContactPhone\":\"15723459765\",\"remark\":\"12\",\"status\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-09 13:13:03', 16);
INSERT INTO `sys_oper_log` VALUES (151, '运营商信息', 3, 'com.lgjy.system.controller.MiniOperatorController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/operator/4', '127.0.0.1', '', '[4]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-09 13:13:39', 18);
INSERT INTO `sys_oper_log` VALUES (152, '停车场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市浦东新区海洋七路999弄\",\"availableSpaces\":539,\"chargingStations\":0,\"cityCode\":\"3101\",\"createBy\":\"150\",\"createTime\":\"2024-06-13 13:47:30\",\"deleted\":0,\"districtCode\":\"310115\",\"id\":13,\"latitude\":30.871855,\"longitude\":121.939591,\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"provinceCode\":\"31\",\"remark\":\"临港科技城项目。停车收费规则：一小时内免费，1至8小时收费5元，8至16小时收费10元，16至24小时收费15元，超过24小时，按上述标准累计计费\",\"status\":2,\"totalSpaces\":539,\"updateBy\":\"1\",\"updateTime\":\"2025-01-02 13:47:47\",\"warehouseCode\":\"0000011\",\"warehouseName\":\"朝露苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-09 13:16:55', 20);
INSERT INTO `sys_oper_log` VALUES (153, '停车场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市浦东新区木荷路515弄\",\"availableSpaces\":567,\"chargingStations\":0,\"cityCode\":\"3101\",\"createBy\":\"150\",\"createTime\":\"2024-06-13 13:46:58\",\"deleted\":0,\"districtCode\":\"310115\",\"id\":12,\"latitude\":30.874122,\"longitude\":121.936965,\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"provinceCode\":\"31\",\"remark\":\"临港科技城项目。停车收费规则：一小时内免费，1至8小时收费5元，8至16小时收费10元，16至24小时收费15元，超过24小时，按上述标准累计计费\",\"status\":3,\"totalSpaces\":567,\"updateBy\":\"1\",\"updateTime\":\"2025-01-02 13:47:34\",\"warehouseCode\":\"0000010\",\"warehouseName\":\"月微苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-09 13:17:01', 10);
INSERT INTO `sys_oper_log` VALUES (154, '菜单管理', 2, 'com.lgjy.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-04 11:01:40\",\"icon\":\"tool\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3,\"menuName\":\"系统工具\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"tool\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-10 13:33:35', 32);
INSERT INTO `sys_oper_log` VALUES (155, '道闸厂商连接测试', 0, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.testConnection()', 'POST', 1, 'admin', NULL, '/barriers/manufacturer/testConnection/2', '127.0.0.1', '', '2', '{\"msg\":\"连接测试成功\",\"code\":200}', 0, NULL, '2025-06-11 12:26:58', 1028);
INSERT INTO `sys_oper_log` VALUES (156, '道闸厂商连接测试', 0, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.testConnection()', 'POST', 1, 'admin', NULL, '/barriers/manufacturer/testConnection/2', '127.0.0.1', '', '2', '{\"msg\":\"连接测试成功\",\"code\":200}', 0, NULL, '2025-06-11 12:27:21', 1014);
INSERT INTO `sys_oper_log` VALUES (157, '道闸操作日志', 5, 'com.lgjy.system.controller.barrier.MiniBarrierMonitorController.exportLogs()', 'POST', 1, 'admin', NULL, '/barriers/monitor/exportLogs', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-11 12:34:04', 1208);
INSERT INTO `sys_oper_log` VALUES (158, '道闸厂商管理', 2, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/manufacturer', '127.0.0.1', '', '{\"apiPort\":83,\"apiUrl\":\"http://qr.it-wy.cn:83\",\"authType\":\"sign\",\"createTime\":\"2025-06-10 13:07:28\",\"deleteFlag\":0,\"envType\":\"test\",\"id\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"sizhuo\",\"params\":{},\"remark\":\"司卓道闸厂商，支持正式/测试环境切换，已优化为字典配置\",\"retryTimes\":3,\"status\":1,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"timeout\":5000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 12:00:10\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 12:52:02', 38);
INSERT INTO `sys_oper_log` VALUES (159, '道闸厂商管理', 2, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/manufacturer', '127.0.0.1', '', '{\"apiPort\":83,\"apiUrl\":\"http://qr.it-wy.cn:83\",\"authType\":\"sign\",\"createTime\":\"2025-06-10 13:07:28\",\"deleteFlag\":0,\"envType\":\"prod\",\"id\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"sizhuo\",\"params\":{},\"remark\":\"司卓道闸厂商，支持正式/测试环境切换，已优化为字典配置\",\"retryTimes\":3,\"status\":1,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"timeout\":5000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 12:52:02\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 12:52:12', 12);
INSERT INTO `sys_oper_log` VALUES (160, '道闸厂商管理', 2, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/manufacturer', '127.0.0.1', '', '{\"apiPort\":83,\"apiUrl\":\"http://qr.it-wy.cn:83\",\"authType\":\"sign\",\"contactPerson\":\"吴老师\",\"contactPhone\":\"15280797659\",\"createTime\":\"2025-06-10 13:07:28\",\"deleteFlag\":0,\"envType\":\"test\",\"id\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"sizhuo\",\"params\":{},\"remark\":\"司卓道闸厂商，支持正式/测试环境切换，已优化为字典配置\",\"retryTimes\":3,\"status\":1,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"timeout\":5000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 12:57:29\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 13:04:13', 14);
INSERT INTO `sys_oper_log` VALUES (161, '道闸厂商管理', 2, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/manufacturer', '127.0.0.1', '', '{\"apiPort\":83,\"apiUrl\":\"http://qr.it-wy.cn:83\",\"authType\":\"sign\",\"contactPerson\":\"吴老师\",\"contactPhone\":\"15280797659\",\"createTime\":\"2025-06-10 13:07:28\",\"deleteFlag\":0,\"envType\":\"prod\",\"id\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"sizhuo\",\"params\":{},\"remark\":\"司卓道闸厂商，支持正式/测试环境切换，已优化为字典配置\",\"retryTimes\":3,\"status\":1,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"timeout\":5000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 13:04:13\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 13:56:58', 40);
INSERT INTO `sys_oper_log` VALUES (162, '道闸厂商管理', 2, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/manufacturer', '127.0.0.1', '', '{\"apiPort\":83,\"apiUrl\":\"http://qr.it-wy.cn:83\",\"authType\":\"sign\",\"contactPerson\":\"吴老师\",\"contactPhone\":\"15280797659\",\"createTime\":\"2025-06-10 13:07:28\",\"deleteFlag\":0,\"envType\":\"test\",\"id\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"sizhuo\",\"params\":{},\"remark\":\"司卓道闸厂商，支持正式/测试环境切换，已优化为字典配置\",\"retryTimes\":3,\"status\":1,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"timeout\":5000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 13:56:58\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 13:57:02', 13);
INSERT INTO `sys_oper_log` VALUES (163, '道闸厂商管理', 2, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/manufacturer', '127.0.0.1', '', '{\"apiUrl\":\"http://hongyin.api.com\",\"authType\":\"sign\",\"contactPerson\":\"李信\",\"contactPhone\":\"15467897895\",\"createTime\":\"2025-06-10 13:07:28\",\"deleteFlag\":0,\"envType\":\"prod\",\"id\":1,\"manufacturerName\":\"泓音\",\"oemCode\":\"hongyin\",\"params\":{},\"retryTimes\":3,\"status\":1,\"timeout\":5000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-10 13:07:28\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 14:02:56', 23);
INSERT INTO `sys_oper_log` VALUES (164, '厂商密钥配置', 2, 'com.lgjy.system.controller.ManufacturerSecretConfigController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/secret', '127.0.0.1', '', '{\"apiUrl\":\"http://qr.it-wy.cn:83\",\"createBy\":\"1\",\"createTime\":\"2025-06-11 14:35:50\",\"currentApiUrl\":\"http://qr.it-wy.cn:83\",\"deleteFlag\":0,\"deleted\":false,\"enabled\":false,\"envType\":\"prod\",\"id\":1,\"manufacturerId\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"SIZHUO\",\"params\":{},\"parkingName\":\"瓴舍城苑\",\"parkingSystemId\":\"nicheng_001\",\"prodEnv\":true,\"remark\":\"瓴舍城苑停车场-正式环境\",\"secretKey\":\"20231226105133354\",\"sizhuoParkingId\":\"20231226105133354\",\"sortOrder\":1,\"status\":0,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"testEnv\":false,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 15:37:03\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 15:37:04', 96);
INSERT INTO `sys_oper_log` VALUES (165, '厂商密钥配置', 2, 'com.lgjy.system.controller.ManufacturerSecretConfigController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/secret', '127.0.0.1', '', '{\"apiUrl\":\"http://qr.it-wy.cn:83\",\"createBy\":\"1\",\"createTime\":\"2025-06-11 14:35:50\",\"currentApiUrl\":\"http://qr.it-wy.cn:83\",\"deleteFlag\":0,\"deleted\":false,\"enabled\":true,\"envType\":\"prod\",\"id\":1,\"manufacturerId\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"SIZHUO\",\"params\":{},\"parkingName\":\"瓴舍城苑\",\"parkingSystemId\":\"nicheng_001\",\"prodEnv\":true,\"remark\":\"瓴舍城苑停车场-正式环境\",\"secretKey\":\"20231226105133354\",\"sizhuoParkingId\":\"20231226105133354\",\"sortOrder\":1,\"status\":1,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"testEnv\":false,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 15:37:08\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 15:37:08', 17);
INSERT INTO `sys_oper_log` VALUES (166, '道闸厂商管理', 2, 'com.lgjy.system.controller.barrier.MiniBarrierManufacturerController.edit()', 'PUT', 1, 'admin', NULL, '/barriers/manufacturer', '127.0.0.1', '', '{\"apiPort\":83,\"apiUrl\":\"http://qr.it-wy.cn:83\",\"authType\":\"sign\",\"contactPerson\":\"吴老师\",\"contactPhone\":\"15280797659\",\"createTime\":\"2025-06-10 13:07:28\",\"deleteFlag\":0,\"envType\":\"prod\",\"id\":2,\"manufacturerName\":\"司卓科技\",\"oemCode\":\"sizhuo\",\"params\":{},\"remark\":\"司卓道闸厂商，支持正式/测试环境切换，已优化为字典配置\",\"retryTimes\":3,\"status\":1,\"testApiUrl\":\"http://wxy.ittiger.club:83\",\"timeout\":5000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 13:57:02\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-11 15:52:52', 26);
INSERT INTO `sys_oper_log` VALUES (167, '停车场信息', 2, 'com.lgjy.system.controller.MiniParkingLotController.edit()', 'PUT', 1, 'admin', NULL, '/platform/parkingLot', '127.0.0.1', '', '{\"availableSpaces\":1628,\"chargingSpaces\":0,\"chargingStandardName\":\"临港三五期收费标准\",\"complexId\":22,\"complexName\":\"熙景苑\",\"createBy\":\"1\",\"createTime\":\"2024-01-14 19:01:43\",\"currentChargingStandardId\":6,\"deleted\":0,\"disabledSpaces\":0,\"hasLighting\":1,\"hasMonitoring\":1,\"id\":22,\"isCovered\":0,\"lotCode\":\"0000022-01\",\"lotName\":\"熙景苑-主停车场\",\"lotType\":2,\"params\":{},\"remark\":\"临港五期项目。停车收费规则：一小时内免费，8小时内5/辆/次，8小时-24小时内每8小时加5元/辆，超过24小时按上述标准累计计费\",\"reservedSpaces\":0,\"status\":1,\"totalSpaces\":1628,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-12 05:30:05\",\"vipSpaces\":0}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Incorrect integer value: \'admin\' for column \'updated_by\' at row 1\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniParkingLotMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniParkingLotMapper.updateMiniParkingLot-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update mini_parking_lot          SET lot_name = ?,             lot_code = ?,             complex_id = ?,             lot_type = ?,                          total_spaces = ?,             available_spaces = ?,             reserved_spaces = ?,             disabled_spaces = ?,             charging_spaces = ?,             vip_spaces = ?,             current_charging_standard_id = ?,                          is_covered = ?,             has_monitoring = ?,             has_lighting = ?,                                       remark = ?,             status = ?,             updated_by = ?,             updated_at = now()          where id = ?\r\n### Cause: java.sql.SQLException: Incorrect integer value: \'admin\' for column \'updated_by\' at row 1\n; uncategorized SQLException; SQL state [HY000]; error code [1366]; Incorrect integer value: \'admin\' for column \'updated_by\' at row 1; nested exception is java.sql.SQLException: Incorrect integer value: \'admin\' for column \'updated_by\' at row 1', '2025-06-12 11:51:10', 123);
INSERT INTO `sys_oper_log` VALUES (168, '停车场信息', 2, 'com.lgjy.system.controller.MiniParkingLotController.edit()', 'PUT', 1, 'admin', NULL, '/platform/parkingLot', '127.0.0.1', '', '{\"availableSpaces\":100,\"chargingSpaces\":0,\"complexId\":29,\"complexName\":\"新元畅想苑北区\",\"createBy\":\"1\",\"createTime\":\"2025-06-11 15:50:58\",\"deleted\":0,\"disabledSpaces\":0,\"hasLighting\":1,\"hasMonitoring\":1,\"id\":29,\"isCovered\":0,\"lotCode\":\"0000029-01\",\"lotName\":\"新元畅想苑北区-主停车场\",\"lotType\":2,\"params\":{},\"reservedSpaces\":0,\"status\":1,\"totalSpaces\":100,\"updateBy\":\"1\",\"updateTime\":\"2025-06-11 15:50:58\",\"vipSpaces\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 12:06:44', 29);
INSERT INTO `sys_oper_log` VALUES (169, '道闸设备控制', 0, 'com.lgjy.system.controller.barrier.MiniBarrierDeviceController.control()', 'POST', 1, 'admin', NULL, '/barriers/device/control/1', '127.0.0.1', '', '{\"plateNo\":\"\",\"operationType\":\"0\"}', '{\"msg\":\"设备控制成功\",\"code\":200,\"data\":{\"success\":true,\"code\":\"200\",\"message\":\"模拟控制成功\",\"gateStatus\":1,\"onlineStatus\":1,\"responseTimestamp\":\"2025-06-12T12:18:50.861+08:00\"}}', 0, NULL, '2025-06-12 12:18:50', 1285);
INSERT INTO `sys_oper_log` VALUES (170, '停车场信息', 2, 'com.lgjy.system.controller.MiniParkingLotController.edit()', 'PUT', 1, 'admin', NULL, '/platform/parkingLot', '127.0.0.1', '', '{\"availableSpaces\":180,\"chargingSpaces\":0,\"chargingStandardName\":\"LIN舍公寓收费标准\",\"complexId\":2,\"complexName\":\"LIN舍公寓\",\"createBy\":\"1\",\"createTime\":\"2025-06-12 05:35:54\",\"currentChargingStandardId\":2,\"deleted\":0,\"disabledSpaces\":0,\"entryHeightLimit\":2.1,\"floorInfo\":\"B1\",\"hasLighting\":1,\"hasMonitoring\":1,\"id\":33,\"isCovered\":1,\"lotCode\":\"0000002-02\",\"lotName\":\"LIN舍公寓-地下一层\",\"lotType\":1,\"params\":{},\"remark\":\"地下一层停车场\",\"reservedSpaces\":0,\"status\":1,\"totalSpaces\":200,\"updateBy\":\"1\",\"updateTime\":\"2025-06-12 05:35:54\",\"vipSpaces\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 14:04:59', 58);
INSERT INTO `sys_oper_log` VALUES (171, '场库管理人员信息', 2, 'com.lgjy.system.controller.MiniWarehouseManagerController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouseManager', '127.0.0.1', '', '{\"complexId\":19,\"complexName\":\"新元盛璟苑\",\"createBy\":\"1\",\"createTime\":\"2025-06-09 12:54:54\",\"deleted\":0,\"id\":1,\"isPrimary\":1,\"managerName\":\"张凌珉\",\"managerPhone\":\"13671994216\",\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"remark\":\"负责临港一期凌波苑\",\"status\":0,\"updateBy\":\"1\",\"updateTime\":\"2025-06-09 12:54:54\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 14:39:34', 28);
INSERT INTO `sys_oper_log` VALUES (172, '场库管理人员信息', 2, 'com.lgjy.system.controller.MiniWarehouseManagerController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouseManager', '127.0.0.1', '', '{\"complexId\":19,\"complexName\":\"新元盛璟苑\",\"createBy\":\"1\",\"createTime\":\"2025-06-09 12:54:54\",\"deleted\":0,\"id\":1,\"isPrimary\":1,\"managerName\":\"张凌珉\",\"managerPhone\":\"13671994216\",\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"remark\":\"负责临港一期凌波苑\",\"status\":1,\"updateBy\":\"1\",\"updateTime\":\"2025-06-12 14:39:34\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 14:39:42', 12);
INSERT INTO `sys_oper_log` VALUES (173, '更新过期优惠券', 2, 'com.lgjy.system.controller.MiniCouponUserController.updateExpiredCoupons()', 'PUT', 1, 'admin', NULL, '/coupon/user/updateExpired', '127.0.0.1', '', '', '{\"msg\":\"更新了 2 张过期优惠券\",\"code\":200}', 0, NULL, '2025-06-12 22:29:49', 27);
INSERT INTO `sys_oper_log` VALUES (174, '更新过期优惠券', 2, 'com.lgjy.system.controller.MiniCouponUserController.updateExpiredCoupons()', 'PUT', 1, 'admin', NULL, '/coupon/user/updateExpired', '127.0.0.1', '', '', '{\"msg\":\"更新了 0 张过期优惠券\",\"code\":200}', 0, NULL, '2025-06-12 22:29:52', 2);
INSERT INTO `sys_oper_log` VALUES (175, '用户优惠券', 2, 'com.lgjy.system.controller.MiniCouponUserController.edit()', 'PUT', 1, 'admin', NULL, '/coupon/user', '127.0.0.1', '', '{\"couponId\":1,\"couponName\":\"新用户专享5元优惠券\",\"couponNo\":\"CPN202401010001\",\"createTime\":\"2025-06-12 19:54:09\",\"id\":1,\"params\":{},\"phoneNumber\":\"13800138001\",\"receiveTime\":\"2024-01-15 10:30:00\",\"remark\":\"测试用户优惠券\",\"updateTime\":\"2025-06-12 22:29:49\",\"useStatus\":2,\"userId\":1,\"validEndTime\":\"2024-02-14 23:59:59\",\"validStartTime\":\"2024-01-15 10:30:00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 22:40:35', 39);
INSERT INTO `sys_oper_log` VALUES (176, '更新过期优惠券', 2, 'com.lgjy.system.controller.MiniCouponUserController.updateExpiredCoupons()', 'PUT', 1, 'admin', NULL, '/coupon/user/updateExpired', '127.0.0.1', '', '', '{\"msg\":\"更新了 0 张过期优惠券\",\"code\":200}', 0, NULL, '2025-06-12 22:40:37', 4);
INSERT INTO `sys_oper_log` VALUES (177, '用户优惠券', 2, 'com.lgjy.system.controller.MiniCouponUserController.edit()', 'PUT', 1, 'admin', NULL, '/coupon/user', '127.0.0.1', '', '{\"couponId\":1,\"couponName\":\"新用户专享5元优惠券\",\"couponNo\":\"CPN202401010001\",\"createTime\":\"2025-06-12 19:54:09\",\"id\":1,\"params\":{},\"phoneNumber\":\"13800138001\",\"receiveTime\":\"2024-01-15 10:30:00\",\"remark\":\"测试用户优惠券\",\"updateTime\":\"2025-06-12 22:40:35\",\"useStatus\":2,\"userId\":1,\"validEndTime\":\"2025-09-27 23:59:59\",\"validStartTime\":\"2024-01-15 10:30:00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 22:41:55', 8);
INSERT INTO `sys_oper_log` VALUES (178, '更新过期优惠券', 2, 'com.lgjy.system.controller.MiniCouponUserController.updateExpiredCoupons()', 'PUT', 1, 'admin', NULL, '/coupon/user/updateExpired', '127.0.0.1', '', '', '{\"msg\":\"更新了 0 张过期优惠券\",\"code\":200}', 0, NULL, '2025-06-12 22:41:58', 1);
INSERT INTO `sys_oper_log` VALUES (179, '结束优惠券', 2, 'com.lgjy.system.controller.MiniCouponController.end()', 'PUT', 1, 'admin', NULL, '/coupon/template/end/1', '127.0.0.1', '', '1', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 22:42:50', 10);
INSERT INTO `sys_oper_log` VALUES (180, '启用优惠券', 2, 'com.lgjy.system.controller.MiniCouponController.enable()', 'PUT', 1, 'admin', NULL, '/coupon/template/enable/1', '127.0.0.1', '', '1', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 22:42:52', 7);
INSERT INTO `sys_oper_log` VALUES (181, '复制优惠券模板', 1, 'com.lgjy.system.controller.MiniCouponController.copyCoupon()', 'POST', 1, 'admin', NULL, '/coupon/template/copy/1', '127.0.0.1', '', '1', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'issue_start_time\' doesn\'t have a default value\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniCouponMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniCouponMapper.insertMiniCoupon-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into mini_coupon          ( coupon_code,             coupon_name,             coupon_type,             discount_type,             discount_value,             threshold_amount,                          applicable_scope,                                       issue_type,             total_quantity,             issued_quantity,             used_quantity,             user_limit,                                       valid_type,             valid_days,                                       status,             description,             usage_rules,             sort_order,             delete_flag,             create_by,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,                          ?,                                       ?,             ?,             ?,             ?,             ?,                                       ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             now() )\r\n### Cause: java.sql.SQLException: Field \'issue_start_time\' doesn\'t have a default value\n; Field \'issue_start_time\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'issue_start_time\' doesn\'t have a default value', '2025-06-12 22:42:59', 56);
INSERT INTO `sys_oper_log` VALUES (182, '复制优惠券模板', 1, 'com.lgjy.system.controller.MiniCouponController.copyCoupon()', 'POST', 1, 'admin', NULL, '/coupon/template/copy/1', '127.0.0.1', '', '1', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 22:49:07', 26);
INSERT INTO `sys_oper_log` VALUES (183, '复制优惠券模板', 1, 'com.lgjy.system.controller.MiniCouponController.copyCoupon()', 'POST', 1, 'admin', NULL, '/coupon/template/copy/5', '127.0.0.1', '', '5', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 22:49:14', 15);
INSERT INTO `sys_oper_log` VALUES (184, '优惠券模板', 2, 'com.lgjy.system.controller.MiniCouponController.edit()', 'PUT', 1, 'admin', NULL, '/coupon/template', '127.0.0.1', '', '{\"applicableScope\":1,\"couponCode\":\"COUPON202506127435\",\"couponName\":\"新用户专享5元优惠券_副本\",\"couponType\":1,\"createBy\":\"1\",\"createTime\":\"2025-06-12 22:49:07\",\"deleteFlag\":0,\"description\":\"新用户注册专享优惠券，满10元可用\",\"discountType\":1,\"discountValue\":5,\"id\":5,\"issueEndTime\":\"2025-07-12 22:49:07\",\"issueStartTime\":\"2025-06-12 22:49:07\",\"issueType\":3,\"issuedQuantity\":0,\"params\":{},\"sortOrder\":1,\"status\":1,\"thresholdAmount\":10,\"totalQuantity\":1000,\"updateBy\":\"1\",\"usageRules\":\"仅限新用户使用，不可与其他优惠同享\",\"usedQuantity\":0,\"userLimit\":1,\"validDays\":30,\"validType\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 23:30:59', 23);
INSERT INTO `sys_oper_log` VALUES (185, '启用优惠券', 2, 'com.lgjy.system.controller.MiniCouponController.enable()', 'PUT', 1, 'admin', NULL, '/coupon/template/enable/6', '127.0.0.1', '', '6', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-12 23:31:05', 14);
INSERT INTO `sys_oper_log` VALUES (186, '优惠券模板', 2, 'com.lgjy.system.controller.MiniCouponController.edit()', 'PUT', 1, 'admin', NULL, '/coupon/template', '127.0.0.1', '', '{\"applicableScope\":1,\"couponCode\":\"COUPON202506124285\",\"couponName\":\"新用户专享5元优惠券_副本_副本\",\"couponType\":1,\"createBy\":\"1\",\"createTime\":\"2025-06-12 22:49:14\",\"deleteFlag\":0,\"description\":\"新用户注册专享优惠券，满10元可用\",\"discountType\":1,\"discountValue\":7,\"id\":6,\"issueEndTime\":\"2025-07-12 22:49:14\",\"issueStartTime\":\"2025-06-12 22:49:14\",\"issueType\":3,\"issuedQuantity\":0,\"params\":{},\"sortOrder\":1,\"status\":2,\"thresholdAmount\":15,\"totalQuantity\":1000,\"updateBy\":\"1\",\"updateTime\":\"2025-06-12 23:31:05\",\"usageRules\":\"仅限新用户使用，不可与其他优惠同享\",\"usedQuantity\":0,\"userLimit\":1,\"validEndTime\":\"2025-09-05 00:00:00\",\"validStartTime\":\"2025-06-13 00:00:00\",\"validType\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-13 04:10:07', 35);
INSERT INTO `sys_oper_log` VALUES (187, '复制优惠券模板', 1, 'com.lgjy.system.controller.MiniCouponController.copyCoupon()', 'POST', 1, 'admin', NULL, '/coupon/template/copy/6', '127.0.0.1', '', '6', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-13 11:48:27', 34);
INSERT INTO `sys_oper_log` VALUES (188, '优惠券模板', 3, 'com.lgjy.system.controller.MiniCouponController.remove()', 'DELETE', 1, 'admin', NULL, '/coupon/template/7', '127.0.0.1', '', '[7]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-13 11:48:30', 17);
INSERT INTO `sys_oper_log` VALUES (189, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"限时抢购活动1\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":5,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/06/06/男1_20250606142446A001.png\",\"remark\":\"限时促销广告\",\"status\":2,\"updateBy\":\"1\",\"updateTime\":\"2025-06-06 15:04:50\",\"url\":\"https://www.example.com/flash-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-16 09:51:04', 162);
INSERT INTO `sys_oper_log` VALUES (190, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"春节促销活动\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 13:56:05\",\"deleteFlag\":0,\"id\":1,\"params\":{},\"picUrl\":\"https://www.example.com/images/spring-sale.jpg\",\"remark\":\"测试广告1\",\"status\":2,\"updateBy\":\"1\",\"updateTime\":\"2025-06-06 14:57:56\",\"url\":\"https://www.example.com/spring-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-16 09:51:06', 9);
INSERT INTO `sys_oper_log` VALUES (191, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"优化测试广告1\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 15:07:27\",\"deleteFlag\":0,\"id\":7,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/06/16/banner1_20250616095448A001.png\",\"remark\":\"测试更新操作\",\"status\":1,\"updateBy\":\"1\",\"updateTime\":\"2025-06-06 15:07:42\",\"url\":\"https://test.com\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-16 09:54:50', 8);
INSERT INTO `sys_oper_log` VALUES (192, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"元旦狂欢节\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":3,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/06/16/banner2_20250616095456A002.png\",\"remark\":\"元旦活动广告\",\"status\":1,\"updateBy\":\"1\",\"updateTime\":\"2025-06-06 15:04:44\",\"url\":\"https://www.example.com/new-year\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-16 09:54:57', 7);
INSERT INTO `sys_oper_log` VALUES (193, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"会员专享优惠\",\"createBy\":\"1\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":4,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_18_56_05_20250617111218A008.png\",\"remark\":\"会员专享广告\",\"status\":2,\"updateBy\":\"1\",\"updateTime\":\"2025-06-06 15:04:44\",\"url\":\"https://www.example.com/vip\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-17 11:12:23', 48);
INSERT INTO `sys_oper_log` VALUES (194, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseComplexController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouseComplex', '127.0.0.1', '', '{\"availableSpaces\":100,\"carouselImages\":\"http://127.0.0.1:9300/statics/2025/06/17/banner1_20250617111824A009.png,http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_19_37_09_20250617111831A010.png\",\"chargingStations\":0,\"complexCode\":\"0000029\",\"complexName\":\"新元畅想苑北区\",\"createById\":1,\"createTime\":\"2025-06-11 15:50:58\",\"deleted\":0,\"id\":29,\"managementMode\":1,\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"status\":1,\"totalParkingLots\":1,\"totalSpaces\":100,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-11 15:50:58\"}', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: \"Invalid value.\" at position 0 in value for column \'mini_warehouse_complex.carousel_images\'.\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniWarehouseComplexMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniWarehouseComplexMapper.updateMiniWarehouseComplex-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update mini_warehouse_complex          SET complex_name = ?,             complex_code = ?,             operator_id = ?,             total_parking_lots = ?,             total_spaces = ?,             available_spaces = ?,             charging_stations = ?,                                                                                           management_mode = ?,                                       carousel_images = ?,                          status = ?,                          updated_at = now()          where id = ?\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: \"Invalid value.\" at position 0 in value for column \'mini_warehouse_complex.carousel_images\'.\n; Data truncation: Invalid JSON text: \"Invalid value.\" at position 0 in value for column \'mini_warehouse_complex.carousel_images\'.; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: \"Invalid value.\" at position 0 in value for column \'mini_warehouse_complex.carousel_images\'.', '2025-06-17 11:18:33', 94);
INSERT INTO `sys_oper_log` VALUES (195, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseComplexController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouseComplex', '127.0.0.1', '', '{\"availableSpaces\":100,\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/06/17/banner1_20250617112216A011.png\\\",\\\"http://127.0.0.1:9300/statics/2025/06/17/ChatGPT_Image_2025年5月28日_19_53_20_20250617112222A012.png\\\"]\",\"chargingStations\":0,\"complexCode\":\"0000029\",\"complexName\":\"新元畅想苑北区\",\"createById\":1,\"createTime\":\"2025-06-11 15:50:58\",\"deleted\":0,\"id\":29,\"managementMode\":1,\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"status\":1,\"totalParkingLots\":1,\"totalSpaces\":100,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-11 15:50:58\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-17 11:22:24', 11);
INSERT INTO `sys_oper_log` VALUES (196, '复制优惠券模板', 1, 'com.lgjy.system.controller.MiniCouponController.copyCoupon()', 'POST', 1, 'admin', NULL, '/coupon/template/copy/6', '127.0.0.1', '', '6', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-17 13:10:06', 33);
INSERT INTO `sys_oper_log` VALUES (197, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-18 11:57:19\",\"cssClass\":\"\",\"default\":false,\"dictCode\":105,\"dictLabel\":\"未营业\",\"dictSort\":2,\"dictType\":\"warehouse_status\",\"dictValue\":\"0\",\"isDefault\":\"N\",\"listClass\":\"warning\",\"params\":{},\"remark\":\"场库未营业状态\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 12:01:17', 26);
INSERT INTO `sys_oper_log` VALUES (198, '场库信息', 1, 'com.lgjy.system.controller.MiniWarehouseController.add()', 'POST', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市\",\"areaCode\":\"310115\",\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/06/18/banner1_20250618123905A002.png\\\"]\",\"chargingStationNum\":88,\"cityCode\":\"310100\",\"contactInfo\":\"15207324567\",\"createBy\":\"admin\",\"id\":72,\"latitude\":10.0000,\"longitude\":80.000010,\"operatorId\":3,\"params\":{},\"passageNum\":2,\"projectName\":\"江山\",\"provinceCode\":\"310000\",\"remark\":\"123\",\"reservationNum\":100,\"responsiblePerson\":\"李老师\",\"smartsType\":0,\"status\":1,\"totalParking\":120,\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 12:39:07', 41);
INSERT INTO `sys_oper_log` VALUES (199, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市\",\"areaCode\":\"310115\",\"carouselImages\":\"[\\\"[\\\\\\\"http://127.0.0.1:9300/statics/2025/06/18/banner1_20250618123905A002.png\\\\\\\"]\\\"]\",\"chargingStationNum\":88,\"cityCode\":\"310100\",\"contactInfo\":\"15207324567\",\"createTime\":\"2025-06-18 12:39:07\",\"deleteFlag\":0,\"id\":72,\"latitude\":10,\"longitude\":80.00001,\"operatorId\":3,\"operatorName\":\"上海临港漕河泾物业服务有限公司\",\"params\":{},\"passageNum\":2,\"projectName\":\"江山路\",\"provinceCode\":\"310000\",\"remark\":\"123\",\"reservationNum\":100,\"responsiblePerson\":\"李老师\",\"smartsType\":0,\"status\":1,\"totalParking\":120,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-18 12:39:07\",\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 12:45:32', 15);
INSERT INTO `sys_oper_log` VALUES (200, '场库信息', 3, 'com.lgjy.system.controller.MiniWarehouseController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/warehouse/72', '127.0.0.1', '', '[72]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 12:59:13', 21);
INSERT INTO `sys_oper_log` VALUES (201, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市浦东新区群峰路128弄\",\"areaCode\":\"310115\",\"chargingStationNum\":0,\"cityCode\":\"3101\",\"contactInfo\":\"***********\",\"createById\":1,\"createTime\":\"2024-01-14 19:00:35\",\"deleteFlag\":0,\"id\":21,\"latitude\":30.893124,\"longitude\":121.814952,\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"parkingLotCount\":0,\"passageNum\":1,\"projectName\":\"临港三期\",\"provinceCode\":\"31\",\"remark\":\"停车收费规则：一小时内免费，1至8小时收费5元，8至16小时收费10元，16至24小时收费15元，超过24小时，按上述标准累计计费\",\"reservationNum\":25,\"responsiblePerson\":\"里没\",\"smartsType\":0,\"status\":1,\"totalParking\":1790,\"updateBy\":\"admin\",\"updateById\":1,\"updateTime\":\"2024-01-14 19:15:44\",\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"新元盛璟苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 13:22:05', 27);
INSERT INTO `sys_oper_log` VALUES (202, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市浦东新区群峰路128弄\",\"areaCode\":\"310115\",\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/06/18/ChatGPT_Image_2025年5月28日_19_53_20_20250618132216A004.png\\\"]\",\"chargingStationNum\":0,\"cityCode\":\"3101\",\"contactInfo\":\"***********\",\"createById\":1,\"createTime\":\"2024-01-14 19:00:35\",\"deleteFlag\":0,\"id\":21,\"latitude\":30.893124,\"longitude\":121.814952,\"operatorId\":1,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"parkingLotCount\":0,\"passageNum\":1,\"projectName\":\"临港三期\",\"provinceCode\":\"31\",\"remark\":\"停车收费规则：一小时内免费，1至8小时收费5元，8至16小时收费10元，16至24小时收费15元，超过24小时，按上述标准累计计费\",\"reservationNum\":25,\"responsiblePerson\":\"里没\",\"smartsType\":0,\"status\":1,\"totalParking\":1790,\"updateBy\":\"admin\",\"updateById\":1,\"updateTime\":\"2025-06-18 13:22:05\",\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"新元盛璟苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 13:22:17', 13);
INSERT INTO `sys_oper_log` VALUES (203, '场库信息', 3, 'com.lgjy.system.controller.MiniWarehouseController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/warehouse/71', '127.0.0.1', '', '[71]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 13:22:34', 15);
INSERT INTO `sys_oper_log` VALUES (204, '场库信息', 1, 'com.lgjy.system.controller.MiniWarehouseController.add()', 'POST', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市浦东新区群峰路128弄\",\"areaCode\":\"120101\",\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/06/18/banner1_20250618135556A005.png\\\"]\",\"chargingStationNum\":2,\"cityCode\":\"120100\",\"contactInfo\":\"***********\",\"createBy\":\"admin\",\"id\":723053377537314816,\"latitude\":30.893124,\"longitude\":121.814952,\"operatorId\":1871825000000000000,\"params\":{},\"parkingLotCount\":0,\"passageNum\":2,\"projectName\":\"123\",\"provinceCode\":\"120000\",\"remark\":\"12玩1213123\",\"reservationNum\":3,\"responsiblePerson\":\"大而我却的\",\"smartsType\":0,\"status\":1,\"totalParking\":3,\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"我的\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 13:55:59', 40);
INSERT INTO `sys_oper_log` VALUES (205, '场库信息', 3, 'com.lgjy.system.controller.MiniWarehouseController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/warehouse/1871825000000001002', '127.0.0.1', '', '[1871825000000001002]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 14:05:04', 29);
INSERT INTO `sys_oper_log` VALUES (206, '场库信息', 3, 'com.lgjy.system.controller.MiniWarehouseController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/warehouse/1871825000000001001', '127.0.0.1', '', '[1871825000000001001]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 14:05:07', 13);
INSERT INTO `sys_oper_log` VALUES (207, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"上海市浦东新区群峰路128弄\",\"areaCode\":\"120101\",\"carouselImages\":\"[\\\"[\\\\\\\"http://127.0.0.1:9300/statics/2025/06/18/banner1_20250618135556A005.png\\\\\\\"]\\\"]\",\"chargingStationNum\":2,\"cityCode\":\"120100\",\"contactInfo\":\"***********\",\"createTime\":\"2025-06-18 13:55:58\",\"deleteFlag\":0,\"id\":\"723053377537314816\",\"latitude\":30.893124,\"longitude\":121.814952,\"operatorId\":\"1871825000000000003\",\"params\":{},\"parkingLotCount\":0,\"passageNum\":2,\"projectName\":\"123\",\"provinceCode\":\"120000\",\"remark\":\"12玩1213123\",\"reservationNum\":3,\"responsiblePerson\":\"大而我却的\",\"smartsType\":0,\"status\":1,\"totalParking\":3,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-18 13:55:58\",\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"我的\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 14:05:17', 21);
INSERT INTO `sys_oper_log` VALUES (208, '场库信息', 1, 'com.lgjy.system.controller.MiniWarehouseController.add()', 'POST', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"额放松放松\",\"areaCode\":\"110102\",\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/06/18/ChatGPT_Image_2025年5月28日_19_37_09_20250618140617A006.png\\\"]\",\"chargingStationNum\":2,\"cityCode\":\"110100\",\"contactInfo\":\"15240346295\",\"createBy\":\"admin\",\"id\":\"723055983177371648\",\"latitude\":12.02930132,\"longitude\":12.00180,\"operatorId\":\"1871825000000000001\",\"params\":{},\"parkingLotCount\":0,\"passageNum\":2,\"projectName\":\"问房东发\",\"provinceCode\":\"110000\",\"remark\":\"12313\",\"reservationNum\":2,\"responsiblePerson\":\"粉色的发\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"范围\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-18 14:06:20', 13);
INSERT INTO `sys_oper_log` VALUES (209, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"\",\"areaCode\":\"310115\",\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/06/19/banner1_20250619140541A001.png\\\"]\",\"chargingStationNum\":0,\"cityCode\":\"310100\",\"contactInfo\":\"15240346295\",\"createTime\":\"2025-06-18 14:06:20\",\"deleteFlag\":0,\"id\":\"723055983177371648\",\"latitude\":30.897799,\"longitude\":121.919736,\"operatorId\":\"1871825000000000001\",\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"parkingLotCount\":0,\"passageNum\":2,\"projectName\":\"江山路\",\"provinceCode\":\"310000\",\"remark\":\"这是江山路的备注\",\"reservationNum\":0,\"responsiblePerson\":\"张三\",\"smartsType\":0,\"status\":1,\"totalParking\":120,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-18 15:48:12\",\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:05:42', 77);
INSERT INTO `sys_oper_log` VALUES (210, '场库管理人员信息', 2, 'com.lgjy.system.controller.MiniWarehouseManagerController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouseManager', '127.0.0.1', '', '{\"createById\":1,\"createTime\":\"2025-06-09 12:54:54\",\"deleted\":0,\"id\":1,\"isPrimary\":1,\"managerName\":\"张凌珉\",\"managerPhone\":\"13671994216\",\"operatorId\":1871825000000000001,\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"remark\":\"负责临港一期凌波苑\",\"status\":1,\"updateBy\":\"1\",\"updateById\":1,\"updateTime\":\"2025-06-19 14:32:19\",\"warehouseId\":723055983177371648,\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:47:19', 54);
INSERT INTO `sys_oper_log` VALUES (211, '停车订单', 1, 'com.lgjy.system.controller.MiniParkingOrderController.add()', 'POST', 1, 'admin', NULL, '/order/parkingOrder', '127.0.0.1', '', '{\"actualPayment\":10,\"beginParkingTime\":\"2025-06-13 00:00:00\",\"carType\":\"1\",\"createBy\":\"1\",\"createTime\":\"2025-06-20 11:19:54\",\"deleteFlag\":0,\"discountAmount\":10,\"endParkingTime\":\"2025-06-21 00:00:00\",\"id\":\"723738877508063232\",\"params\":{},\"parkingDuration\":222,\"payStatus\":0,\"payType\":1,\"paymentAmount\":20,\"plateNo\":\"LD29309112\",\"warehouseId\":\"723055983177371648\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 11:19:54', 29);
INSERT INTO `sys_oper_log` VALUES (212, '停车订单', 2, 'com.lgjy.system.controller.MiniParkingOrderController.edit()', 'PUT', 1, 'admin', NULL, '/order/parkingOrder', '127.0.0.1', '', '{\"actualPayment\":10,\"beginParkingTime\":\"2025-06-13 00:00:00\",\"carType\":\"1\",\"createBy\":\"1\",\"createTime\":\"2025-06-20 11:19:54\",\"deleteFlag\":0,\"discountAmount\":10,\"endParkingTime\":\"2025-06-21 00:00:00\",\"id\":\"723738877508063232\",\"params\":{},\"parkingDuration\":222,\"payStatus\":0,\"payStatusName\":\"未支付\",\"payType\":1,\"payTypeName\":\"微信支付\",\"paymentAmount\":12,\"plateNo\":\"LD29309112\",\"updateBy\":\"1\",\"updateTime\":\"2025-06-20 11:20:09\",\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 11:20:09', 12);
INSERT INTO `sys_oper_log` VALUES (213, '停车订单', 2, 'com.lgjy.system.controller.MiniParkingOrderController.edit()', 'PUT', 1, 'admin', NULL, '/order/parkingOrder', '127.0.0.1', '', '{\"actualPayment\":10,\"beginParkingTime\":\"2025-06-13 00:00:00\",\"carType\":\"1\",\"createBy\":\"1\",\"createTime\":\"2025-06-20 11:19:54\",\"deleteFlag\":0,\"discountAmount\":10,\"endParkingTime\":\"2025-06-21 00:00:00\",\"id\":\"723738877508063232\",\"params\":{},\"parkingDuration\":222,\"payStatus\":0,\"payStatusName\":\"未支付\",\"payType\":1,\"payTypeName\":\"微信支付\",\"paymentAmount\":20,\"plateNo\":\"LD29309112\",\"updateBy\":\"1\",\"updateTime\":\"2025-06-20 11:24:09\",\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 11:24:09', 10);
INSERT INTO `sys_oper_log` VALUES (214, '停车订单', 1, 'com.lgjy.system.controller.MiniParkingOrderController.add()', 'POST', 1, 'admin', NULL, '/parkingOrder', '127.0.0.1', '', '{\"actualPayment\":20,\"beginParkingTime\":\"2025-06-20 14:38:43\",\"carType\":\"2\",\"createBy\":\"1\",\"createTime\":\"2025-06-20 14:38:48\",\"deleteFlag\":0,\"discountAmount\":10,\"endParkingTime\":\"2025-06-21 00:00:00\",\"id\":\"723788932952756224\",\"params\":{},\"parkingDuration\":223,\"payStatus\":1,\"payType\":1,\"paymentAmount\":30,\"plateNo\":\"OM02930123\",\"warehouseId\":\"723055983177371648\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 14:38:49', 41);
INSERT INTO `sys_oper_log` VALUES (215, '停车订单', 1, 'com.lgjy.system.controller.MiniParkingOrderController.add()', 'POST', 1, 'admin', NULL, '/parkingOrder', '127.0.0.1', '', '{\"createBy\":\"1\",\"createTime\":\"2025-06-20 14:39:11\",\"deleteFlag\":0,\"id\":\"723789028423503872\",\"params\":{},\"payStatus\":2,\"plateNo\":\"OHN0921840\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 14:39:11', 14);
INSERT INTO `sys_oper_log` VALUES (216, '银联配置', 1, 'com.lgjy.system.controller.MiniUnionPayConfigController.add()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig', '127.0.0.1', '', '{\"createBy\":\"admin\",\"deleteFlag\":0,\"id\":\"723795096830611456\",\"mid\":\"12893710237013\",\"params\":{},\"payType\":1,\"remark\":\"1231\",\"tid\":\"1231231231231\",\"warehouseId\":\"723055983177371648\"}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'create_by\' in \'field list\'\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniUnionPayConfigMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniUnionPayConfigMapper.insertMiniUnionPayConfig-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into mini_union_pay_config          ( id,             warehouse_id,             pay_type,             mid,             tid,             remark,             delete_flag,             create_by,             create_time )            values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             now() )\r\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'create_by\' in \'field list\'\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'create_by\' in \'field list\'', '2025-06-20 15:03:18', 91);
INSERT INTO `sys_oper_log` VALUES (217, '银联配置', 1, 'com.lgjy.system.controller.MiniUnionPayConfigController.add()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig', '127.0.0.1', '', '{\"createBy\":\"admin\",\"deleteFlag\":0,\"id\":\"723796006092804096\",\"mid\":\"12893710237013\",\"params\":{},\"payType\":1,\"remark\":\"1231\",\"tid\":\"1231231231231\",\"warehouseId\":\"723055983177371648\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:06:55', 13);
INSERT INTO `sys_oper_log` VALUES (218, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"额放松放松\",\"areaCode\":\"310115\",\"carouselImages\":\"[\\\"[\\\\\\\"http://127.0.0.1:9300/statics/2025/06/19/banner1_20250619140541A001.png\\\\\\\"]\\\"]\",\"chargingStationNum\":0,\"cityCode\":\"310100\",\"contactInfo\":\"15240346295\",\"createTime\":\"2025-06-18 14:06:20\",\"deleteFlag\":0,\"id\":\"723055983177371648\",\"latitude\":30.897799,\"longitude\":121.919736,\"operatorId\":\"1871825000000000001\",\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"parkingLotCount\":0,\"passageNum\":2,\"projectName\":\"江山路\",\"provinceCode\":\"310000\",\"remark\":\"这是江山路的备注\",\"reservationNum\":0,\"responsiblePerson\":\"张三\",\"smartsType\":0,\"status\":1,\"totalParking\":120,\"updateBy\":\"admin\",\"updateTime\":\"2025-06-19 14:05:42\",\"userId\":1,\"vipParkSign\":0,\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:10:45', 23);
INSERT INTO `sys_oper_log` VALUES (219, '银联配置', 1, 'com.lgjy.system.controller.MiniUnionPayConfigController.add()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig', '127.0.0.1', '', '{\"createBy\":\"admin\",\"deleteFlag\":0,\"id\":\"723799006991486976\",\"mid\":\"213142354345345\",\"params\":{},\"payType\":1,\"remark\":\"1\",\"tid\":\"45345353453\",\"warehouseId\":\"723055983177371648\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:18:50', 32);
INSERT INTO `sys_oper_log` VALUES (220, '银联配置', 3, 'com.lgjy.system.controller.MiniUnionPayConfigController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/unionPayConfig/723799006991486976', '127.0.0.1', '', '[723799006991486976]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:18:57', 20);
INSERT INTO `sys_oper_log` VALUES (221, '银联配置', 2, 'com.lgjy.system.controller.MiniUnionPayConfigController.edit()', 'PUT', 1, 'admin', NULL, '/platform/unionPayConfig', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:06:55\",\"deleteFlag\":0,\"id\":\"723796006092804096\",\"mid\":\"128937102370131\",\"params\":{},\"payType\":2,\"remark\":\"1231\",\"tid\":\"12312312312311\",\"updateBy\":\"admin\",\"updateTime\":\"2025-06-20 15:06:55\",\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:19:13', 15);
INSERT INTO `sys_oper_log` VALUES (222, '银联配置', 2, 'com.lgjy.system.controller.MiniUnionPayConfigController.edit()', 'PUT', 1, 'admin', NULL, '/platform/unionPayConfig', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:06:55\",\"deleteFlag\":0,\"id\":\"723796006092804096\",\"mid\":\"12893710237013112\",\"params\":{},\"payType\":1,\"remark\":\"1231\",\"tid\":\"123123123123111\",\"updateBy\":\"admin\",\"updateTime\":\"2025-06-20 15:19:13\",\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:19:41', 12);
INSERT INTO `sys_oper_log` VALUES (223, '银联配置', 5, 'com.lgjy.system.controller.MiniUnionPayConfigController.export()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 15:19:46', 771);
INSERT INTO `sys_oper_log` VALUES (224, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:28:59\",\"cssClass\":\"\",\"default\":true,\"dictCode\":135,\"dictLabel\":\"包月123\",\"dictSort\":1,\"dictType\":\"vip_package_type\",\"dictValue\":\"1\",\"isDefault\":\"Y\",\"listClass\":\"primary\",\"params\":{},\"remark\":\"包月套餐\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:39:02', 17);
INSERT INTO `sys_oper_log` VALUES (225, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:28:59\",\"cssClass\":\"\",\"default\":true,\"dictCode\":135,\"dictLabel\":\"包月\",\"dictSort\":1,\"dictType\":\"vip_package_type\",\"dictValue\":\"1\",\"isDefault\":\"Y\",\"listClass\":\"primary\",\"params\":{},\"remark\":\"包月套餐\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 15:39:14', 15);
INSERT INTO `sys_oper_log` VALUES (226, '银联配置', 5, 'com.lgjy.system.controller.MiniUnionPayConfigController.export()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 15:53:13', 62);
INSERT INTO `sys_oper_log` VALUES (227, '银联配置', 5, 'com.lgjy.system.controller.MiniUnionPayConfigController.export()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 16:01:26', 975);
INSERT INTO `sys_oper_log` VALUES (228, '场库信息', 5, 'com.lgjy.system.controller.MiniWarehouseController.export()', 'POST', 1, 'admin', NULL, '/platform/warehouse/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 18:44:17', 971);
INSERT INTO `sys_oper_log` VALUES (229, '停车订单', 3, 'com.lgjy.system.controller.MiniParkingOrderController.remove()', 'DELETE', 1, 'admin', NULL, '/parkingOrder/723789028423503872', '127.0.0.1', '', '[723789028423503872]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 18:50:30', 35);
INSERT INTO `sys_oper_log` VALUES (230, '异常订单', 5, 'com.lgjy.system.controller.MiniExceptionOrderController.export()', 'POST', 1, 'admin', NULL, '/exceptionOrder/export', '127.0.0.1', '', '{\"exceptionType\":\"\",\"plateNo\":\"\",\"warehouseId\":\"\",\"pageSize\":\"10\",\"handleStatus\":\"\",\"beginTime\":\"\",\"endTime\":\"\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 19:20:07', 86);
INSERT INTO `sys_oper_log` VALUES (231, '异常订单', 5, 'com.lgjy.system.controller.MiniExceptionOrderController.export()', 'POST', 1, 'admin', NULL, '/exceptionOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 19:23:00', 39);
INSERT INTO `sys_oper_log` VALUES (232, '异常订单', 5, 'com.lgjy.system.controller.MiniExceptionOrderController.export()', 'POST', 1, 'admin', NULL, '/exceptionOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 19:34:01', 860);
INSERT INTO `sys_oper_log` VALUES (233, '停车订单', 5, 'com.lgjy.system.controller.MiniParkingOrderController.export()', 'POST', 1, 'admin', NULL, '/parkingOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-20 19:34:54', 31);
INSERT INTO `sys_oper_log` VALUES (234, '停车订单', 5, 'com.lgjy.system.controller.MiniParkingOrderController.export()', 'POST', 1, 'admin', NULL, '/parkingOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-21 14:37:02', 832);
INSERT INTO `sys_oper_log` VALUES (235, '异常订单', 5, 'com.lgjy.system.controller.MiniExceptionOrderController.export()', 'POST', 1, 'admin', NULL, '/exceptionOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-21 15:00:02', 951);
INSERT INTO `sys_oper_log` VALUES (236, '会员套餐配置', 5, 'com.lgjy.system.controller.MiniVipPackageController.export()', 'POST', 1, 'admin', NULL, '/vip/package/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-21 15:12:04', 59);
INSERT INTO `sys_oper_log` VALUES (237, '广告配置信息', 5, 'com.lgjy.system.controller.MiniAdvertConfigController.export()', 'POST', 1, 'admin', NULL, '/advertConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-21 15:13:13', 32);
INSERT INTO `sys_oper_log` VALUES (238, '广告配置信息', 5, 'com.lgjy.system.controller.MiniAdvertConfigController.export()', 'POST', 1, 'admin', NULL, '/advertConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-21 15:32:50', 733);
INSERT INTO `sys_oper_log` VALUES (239, '停车订单', 5, 'com.lgjy.system.controller.MiniParkingOrderController.export()', 'POST', 1, 'admin', NULL, '/parkingOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-21 15:33:57', 33);
INSERT INTO `sys_oper_log` VALUES (240, '用户管理', 5, 'com.lgjy.system.controller.SysUserController.export()', 'POST', 1, 'admin', NULL, '/user/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 10:24:56', 840);
INSERT INTO `sys_oper_log` VALUES (241, '广告配置信息', 5, 'com.lgjy.system.controller.MiniAdvertConfigController.export()', 'POST', 1, 'admin', NULL, '/advertConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 10:25:31', 27);
INSERT INTO `sys_oper_log` VALUES (242, '停车订单', 5, 'com.lgjy.system.controller.MiniParkingOrderController.export()', 'POST', 1, 'admin', NULL, '/parkingOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 10:25:49', 27);
INSERT INTO `sys_oper_log` VALUES (243, '异常订单', 5, 'com.lgjy.system.controller.MiniExceptionOrderController.export()', 'POST', 1, 'admin', NULL, '/exceptionOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 10:26:02', 24);
INSERT INTO `sys_oper_log` VALUES (244, '异常订单', 5, 'com.lgjy.system.controller.MiniExceptionOrderController.export()', 'POST', 1, 'admin', NULL, '/exceptionOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 10:26:06', 23);
INSERT INTO `sys_oper_log` VALUES (245, '会员套餐配置', 5, 'com.lgjy.system.controller.MiniVipPackageController.export()', 'POST', 1, 'admin', NULL, '/vip/package/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 10:26:26', 47);
INSERT INTO `sys_oper_log` VALUES (246, '银联配置', 5, 'com.lgjy.system.controller.MiniUnionPayConfigController.export()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 14:48:06', 1115);
INSERT INTO `sys_oper_log` VALUES (247, '场库信息', 5, 'com.lgjy.system.controller.MiniWarehouseController.export()', 'POST', 1, 'admin', NULL, '/platform/warehouse/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 14:48:38', 36);
INSERT INTO `sys_oper_log` VALUES (248, '场库信息', 5, 'com.lgjy.system.controller.MiniWarehouseController.export()', 'POST', 1, 'admin', NULL, '/platform/warehouse/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 15:09:35', 55);
INSERT INTO `sys_oper_log` VALUES (249, '运营商信息', 5, 'com.lgjy.system.controller.MiniOperatorController.export()', 'POST', 1, 'admin', NULL, '/platform/operator/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 15:10:47', 34);
INSERT INTO `sys_oper_log` VALUES (250, '银联配置', 5, 'com.lgjy.system.controller.MiniUnionPayConfigController.export()', 'POST', 1, 'admin', NULL, '/platform/unionPayConfig/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 15:24:03', 895);
INSERT INTO `sys_oper_log` VALUES (251, '停车订单', 5, 'com.lgjy.system.controller.MiniParkingOrderController.export()', 'POST', 1, 'admin', NULL, '/parkingOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 15:24:14', 34);
INSERT INTO `sys_oper_log` VALUES (252, '运营商信息', 5, 'com.lgjy.system.controller.MiniOperatorController.export()', 'POST', 1, 'admin', NULL, '/platform/operator/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 15:27:14', 24);
INSERT INTO `sys_oper_log` VALUES (253, '优惠券模板', 5, 'com.lgjy.system.controller.MiniCouponController.export()', 'POST', 1, 'admin', NULL, '/coupon/template/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-24 15:31:21', 32);
INSERT INTO `sys_oper_log` VALUES (254, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:42:20\",\"cssClass\":\"\",\"default\":false,\"dictCode\":164,\"dictLabel\":\"支付宝\",\"dictSort\":1,\"dictType\":\"pay_method\",\"dictValue\":\"1\",\"isDefault\":\"N\",\"listClass\":\"primary\",\"params\":{},\"remark\":\"支付宝支付方式\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:09:51', 32);
INSERT INTO `sys_oper_log` VALUES (255, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:42:20\",\"cssClass\":\"\",\"default\":true,\"dictCode\":163,\"dictLabel\":\"微信支付\",\"dictSort\":2,\"dictType\":\"pay_method\",\"dictValue\":\"2\",\"isDefault\":\"Y\",\"listClass\":\"success\",\"params\":{},\"remark\":\"微信支付方式\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:09:57', 12);
INSERT INTO `sys_oper_log` VALUES (256, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:42:07\",\"cssClass\":\"\",\"default\":false,\"dictCode\":160,\"dictLabel\":\"已支付\",\"dictSort\":5,\"dictType\":\"pay_status\",\"dictValue\":\"5\",\"isDefault\":\"N\",\"listClass\":\"success\",\"params\":{},\"remark\":\"已支付状态\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:12:53', 14);
INSERT INTO `sys_oper_log` VALUES (257, '字典数据', 1, 'com.lgjy.system.controller.SysDictDataController.add()', 'POST', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"进行中\",\"dictSort\":1,\"dictType\":\"pay_status\",\"dictValue\":\"1\",\"listClass\":\"primary\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:13:26', 12);
INSERT INTO `sys_oper_log` VALUES (258, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-26 10:13:26\",\"default\":false,\"dictCode\":175,\"dictLabel\":\"进行中\",\"dictSort\":1,\"dictType\":\"pay_status\",\"dictValue\":\"1\",\"isDefault\":\"N\",\"listClass\":\"primary\",\"params\":{},\"remark\":\"进行中的状态\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:13:40', 13);
INSERT INTO `sys_oper_log` VALUES (259, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:42:07\",\"cssClass\":\"\",\"default\":true,\"dictCode\":159,\"dictLabel\":\"未支付\",\"dictSort\":0,\"dictType\":\"pay_status\",\"dictValue\":\"0\",\"isDefault\":\"Y\",\"listClass\":\"warning\",\"params\":{},\"remark\":\"未支付状态\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:13:47', 13);
INSERT INTO `sys_oper_log` VALUES (260, '字典数据', 1, 'com.lgjy.system.controller.SysDictDataController.add()', 'POST', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"支付中\",\"dictSort\":2,\"dictType\":\"pay_status\",\"dictValue\":\"2\",\"listClass\":\"warning\",\"params\":{},\"remark\":\"支付中状态\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:14:22', 13);
INSERT INTO `sys_oper_log` VALUES (261, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:42:07\",\"cssClass\":\"\",\"default\":false,\"dictCode\":161,\"dictLabel\":\"支付失败\",\"dictSort\":3,\"dictType\":\"pay_status\",\"dictValue\":\"3\",\"isDefault\":\"N\",\"listClass\":\"danger\",\"params\":{},\"remark\":\"支付失败状态\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:14:36', 15);
INSERT INTO `sys_oper_log` VALUES (262, '字典数据', 2, 'com.lgjy.system.controller.SysDictDataController.edit()', 'PUT', 1, 'admin', NULL, '/dict/data', '127.0.0.1', '', '{\"createBy\":\"admin\",\"createTime\":\"2025-06-20 15:42:07\",\"cssClass\":\"\",\"default\":false,\"dictCode\":162,\"dictLabel\":\"已退款\",\"dictSort\":4,\"dictType\":\"pay_status\",\"dictValue\":\"4\",\"isDefault\":\"N\",\"listClass\":\"info\",\"params\":{},\"remark\":\"已退款状态\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 10:14:40', 16);
INSERT INTO `sys_oper_log` VALUES (263, '菜单管理', 2, 'com.lgjy.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-06 14:10:37\",\"icon\":\"chart\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2006,\"menuName\":\"运营管理\",\"menuType\":\"M\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"operation\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 10:05:42', 36);
INSERT INTO `sys_oper_log` VALUES (264, '菜单管理', 2, 'com.lgjy.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-09 14:45:53\",\"icon\":\"money\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2026,\"menuName\":\"会员管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"vip\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 10:06:06', 12);
INSERT INTO `sys_oper_log` VALUES (265, '菜单管理', 2, 'com.lgjy.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-09 12:37:01\",\"icon\":\"table\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"平台管理\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":0,\"path\":\"platform\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 10:06:50', 10);
INSERT INTO `sys_oper_log` VALUES (266, '商户信息', 1, 'com.lgjy.system.controller.MiniMerchantController.add()', 'POST', 1, 'admin', NULL, '/merchant/info', '127.0.0.1', '', '{\"businessLicense\":\"91723089127339812\",\"headCard\":\"36233020021236\",\"headName\":\"十分的\",\"headPhone\":\"122\",\"id\":\"24\",\"merchantName\":\"李某某\",\"moveTime\":\"2025-06-27 12:28:15\",\"params\":{},\"warehouseId\":\"723055983177371648\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 12:28:17', 31);
INSERT INTO `sys_oper_log` VALUES (267, '商户信息', 1, 'com.lgjy.system.controller.MiniMerchantController.add()', 'POST', 1, 'admin', NULL, '/merchant/info', '127.0.0.1', '', '{\"businessLicense\":\"************-9123\",\"headCard\":\"362330200212093778\",\"headName\":\"李某某\",\"headPhone\":\"***********\",\"id\":\"25\",\"merchantName\":\"虹桥火车\",\"moveTime\":\"2025-06-27 12:33:20\",\"params\":{},\"warehouseId\":\"723055983177371648\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 12:33:48', 17);
INSERT INTO `sys_oper_log` VALUES (268, '商户信息', 3, 'com.lgjy.system.controller.MiniMerchantController.remove()', 'DELETE', 1, 'admin', NULL, '/merchant/info/24', '127.0.0.1', '', '[24]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 12:33:53', 12);
INSERT INTO `sys_oper_log` VALUES (269, '商户信息', 1, 'com.lgjy.system.controller.MiniMerchantController.add()', 'POST', 1, 'admin', NULL, '/merchant/info', '127.0.0.1', '', '{\"businessLicense\":\"19028312-7381\",\"createBy\":\"1\",\"headCard\":\"362330200212093788\",\"headName\":\"111\",\"headPhone\":\"***********\",\"id\":\"26\",\"merchantName\":\"打卡苏\",\"moveTime\":\"2025-06-27 12:45:26\",\"params\":{},\"warehouseId\":\"723055983177371648\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 12:45:34', 30);
INSERT INTO `sys_oper_log` VALUES (270, '商户信息', 2, 'com.lgjy.system.controller.MiniMerchantController.edit()', 'PUT', 1, 'admin', NULL, '/merchant/info', '127.0.0.1', '', '{\"businessLicense\":\"19028312-7381\",\"companyName\":\"上海申能物业管理有限公司\",\"createBy\":\"1\",\"createTime\":\"2025-06-27 12:45:34\",\"createdBy\":\"超级管理员\",\"deleteFlag\":0,\"headCard\":\"362330200212093788\",\"headName\":\"11222\",\"headPhone\":\"***********\",\"id\":\"26\",\"merchantName\":\"打卡苏\",\"moveTime\":\"2025-06-27 12:45:26\",\"params\":{},\"projectName\":\"江山路\",\"updateBy\":\"1\",\"updateTime\":\"2025-06-27 12:45:34\",\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 12:45:48', 17);
INSERT INTO `sys_oper_log` VALUES (271, '商户信息', 3, 'com.lgjy.system.controller.MiniMerchantController.remove()', 'DELETE', 1, 'admin', NULL, '/merchant/info/25', '127.0.0.1', '', '[25]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 12:45:54', 12);
INSERT INTO `sys_oper_log` VALUES (272, '商户信息', 5, 'com.lgjy.system.controller.MiniMerchantController.export()', 'POST', 1, 'admin', NULL, '/merchant/info/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-27 12:48:58', 757);
INSERT INTO `sys_oper_log` VALUES (273, '会员信息', 1, 'com.lgjy.system.controller.MiniVipMemberController.add()', 'POST', 1, 'admin', NULL, '/vip/member', '127.0.0.1', '', '{\"beginVipTime\":\"2025-07-01 13:21:40\",\"deleteFlag\":0,\"endVipTime\":\"2025-07-31 00:00:00\",\"id\":486,\"params\":{},\"parkingLotId\":723055983177371648,\"plateNo\":\"赣A11111\",\"vipType\":1,\"warehouseId\":723055983177371648}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-01 13:22:33', 43);
INSERT INTO `sys_oper_log` VALUES (274, '会员信息', 1, 'com.lgjy.system.controller.MiniVipMemberController.add()', 'POST', 1, 'admin', NULL, '/vip/member', '127.0.0.1', '', '{\"beginVipTime\":\"2025-07-01 00:00:00\",\"deleteFlag\":0,\"endVipTime\":\"2025-07-01 13:48:16\",\"id\":488,\"params\":{},\"parkingLotId\":723055983177371648,\"phoneNumber\":\"15270342658\",\"plateNo\":\"沪S67445\",\"remark\":\"123\",\"vipType\":5,\"warehouseId\":723055983177371648}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-01 13:48:24', 32);
INSERT INTO `sys_oper_log` VALUES (275, '部门管理', 1, 'com.lgjy.system.controller.SysDeptController.add()', 'POST', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"111\",\"orderNum\":0,\"params\":{},\"parentId\":100,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-02 11:04:53', 31);
INSERT INTO `sys_oper_log` VALUES (276, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"元旦狂欢节\",\"createBy\":\"超级管理员\",\"createTime\":\"2025-06-06 13:58:40\",\"deleteFlag\":0,\"id\":3,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/02/ChatGPT_Image_2025年5月23日_20_32_09_20250702212046A001.png\",\"remark\":\"元旦活动广告\",\"status\":1,\"updateBy\":\"1\",\"updateTime\":\"2025-06-06 15:04:44\",\"url\":\"https://www.example.com/new-year\"}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Incorrect integer value: \'超级管理员\' for column \'create_by\' at row 1\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniAdvertConfigMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniAdvertConfigMapper.updateMiniAdvertConfig-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update mini_advert_config          SET remark = ?,             advert_title = ?,             url = ?,             pic_url = ?,             status = ?,             delete_flag = ?,             create_by = ?,             create_time = ?,             update_by = ?,             update_time = ?          where id = ?\r\n### Cause: java.sql.SQLException: Incorrect integer value: \'超级管理员\' for column \'create_by\' at row 1\n; uncategorized SQLException; SQL state [HY000]; error code [1366]; Incorrect integer value: \'超级管理员\' for column \'create_by\' at row 1; nested exception is java.sql.SQLException: Incorrect integer value: \'超级管理员\' for column \'create_by\' at row 1', '2025-07-02 21:20:49', 315);
INSERT INTO `sys_oper_log` VALUES (277, '广告配置信息', 1, 'com.lgjy.system.controller.MiniAdvertConfigController.add()', 'POST', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"123就应该\",\"createBy\":\"1\",\"deleteFlag\":0,\"id\":8,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/02/ChatGPT_Image_2025年5月22日_21_07_13_20250702212258A002.png\",\"status\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-02 21:23:12', 24);
INSERT INTO `sys_oper_log` VALUES (278, '广告配置信息', 1, 'com.lgjy.system.controller.MiniAdvertConfigController.add()', 'POST', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"1231\",\"deleteFlag\":0,\"id\":9,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月22日_21_07_13_20250703001422A003.png\",\"status\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 00:14:24', 23);
INSERT INTO `sys_oper_log` VALUES (279, '广告配置信息', 3, 'com.lgjy.system.controller.MiniAdvertConfigController.remove()', 'DELETE', 1, 'admin', NULL, '/advertConfig/9', '127.0.0.1', '', '[9]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 00:14:27', 10);
INSERT INTO `sys_oper_log` VALUES (280, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"123就应该\",\"createBy\":1,\"createTime\":\"2025-07-02 21:23:12\",\"deleteFlag\":0,\"id\":8,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/02/ChatGPT_Image_2025年5月22日_21_07_13_20250702212258A002.png\",\"status\":1,\"updateTime\":\"2025-07-02 21:23:12\",\"url\":\"1231\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 00:14:33', 8);
INSERT INTO `sys_oper_log` VALUES (281, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"优化测试广告1\",\"createBy\":1,\"createTime\":\"2025-06-06 15:07:27\",\"deleteFlag\":0,\"id\":7,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月23日_07_30_16_20250703001439A004.png\",\"remark\":\"测试更新操作\",\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-06-06 15:07:42\",\"url\":\"https://test.com\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 00:14:40', 8);
INSERT INTO `sys_oper_log` VALUES (282, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"123就应该\",\"createBy\":1,\"createTime\":\"2025-07-02 21:23:12\",\"deleteFlag\":0,\"id\":8,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/02/ChatGPT_Image_2025年5月22日_21_07_13_20250702212258A002.png\",\"status\":1,\"updateTime\":\"2025-07-02 21:23:12\",\"url\":\"http://test.com\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 00:25:00', 29);
INSERT INTO `sys_oper_log` VALUES (283, '场库信息', 1, 'com.lgjy.system.controller.MiniWarehouseController.add()', 'POST', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"children\":[],\"id\":\"728295218234920960\",\"operatorId\":\"1871825000000000001\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"123156\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"userId\":1,\"warehouseName\":\"123\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 01:05:11', 34);
INSERT INTO `sys_oper_log` VALUES (284, '特殊会员', 1, 'com.lgjy.system.controller.MiniSpecialUserController.add()', 'POST', 1, 'admin', NULL, '/special/user', '127.0.0.1', '', '{\"nickName\":\"张三\",\"params\":{},\"phoneNumber\":\"15772687594\",\"plateNo\":\"Goi98127\",\"userType\":\"VIP客户\"}', NULL, 1, '\r\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'su.remark\' in \'field list\'\r\n### The error may exist in file [F:\\parking\\park-api\\lgjy-modules\\lgjy-system\\target\\classes\\mapper\\system\\MiniSpecialUserMapper.xml]\r\n### The error may involve com.lgjy.system.mapper.MiniSpecialUserMapper.checkPhoneNumberUnique-Inline\r\n### The error occurred while setting parameters\r\n### SQL: select su.id, su.nick_name, su.phone_number, su.plate_no, su.user_type, su.delete_flag,                su.create_by, coalesce(cu.nick_name, cu.user_name, su.create_by) as create_by_name, su.create_time,                su.update_by, coalesce(uu.nick_name, uu.user_name, su.update_by) as update_by_name, su.update_time,                su.remark         from mini_special_user su         left join sys_user cu on su.create_by = cu.user_id and cu.delete_flag = \'0\'         left join sys_user uu on su.update_by = uu.user_id and uu.delete_flag = \'0\'               where su.phone_number = ? and su.delete_flag = 0 limit 1\r\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'su.remark\' in \'field list\'\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'su.remark\' in \'field list\'', '2025-07-03 08:23:43', 80);
INSERT INTO `sys_oper_log` VALUES (285, '特殊会员', 1, 'com.lgjy.system.controller.MiniSpecialUserController.add()', 'POST', 1, 'admin', NULL, '/special/user', '127.0.0.1', '', '{\"nickName\":\"张三\",\"params\":{},\"phoneNumber\":\"15772687594\",\"plateNo\":\"赣A11111\",\"remark\":\"123\",\"userType\":\"VIP客户\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 08:26:21', 12);
INSERT INTO `sys_oper_log` VALUES (286, '特殊会员', 2, 'com.lgjy.system.controller.MiniSpecialUserController.edit()', 'PUT', 1, 'admin', NULL, '/special/user', '127.0.0.1', '', '{\"createBy\":1,\"createByName\":\"超级管理员\",\"createTime\":\"2025-07-03 08:26:21\",\"deleteFlag\":0,\"id\":\"1\",\"nickName\":\"张三\",\"params\":{},\"phoneNumber\":\"15772687594\",\"plateNo\":\"赣A11112\",\"remark\":\"123\",\"updateBy\":1,\"updateByName\":\"超级管理员\",\"updateTime\":\"2025-07-03 08:26:21\",\"userType\":\"VIP客户\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 08:39:12', 11);
INSERT INTO `sys_oper_log` VALUES (287, '特殊会员', 1, 'com.lgjy.system.controller.MiniSpecialUserController.add()', 'POST', 1, 'admin', NULL, '/special/user', '127.0.0.1', '', '{\"nickName\":\"里没\",\"params\":{},\"phoneNumber\":\"15765987546\",\"plateNo\":\"赣A11113\",\"remark\":\"孙菲菲违法\",\"userType\":\"集团客户\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 08:39:39', 9);
INSERT INTO `sys_oper_log` VALUES (288, '特殊会员', 2, 'com.lgjy.system.controller.MiniSpecialUserController.edit()', 'PUT', 1, 'admin', NULL, '/special/user', '127.0.0.1', '', '{\"createTime\":\"2025-07-03 08:39:39\",\"deleteFlag\":0,\"id\":\"2\",\"nickName\":\"里没\",\"params\":{},\"phoneNumber\":\"15765987540\",\"plateNo\":\"赣A11113\",\"remark\":\"孙菲菲违法\",\"updateTime\":\"2025-07-03 08:39:39\",\"userType\":\"集团客户\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 08:39:48', 8);
INSERT INTO `sys_oper_log` VALUES (289, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-02 19:45:54\",\"deleteFlag\":0,\"id\":91,\"packageName\":\"著雨苑月卡\",\"packagePrice\":160,\"packageStatus\":1,\"packageType\":30,\"params\":{},\"remark\":\"著雨苑住户月度套餐\",\"updateTime\":\"2025-07-02 20:07:05\",\"vipType\":0,\"warehouseId\":1871825000000001000,\"warehouseName\":\"著雨苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 08:44:29', 23);
INSERT INTO `sys_oper_log` VALUES (290, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-02 19:45:54\",\"deleteFlag\":0,\"id\":91,\"packageName\":\"著雨苑月卡\",\"packagePrice\":160,\"packageStatus\":1,\"packageType\":30,\"params\":{},\"remark\":\"著雨苑住户月度套餐\",\"updateTime\":\"2025-07-03 08:44:29\",\"vipType\":0,\"warehouseId\":1871825000000001002}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 08:44:43', 6);
INSERT INTO `sys_oper_log` VALUES (291, '场库信息', 3, 'com.lgjy.system.controller.MiniWarehouseController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/warehouse/728295218234920960,1871825000000001001,1871825000000001002,1871825000000001003,1871825000000001004,1871825000000001005,723055983177371648', '127.0.0.1', '', '[728295218234920960,1871825000000001001,1871825000000001002,1871825000000001003,1871825000000001004,1871825000000001005,723055983177371648]', NULL, 1, '场库\'江山路\'下还有管理员，不能删除', '2025-07-03 10:16:05', 26);
INSERT INTO `sys_oper_log` VALUES (292, '场库信息', 3, 'com.lgjy.system.controller.MiniWarehouseController.remove()', 'DELETE', 1, 'admin', NULL, '/platform/warehouse/728295218234920960,1871825000000001001,1871825000000001002,1871825000000001003,1871825000000001004,1871825000000001005,723055983177371648', '127.0.0.1', '', '[728295218234920960,1871825000000001001,1871825000000001002,1871825000000001003,1871825000000001004,1871825000000001005,723055983177371648]', NULL, 1, '场库\'江山路\'下还有管理员，不能删除', '2025-07-03 10:17:12', 10);
INSERT INTO `sys_oper_log` VALUES (293, '字典类型', 9, 'com.lgjy.system.controller.SysDictTypeController.clearCache()', 'DELETE', 1, NULL, NULL, '/dict/type/clearCache/pay_type', '127.0.0.1', '', '\"pay_type\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:25:15', 19);
INSERT INTO `sys_oper_log` VALUES (294, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月22日_21_07_13_20250703103221A001.png\\\"]\",\"children\":[],\"createTime\":\"2025-07-01 10:03:15\",\"deleteFlag\":0,\"id\":\"1871825000000001005\",\"operatorId\":\"1871825000000000003\",\"operatorName\":\"上海临港漕河泾物业服务有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"林彩苑\",\"smartsType\":0,\"status\":1,\"totalParking\":3,\"updateTime\":\"2025-07-01 10:03:15\",\"userId\":1,\"warehouseName\":\"林彩苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:32:26', 23);
INSERT INTO `sys_oper_log` VALUES (295, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_12_11_20250703103235A002.png\\\"]\",\"children\":[],\"createTime\":\"2025-07-01 10:03:15\",\"deleteFlag\":0,\"id\":\"1871825000000001005\",\"operatorId\":\"1871825000000000003\",\"operatorName\":\"上海临港漕河泾物业服务有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"林彩苑\",\"smartsType\":0,\"status\":1,\"totalParking\":3,\"updateTime\":\"2025-07-03 10:32:26\",\"userId\":1,\"warehouseName\":\"林彩苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:32:36', 7);
INSERT INTO `sys_oper_log` VALUES (296, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_30_24_20250703103900A003.png\\\"]\",\"children\":[],\"createTime\":\"2025-07-01 10:03:15\",\"deleteFlag\":0,\"id\":\"1871825000000001005\",\"operatorId\":\"1871825000000000003\",\"operatorName\":\"上海临港漕河泾物业服务有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"林彩苑\",\"smartsType\":0,\"status\":1,\"totalParking\":3,\"updateTime\":\"2025-07-03 10:32:36\",\"userId\":1,\"warehouseName\":\"林彩苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:39:00', 8);
INSERT INTO `sys_oper_log` VALUES (297, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_12_11_20250703103907A004.png\\\"]\",\"children\":[],\"createTime\":\"2025-07-01 10:03:15\",\"deleteFlag\":0,\"id\":\"1871825000000001004\",\"operatorId\":\"1871825000000000002\",\"operatorName\":\"上海聚悦资产管理有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"著雨苑\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"updateTime\":\"2025-07-01 10:03:15\",\"userId\":1,\"warehouseName\":\"著雨苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:39:10', 6);
INSERT INTO `sys_oper_log` VALUES (298, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_53_20_20250703103942A005.png\\\"]\",\"children\":[],\"createTime\":\"2025-07-01 10:03:15\",\"deleteFlag\":0,\"id\":\"1871825000000001003\",\"operatorId\":\"1871825000000000001\",\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"泽辉苑\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"updateTime\":\"2025-07-01 10:03:15\",\"userId\":1,\"warehouseName\":\"泽辉苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:39:43', 8);
INSERT INTO `sys_oper_log` VALUES (299, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-07-03 01:05:11\",\"deleteFlag\":0,\"id\":\"728295218234920960\",\"operatorId\":\"1871825000000000001\",\"operatorName\":\"上海申能物业管理有限公司\",\"params\":{},\"parentId\":\"1871825000000001001\",\"projectName\":\"123156\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"updateTime\":\"2025-07-03 01:05:11\",\"userId\":1,\"warehouseName\":\"123\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:47:06', 9);
INSERT INTO `sys_oper_log` VALUES (300, '场库信息', 1, 'com.lgjy.system.controller.MiniWarehouseController.add()', 'POST', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"children\":[],\"id\":\"728441884367785984\",\"operatorId\":\"1871825000000000002\",\"params\":{},\"parentId\":\"1871825000000001001\",\"status\":1,\"totalParking\":3,\"userId\":1,\"warehouseName\":\"12313\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:47:59', 9);
INSERT INTO `sys_oper_log` VALUES (301, '场库信息', 1, 'com.lgjy.system.controller.MiniWarehouseController.add()', 'POST', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"children\":[],\"id\":\"728441935903199232\",\"operatorId\":\"1871825000000000003\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"1231\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"userId\":1,\"warehouseName\":\"123123123\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 10:48:11', 8);
INSERT INTO `sys_oper_log` VALUES (302, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"address\":\"213123123123123\",\"areaCode\":\"120101\",\"carouselImages\":\"[\\\"http://127.0.0.1:9300/statics/2025/07/03/ChatGPT_Image_2025年5月28日_19_30_24_20250703103900A003.png\\\"]\",\"children\":[],\"cityCode\":\"120100\",\"createTime\":\"2025-07-01 10:03:15\",\"deleteFlag\":0,\"id\":\"1871825000000001005\",\"operatorId\":\"1871825000000000003\",\"operatorName\":\"上海临港漕河泾物业服务有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"林彩苑\",\"provinceCode\":\"120000\",\"smartsType\":0,\"status\":1,\"totalParking\":3,\"updateTime\":\"2025-07-03 10:39:00\",\"userId\":1,\"warehouseName\":\"林彩苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 11:01:27', 22);
INSERT INTO `sys_oper_log` VALUES (303, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-07-03 10:48:11\",\"deleteFlag\":0,\"id\":\"728441935903199232\",\"operatorId\":\"1871825000000000003\",\"operatorName\":\"上海临港漕河泾物业服务有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"1231\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"updateTime\":\"2025-07-03 10:48:11\",\"userId\":1,\"warehouseName\":\"123123123\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 12:39:13', 21);
INSERT INTO `sys_oper_log` VALUES (304, '场库信息', 2, 'com.lgjy.system.controller.MiniWarehouseController.edit()', 'PUT', 1, 'admin', NULL, '/platform/warehouse', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-07-03 10:48:11\",\"deleteFlag\":0,\"id\":\"728441935903199232\",\"operatorId\":\"1871825000000000003\",\"operatorName\":\"上海临港漕河泾物业服务有限公司\",\"params\":{},\"parentId\":\"0\",\"projectName\":\"1231\",\"smartsType\":0,\"status\":1,\"totalParking\":5,\"updateTime\":\"2025-07-03 12:39:13\",\"userId\":1,\"warehouseName\":\"123123123\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 12:45:12', 9);
INSERT INTO `sys_oper_log` VALUES (305, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-02 19:45:54\",\"deleteFlag\":0,\"id\":91,\"packageName\":\"著雨苑月卡\",\"packagePrice\":160,\"packageStatus\":1,\"packageType\":30,\"params\":{},\"remark\":\"著雨苑住户月度套餐\",\"updateTime\":\"2025-07-03 08:44:43\",\"vipType\":0,\"warehouseId\":1871825000000002002,\"warehouseName\":\"LIN舍公寓\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 13:24:05', 59);
INSERT INTO `sys_oper_log` VALUES (306, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-02 19:45:54\",\"deleteFlag\":0,\"id\":91,\"packageName\":\"著雨苑月卡\",\"packagePrice\":160,\"packageStatus\":1,\"packageType\":30,\"params\":{},\"parentWarehouseName\":\"LIN舍公寓\",\"remark\":\"著雨苑住户月度套餐\",\"updateTime\":\"2025-07-03 13:24:05\",\"vipType\":0,\"warehouseId\":1871825000000002002,\"warehouseName\":\"LIN舍公寓-主停车场\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 13:32:00', 25);
INSERT INTO `sys_oper_log` VALUES (307, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-02 19:45:54\",\"deleteFlag\":0,\"id\":92,\"packageName\":\"著雨苑季卡\",\"packagePrice\":450,\"packageStatus\":1,\"packageType\":90,\"params\":{},\"remark\":\"著雨苑住户季度套餐\",\"updateTime\":\"2025-07-02 20:07:05\",\"vipType\":2,\"warehouseId\":1871825000000002004,\"warehouseName\":\"著雨苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 13:32:19', 8);
INSERT INTO `sys_oper_log` VALUES (308, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-02 19:45:54\",\"deleteFlag\":0,\"id\":91,\"packageName\":\"著雨苑月卡\",\"packagePrice\":160,\"packageStatus\":1,\"packageType\":30,\"params\":{},\"parentWarehouseName\":\"LIN舍公寓\",\"remark\":\"著雨苑住户月度套餐\",\"updateTime\":\"2025-07-03 13:32:00\",\"vipType\":0,\"warehouseId\":1871825000000002002,\"warehouseName\":\"LIN舍公寓-主停车场\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 14:12:02', 31);
INSERT INTO `sys_oper_log` VALUES (309, '会员套餐配置', 3, 'com.lgjy.system.controller.MiniVipPackageController.remove()', 'DELETE', 1, 'admin', NULL, '/vip/package/91,92,93,80,81,82,83,84,85', '127.0.0.1', '', '[91,92,93,80,81,82,83,84,85]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 14:34:28', 19);
INSERT INTO `sys_oper_log` VALUES (310, '会员套餐配置', 3, 'com.lgjy.system.controller.MiniVipPackageController.remove()', 'DELETE', 1, 'admin', NULL, '/vip/package/86,87,88,89', '127.0.0.1', '', '[86,87,88,89]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-03 14:34:34', 5);
INSERT INTO `sys_oper_log` VALUES (311, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"123就应该\",\"createBy\":1,\"createTime\":\"2025-07-02 21:23:12\",\"deleteFlag\":0,\"id\":8,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/02/ChatGPT_Image_2025年5月22日_21_07_13_20250702212258A002.png\",\"remark\":\"123123\",\"status\":1,\"updateTime\":\"2025-07-02 21:23:12\",\"url\":\"http://tes123123t.com\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-05 19:37:09', 62);
INSERT INTO `sys_oper_log` VALUES (312, '停车订单', 5, 'com.lgjy.system.controller.MiniParkingOrderController.export()', 'POST', 1, 'admin', NULL, '/parkingOrder/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-05 19:51:11', 1189);
INSERT INTO `sys_oper_log` VALUES (313, '停车订单', 1, 'com.lgjy.system.controller.MiniParkingOrderController.add()', 'POST', 1, 'admin', NULL, '/parkingOrder', '127.0.0.1', '', '{\"actualPayment\":120,\"beginParkingTime\":\"2025-07-05 00:00:00\",\"carType\":\"临时车\",\"createTime\":\"2025-07-05 19:53:54\",\"deleteFlag\":0,\"discountAmount\":3,\"endParkingTime\":\"2025-07-31 00:00:00\",\"id\":\"729304044857856000\",\"params\":{},\"parkingDuration\":1231,\"payStatus\":0,\"payType\":0,\"paymentAmount\":123,\"plateNo\":\"沪AS1231\",\"warehouseId\":\"728441935903199232\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-05 19:53:54', 32);
INSERT INTO `sys_oper_log` VALUES (314, '停车订单', 3, 'com.lgjy.system.controller.MiniParkingOrderController.remove()', 'DELETE', 1, 'admin', NULL, '/parkingOrder/729304044857856000', '127.0.0.1', '', '[729304044857856000]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-05 19:54:01', 28);
INSERT INTO `sys_oper_log` VALUES (315, '特殊会员', 2, 'com.lgjy.system.controller.MiniSpecialUserController.edit()', 'PUT', 1, 'admin', NULL, '/special/user', '127.0.0.1', '', '{\"createTime\":\"2025-07-03 08:39:39\",\"deleteFlag\":0,\"id\":\"2\",\"nickName\":\"里没\",\"params\":{},\"phoneNumber\":\"15765987540\",\"plateNo\":\"赣A11112\",\"remark\":\"孙菲菲违法\",\"updateTime\":\"2025-07-03 08:39:48\",\"userType\":\"集团客户\"}', '{\"msg\":\"修改特殊会员\'赣A11112\'失败，车牌号已存在\",\"code\":500}', 0, NULL, '2025-07-05 19:55:51', 7);
INSERT INTO `sys_oper_log` VALUES (316, '特殊会员', 2, 'com.lgjy.system.controller.MiniSpecialUserController.edit()', 'PUT', 1, 'admin', NULL, '/special/user', '127.0.0.1', '', '{\"createTime\":\"2025-07-03 08:39:39\",\"deleteFlag\":0,\"id\":\"2\",\"nickName\":\"里没\",\"params\":{},\"phoneNumber\":\"15765987540\",\"plateNo\":\"赣A11110\",\"remark\":\"孙菲菲违法\",\"updateTime\":\"2025-07-03 08:39:48\",\"userType\":\"集团客户\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-05 19:55:58', 12);
INSERT INTO `sys_oper_log` VALUES (317, '停车订单', 2, 'com.lgjy.system.controller.MiniParkingOrderController.edit()', 'PUT', 1, 'admin', NULL, '/parkingOrder', '127.0.0.1', '', '{\"actualPayment\":1,\"beginParkingTime\":\"2025-06-26 09:32:33\",\"carType\":\"临时车\",\"createTime\":\"2025-06-26 10:03:09\",\"deleteFlag\":0,\"discountAmount\":0,\"endParkingTime\":\"2025-06-26 10:03:09\",\"id\":\"725893887683731456\",\"openId\":\"oi_4B7EopE5aoD07esmrJuvCN5GA\",\"params\":{},\"parkingDuration\":31,\"payStatus\":5,\"payStatusName\":\"未知\",\"payType\":4,\"payTypeName\":\"支付宝\",\"paymentAmount\":1,\"paymentTime\":\"2025-06-26 10:27:25\",\"plateNo\":\"沪AS1234\",\"tradeId\":\"37Y120250626100309281065388P\",\"updateTime\":\"2025-07-05 20:23:57\",\"userId\":\"722315301991092224\",\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-05 20:23:57', 34);
INSERT INTO `sys_oper_log` VALUES (318, '停车订单', 2, 'com.lgjy.system.controller.MiniParkingOrderController.edit()', 'PUT', 1, 'admin', NULL, '/parkingOrder', '127.0.0.1', '', '{\"actualPayment\":1,\"beginParkingTime\":\"2025-06-26 09:32:33\",\"carType\":\"临时车\",\"createTime\":\"2025-06-26 10:03:09\",\"deleteFlag\":0,\"discountAmount\":0,\"endParkingTime\":\"2025-06-26 10:03:09\",\"id\":\"725893887683731456\",\"openId\":\"oi_4B7EopE5aoD07esmrJuvCN5GA\",\"params\":{},\"parkingDuration\":31,\"payStatus\":5,\"payStatusName\":\"未知\",\"payType\":2,\"payTypeName\":\"免费\",\"paymentAmount\":1,\"paymentTime\":\"2025-06-26 10:27:25\",\"plateNo\":\"沪AS1234\",\"tradeId\":\"37Y120250626100309281065388P\",\"updateTime\":\"2025-07-05 20:24:11\",\"userId\":\"722315301991092224\",\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-05 20:24:11', 14);
INSERT INTO `sys_oper_log` VALUES (319, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, NULL, NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 18:00:00\",\"endTime\":\"2025-07-08 20:00:00\",\"name\":\"????\",\"params\":{},\"phoneNumber\":\"13800138000\",\"plateNo\":\"??A001\",\"remark\":\"?????\",\"warehouseId\":1}', '{\"msg\":\"推送到司卓系统失败：请输入正确的场库号\",\"code\":500}', 0, NULL, '2025-07-08 18:44:53', 1254);
INSERT INTO `sys_oper_log` VALUES (320, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, NULL, NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 18:00:00\",\"endTime\":\"2025-07-08 20:00:00\",\"name\":\"????\",\"params\":{},\"phoneNumber\":\"13800138000\",\"plateNo\":\"??A001\",\"remark\":\"?????\",\"warehouseId\":1}', '{\"msg\":\"推送到司卓系统失败：请输入正确的场库号\",\"code\":500}', 0, NULL, '2025-07-08 18:45:08', 11);
INSERT INTO `sys_oper_log` VALUES (321, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, 'admin', NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 18:45:28\",\"endTime\":\"2025-07-09 00:00:00\",\"name\":\"张三\",\"params\":{},\"phoneNumber\":\"15272948565\",\"plateNo\":\"沪12345\",\"remark\":\"123\",\"warehouseId\":1871825000000002002}', '{\"msg\":\"推送到司卓系统失败：请输入正确的场库号\",\"code\":500}', 0, NULL, '2025-07-08 18:45:35', 768);
INSERT INTO `sys_oper_log` VALUES (322, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, 'admin', NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 19:32:19\",\"endTime\":\"2025-07-08 19:32:21\",\"name\":\"张三\",\"params\":{},\"phoneNumber\":\"15236985623\",\"plateNo\":\"沪AS1234\",\"remark\":\"123\",\"warehouseId\":1871825000000001002}', NULL, 1, 'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'plateNo\' not found. Available parameters are [arg1, arg0, param1, param2]', '2025-07-08 19:32:24', 17);
INSERT INTO `sys_oper_log` VALUES (323, '黑名单管理', 1, 'com.lgjy.system.controller.MiniBlacklistController.add()', 'POST', 1, 'admin', NULL, '/owner/blacklist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 20:39:26\",\"endTime\":\"2025-08-30 00:00:00\",\"imgUrl\":\"123\",\"name\":\"李四\",\"params\":{},\"phoneNumber\":\"15794568795\",\"plateNo\":\"沪AS1234\",\"warehouseId\":1871825000000001002}', '{\"msg\":\"推送到司卓系统失败：黑名单保存失败[404] during [POST] to [http://park-gate/control/saveBlackCar?parkingId=sizhuo_001&plateNum=%E6%B2%AAAS1234&endDate=Sat%20Aug%2030%2000%3A00%3A00%20CST%202025] [RemoteGateService#saveBlackCar(String,String,Date,String)]: [{\\\"timestamp\\\":\\\"2025-07-08T20:39:36.673+08:00\\\",\\\"status\\\":404,\\\"error\\\":\\\"Not Found\\\",\\\"path\\\":\\\"/control/saveBlackCar\\\"}]\",\"code\":500}', 0, NULL, '2025-07-08 20:39:36', 442);
INSERT INTO `sys_oper_log` VALUES (324, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, 'admin', NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 20:40:58\",\"endTime\":\"2025-07-18 00:00:00\",\"name\":\"张三\",\"params\":{},\"phoneNumber\":\"15762359875\",\"plateNo\":\"沪AS1234\",\"remark\":\"123\",\"warehouseId\":1871825000000001002}', NULL, 1, 'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'plateNo\' not found. Available parameters are [arg1, arg0, param1, param2]', '2025-07-08 20:41:03', 8);
INSERT INTO `sys_oper_log` VALUES (325, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, 'admin', NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 20:40:58\",\"endTime\":\"2025-07-18 00:00:00\",\"name\":\"张三\",\"params\":{},\"phoneNumber\":\"15762359875\",\"plateNo\":\"沪AS1234\",\"remark\":\"123\",\"warehouseId\":1871825000000001002}', '{\"msg\":\"推送到司卓系统失败：请输入正确的场库号\",\"code\":500}', 0, NULL, '2025-07-08 20:49:46', 172);
INSERT INTO `sys_oper_log` VALUES (326, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, 'admin', NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-08 21:27:46\",\"endTime\":\"2025-07-10 00:00:00\",\"name\":\"张三\",\"params\":{},\"phoneNumber\":\"15223689562\",\"plateNo\":\"沪AS1234\",\"remark\":\"123\",\"warehouseId\":1871825000000001002}', '{\"msg\":\"推送到司卓系统失败：请输入正确的场库号\",\"code\":500}', 0, NULL, '2025-07-08 21:27:52', 358);
INSERT INTO `sys_oper_log` VALUES (327, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.batchDisable()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/batchDisable/723394893304696800', '127.0.0.1', '', '[723394893304696800]', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:01:05', 33);
INSERT INTO `sys_oper_log` VALUES (328, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.batchDisable()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/batchDisable/723394893304696800', '127.0.0.1', '', '[723394893304696800]', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:01:07', 2);
INSERT INTO `sys_oper_log` VALUES (329, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.batchDisable()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/batchDisable/722315301991092200', '127.0.0.1', '', '[722315301991092200]', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:01:10', 3);
INSERT INTO `sys_oper_log` VALUES (330, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.batchDisable()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/batchDisable/723394893304696800', '127.0.0.1', '', '[723394893304696800]', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:01:33', 3);
INSERT INTO `sys_oper_log` VALUES (331, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.batchDisable()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/batchDisable/723394893304696800', '127.0.0.1', '', '[723394893304696800]', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:03:12', 2);
INSERT INTO `sys_oper_log` VALUES (332, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.batchDisable()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/batchDisable/723394893304696800', '127.0.0.1', '', '[723394893304696800]', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:03:20', 4);
INSERT INTO `sys_oper_log` VALUES (333, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":723394893304696800,\"status\":1,\"updateTime\":\"2025-07-09 12:06:48.344\"}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:06:48', 9);
INSERT INTO `sys_oper_log` VALUES (334, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":723394893304696800,\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-07-09 12:14:19.878\"}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:14:20', 34);
INSERT INTO `sys_oper_log` VALUES (335, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":723394893304696800,\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-07-09 12:14:24.646\"}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:14:24', 3);
INSERT INTO `sys_oper_log` VALUES (336, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":723394893304696800,\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-07-09 12:19:26.134\"}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-07-09 12:19:26', 26);
INSERT INTO `sys_oper_log` VALUES (337, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":\"723394893304696800\",\"status\":1}', '{\"msg\":\"用户不存在\",\"code\":500}', 0, NULL, '2025-07-09 12:28:37', 22);
INSERT INTO `sys_oper_log` VALUES (338, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":\"723394893304696832\",\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-07-09 12:30:06.12\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-09 12:30:06', 43);
INSERT INTO `sys_oper_log` VALUES (339, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":\"723394893304696832\",\"status\":0,\"updateBy\":1,\"updateTime\":\"2025-07-09 12:30:11.196\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-09 12:30:11', 11);
INSERT INTO `sys_oper_log` VALUES (340, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":\"723394893304696832\",\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-07-09 12:33:00.577\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-09 12:33:00', 11);
INSERT INTO `sys_oper_log` VALUES (341, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":\"723394893304696832\",\"status\":0,\"updateBy\":1,\"updateTime\":\"2025-07-09 12:33:02.66\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-09 12:33:02', 15);
INSERT INTO `sys_oper_log` VALUES (342, '菜单管理', 2, 'com.lgjy.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createTime\":\"2025-06-04 11:01:40\",\"icon\":\"tool\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3,\"menuName\":\"系统工具\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"tool\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-09 20:37:30', 24);
INSERT INTO `sys_oper_log` VALUES (343, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":\"723394893304696832\",\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-07-10 10:33:24.13\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:33:24', 25);
INSERT INTO `sys_oper_log` VALUES (344, '小程序用户', 2, 'com.lgjy.system.controller.WxUserController.changeStatus()', 'PUT', 1, 'admin', NULL, '/owner/wxuser/changeStatus', '127.0.0.1', '', '{\"id\":\"723394893304696832\",\"status\":0,\"updateBy\":1,\"updateTime\":\"2025-07-10 10:33:25.709\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:33:25', 8);
INSERT INTO `sys_oper_log` VALUES (345, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"123就应该\",\"createBy\":1,\"createTime\":\"2025-07-02 21:23:12\",\"deleteFlag\":0,\"id\":8,\"params\":{},\"picUrl\":\"http://127.0.0.1:9300/statics/2025/07/02/ChatGPT_Image_2025年5月22日_21_07_13_20250702212258A002.png\",\"remark\":\"123123\",\"status\":1,\"updateTime\":\"2025-07-02 21:23:12\",\"url\":\"http://tes1231223133t.com\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:33:33', 12);
INSERT INTO `sys_oper_log` VALUES (346, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"春节促销活动\",\"createBy\":1,\"createTime\":\"2025-06-06 13:56:05\",\"deleteFlag\":0,\"id\":1,\"params\":{},\"picUrl\":\"https://www.example.com/images/spring-sale.jpg\",\"remark\":\"测试广告1\",\"status\":1,\"updateBy\":1,\"updateTime\":\"2025-06-06 14:57:56\",\"url\":\"https://www.example.com/spring-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:33:39', 9);
INSERT INTO `sys_oper_log` VALUES (347, '广告配置信息', 2, 'com.lgjy.system.controller.MiniAdvertConfigController.edit()', 'PUT', 1, 'admin', NULL, '/advertConfig', '127.0.0.1', '', '{\"advertTitle\":\"春节促销活动\",\"createBy\":1,\"createTime\":\"2025-06-06 13:56:05\",\"deleteFlag\":0,\"id\":1,\"params\":{},\"picUrl\":\"https://www.example.com/images/spring-sale.jpg\",\"remark\":\"测试广告1\",\"status\":2,\"updateBy\":1,\"updateTime\":\"2025-06-06 14:57:56\",\"url\":\"https://www.example.com/spring-sale\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:33:42', 8);
INSERT INTO `sys_oper_log` VALUES (348, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-03 15:09:00\",\"deleteFlag\":0,\"id\":\"1001\",\"packageName\":\"江山路月卡\",\"packagePrice\":0.01,\"packageStatus\":1,\"packageType\":30,\"params\":{},\"remark\":\"普通会员月度套餐\",\"updateTime\":\"2025-07-03 15:09:00\",\"vipType\":0,\"warehouseId\":\"723055983177371600\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 09:51:15', 40);
INSERT INTO `sys_oper_log` VALUES (349, '会员套餐配置', 1, 'com.lgjy.system.controller.MiniVipPackageController.add()', 'POST', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"deleteFlag\":0,\"id\":\"1013\",\"packageName\":\"123\",\"packagePrice\":0.01,\"packageStatus\":1,\"packageType\":3,\"params\":{},\"vipType\":0,\"warehouseId\":\"723055983177371600\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 09:54:08', 19);
INSERT INTO `sys_oper_log` VALUES (350, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createTime\":\"2025-07-11 09:54:08\",\"deleteFlag\":0,\"id\":\"1013\",\"packageName\":\"123\",\"packagePrice\":0.01,\"packageStatus\":1,\"packageType\":3,\"params\":{},\"updateTime\":\"2025-07-11 09:54:08\",\"vipType\":0,\"warehouseId\":\"728441935903199232\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 09:54:42', 7);
INSERT INTO `sys_oper_log` VALUES (351, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-03 15:09:00\",\"deleteFlag\":0,\"id\":\"1001\",\"packageName\":\"江山路月卡\",\"packagePrice\":0.01,\"packageStatus\":1,\"packageType\":30,\"params\":{},\"remark\":\"普通会员月度套餐\",\"updateTime\":\"2025-07-11 09:51:15\",\"vipType\":0,\"warehouseId\":\"1871825000000002002\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 09:54:50', 7);
INSERT INTO `sys_oper_log` VALUES (352, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createTime\":\"2025-07-11 09:54:08\",\"deleteFlag\":0,\"id\":\"1013\",\"packageName\":\"123\",\"packagePrice\":0.01,\"packageStatus\":1,\"packageType\":3,\"params\":{},\"updateTime\":\"2025-07-11 09:54:42\",\"vipType\":0,\"warehouseId\":\"1871825000000002002\",\"warehouseName\":\"123123123\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 09:55:38', 6);
INSERT INTO `sys_oper_log` VALUES (353, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createTime\":\"2025-07-11 09:54:08\",\"deleteFlag\":0,\"id\":\"1013\",\"packageName\":\"123\",\"packagePrice\":0.01,\"packageStatus\":1,\"packageType\":3,\"params\":{},\"parentWarehouseName\":\"LIN舍公寓\",\"updateTime\":\"2025-07-11 09:55:38\",\"vipType\":0,\"warehouseId\":\"1871825000000001005\",\"warehouseName\":\"LIN舍公寓-主停车场\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 09:55:45', 6);
INSERT INTO `sys_oper_log` VALUES (354, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createTime\":\"2025-07-11 09:54:08\",\"deleteFlag\":0,\"id\":\"1013\",\"packageName\":\"123\",\"packagePrice\":0.01,\"packageStatus\":1,\"packageType\":3,\"params\":{},\"updateTime\":\"2025-07-11 09:55:45\",\"vipType\":0,\"warehouseId\":\"1871825000000001003\",\"warehouseName\":\"林彩苑\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 09:55:50', 5);
INSERT INTO `sys_oper_log` VALUES (355, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-03 15:09:00\",\"deleteFlag\":0,\"id\":\"1002\",\"packageName\":\"江山路季卡123\",\"packagePrice\":540,\"packageStatus\":1,\"packageType\":90,\"params\":{},\"remark\":\"集团客户季度套餐\",\"updateTime\":\"2025-07-03 15:09:00\",\"vipType\":1,\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 10:18:37', 32);
INSERT INTO `sys_oper_log` VALUES (356, '会员套餐配置', 2, 'com.lgjy.system.controller.MiniVipPackageController.edit()', 'PUT', 1, 'admin', NULL, '/vip/package', '127.0.0.1', '', '{\"createBy\":1,\"createTime\":\"2025-07-03 15:09:00\",\"deleteFlag\":0,\"id\":\"1002\",\"packageName\":\"江山路季卡\",\"packagePrice\":541,\"packageStatus\":1,\"packageType\":90,\"params\":{},\"remark\":\"集团客户季度套餐\",\"updateTime\":\"2025-07-11 10:18:37\",\"vipType\":1,\"warehouseId\":\"723055983177371648\",\"warehouseName\":\"江山路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 10:18:45', 8);
INSERT INTO `sys_oper_log` VALUES (357, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, 'admin', NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-11 00:00:00\",\"endTime\":\"2025-07-21 23:59:59\",\"name\":\"张三\",\"params\":{},\"phoneNumber\":\"15273026595\",\"plateNo\":\"沪AS9999\",\"remark\":\"123\",\"warehouseId\":723055983177371648}', '{\"msg\":\"推送到司卓系统失败：请输入正确的场库号\",\"code\":500}', 0, NULL, '2025-07-11 11:19:37', 85);
INSERT INTO `sys_oper_log` VALUES (358, '白名单管理', 1, 'com.lgjy.system.controller.MiniWhitelistController.add()', 'POST', 1, 'admin', NULL, '/owner/whitelist', '127.0.0.1', '', '{\"beginTime\":\"2025-07-11 00:00:00\",\"endTime\":\"2025-07-21 23:59:59\",\"name\":\"张三\",\"params\":{},\"phoneNumber\":\"15273026595\",\"plateNo\":\"沪AS9999\",\"remark\":\"123\",\"warehouseId\":723055983177371648}', '{\"msg\":\"推送到司卓系统失败：请输入正确的场库号\",\"code\":500}', 0, NULL, '2025-07-11 11:19:37', 2089);
INSERT INTO `sys_oper_log` VALUES (359, '车辆出入场记录', 3, 'com.lgjy.system.controller.GateParkingInfoController.remove()', 'DELETE', 1, 'admin', NULL, '/gateParkingInfo/e9c039ccb34b4d1e8a182b429299da61', '127.0.0.1', '', '[\"e9c039ccb34b4d1e8a182b429299da61\"]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-11 15:03:57', 84);
INSERT INTO `sys_oper_log` VALUES (360, '错误数据日志', 3, 'com.lgjy.system.controller.ErrorDataLogController.remove()', 'DELETE', 1, 'admin', NULL, '/errorDataLog/EDL003', '127.0.0.1', '', '[\"EDL003\"]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-16 02:35:52', 19);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'admin', '管理员', 1, '0', 1, '2025-06-04 11:01:40', 1, '2025-06-04 14:24:51', '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', 0, 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:28:06', '超级管理员');
INSERT INTO `sys_role` VALUES (2, '管理员', 'common', 2, '2', 1, 1, '0', 0, 1, '2025-06-04 11:01:40', 1, '2025-06-04 14:29:04', '普通角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, 100);
INSERT INTO `sys_role_dept` VALUES (2, 101);
INSERT INTO `sys_role_dept` VALUES (2, 105);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 1);
INSERT INTO `sys_role_menu` VALUES (1, 100);
INSERT INTO `sys_role_menu` VALUES (1, 101);
INSERT INTO `sys_role_menu` VALUES (1, 102);
INSERT INTO `sys_role_menu` VALUES (1, 103);
INSERT INTO `sys_role_menu` VALUES (1, 104);
INSERT INTO `sys_role_menu` VALUES (1, 105);
INSERT INTO `sys_role_menu` VALUES (1, 106);
INSERT INTO `sys_role_menu` VALUES (1, 107);
INSERT INTO `sys_role_menu` VALUES (1, 108);
INSERT INTO `sys_role_menu` VALUES (1, 500);
INSERT INTO `sys_role_menu` VALUES (1, 501);
INSERT INTO `sys_role_menu` VALUES (1, 1000);
INSERT INTO `sys_role_menu` VALUES (1, 1001);
INSERT INTO `sys_role_menu` VALUES (1, 1002);
INSERT INTO `sys_role_menu` VALUES (1, 1003);
INSERT INTO `sys_role_menu` VALUES (1, 1004);
INSERT INTO `sys_role_menu` VALUES (1, 1005);
INSERT INTO `sys_role_menu` VALUES (1, 1006);
INSERT INTO `sys_role_menu` VALUES (1, 1007);
INSERT INTO `sys_role_menu` VALUES (1, 1008);
INSERT INTO `sys_role_menu` VALUES (1, 1009);
INSERT INTO `sys_role_menu` VALUES (1, 1010);
INSERT INTO `sys_role_menu` VALUES (1, 1011);
INSERT INTO `sys_role_menu` VALUES (1, 1012);
INSERT INTO `sys_role_menu` VALUES (1, 1013);
INSERT INTO `sys_role_menu` VALUES (1, 1014);
INSERT INTO `sys_role_menu` VALUES (1, 1015);
INSERT INTO `sys_role_menu` VALUES (1, 1016);
INSERT INTO `sys_role_menu` VALUES (1, 1017);
INSERT INTO `sys_role_menu` VALUES (1, 1018);
INSERT INTO `sys_role_menu` VALUES (1, 1019);
INSERT INTO `sys_role_menu` VALUES (1, 1020);
INSERT INTO `sys_role_menu` VALUES (1, 1021);
INSERT INTO `sys_role_menu` VALUES (1, 1022);
INSERT INTO `sys_role_menu` VALUES (1, 1023);
INSERT INTO `sys_role_menu` VALUES (1, 1024);
INSERT INTO `sys_role_menu` VALUES (1, 1025);
INSERT INTO `sys_role_menu` VALUES (1, 1026);
INSERT INTO `sys_role_menu` VALUES (1, 1027);
INSERT INTO `sys_role_menu` VALUES (1, 1028);
INSERT INTO `sys_role_menu` VALUES (1, 1029);
INSERT INTO `sys_role_menu` VALUES (1, 1030);
INSERT INTO `sys_role_menu` VALUES (1, 1031);
INSERT INTO `sys_role_menu` VALUES (1, 1032);
INSERT INTO `sys_role_menu` VALUES (1, 1033);
INSERT INTO `sys_role_menu` VALUES (1, 1034);
INSERT INTO `sys_role_menu` VALUES (1, 1035);
INSERT INTO `sys_role_menu` VALUES (1, 1036);
INSERT INTO `sys_role_menu` VALUES (1, 1037);
INSERT INTO `sys_role_menu` VALUES (1, 1038);
INSERT INTO `sys_role_menu` VALUES (1, 1039);
INSERT INTO `sys_role_menu` VALUES (1, 1040);
INSERT INTO `sys_role_menu` VALUES (1, 1041);
INSERT INTO `sys_role_menu` VALUES (1, 1042);
INSERT INTO `sys_role_menu` VALUES (1, 1043);
INSERT INTO `sys_role_menu` VALUES (1, 1044);
INSERT INTO `sys_role_menu` VALUES (1, 1045);
INSERT INTO `sys_role_menu` VALUES (1, 2000);
INSERT INTO `sys_role_menu` VALUES (1, 2001);
INSERT INTO `sys_role_menu` VALUES (1, 2002);
INSERT INTO `sys_role_menu` VALUES (1, 2003);
INSERT INTO `sys_role_menu` VALUES (1, 2004);
INSERT INTO `sys_role_menu` VALUES (1, 2005);
INSERT INTO `sys_role_menu` VALUES (1, 2006);
INSERT INTO `sys_role_menu` VALUES (1, 2007);
INSERT INTO `sys_role_menu` VALUES (1, 2008);
INSERT INTO `sys_role_menu` VALUES (1, 2009);
INSERT INTO `sys_role_menu` VALUES (1, 2010);
INSERT INTO `sys_role_menu` VALUES (1, 2011);
INSERT INTO `sys_role_menu` VALUES (1, 2012);
INSERT INTO `sys_role_menu` VALUES (1, 2013);
INSERT INTO `sys_role_menu` VALUES (1, 2014);
INSERT INTO `sys_role_menu` VALUES (1, 2015);
INSERT INTO `sys_role_menu` VALUES (1, 2016);
INSERT INTO `sys_role_menu` VALUES (1, 2017);
INSERT INTO `sys_role_menu` VALUES (1, 2018);
INSERT INTO `sys_role_menu` VALUES (1, 2019);
INSERT INTO `sys_role_menu` VALUES (1, 2020);
INSERT INTO `sys_role_menu` VALUES (1, 2021);
INSERT INTO `sys_role_menu` VALUES (1, 2022);
INSERT INTO `sys_role_menu` VALUES (1, 2023);
INSERT INTO `sys_role_menu` VALUES (1, 2024);
INSERT INTO `sys_role_menu` VALUES (1, 2025);
INSERT INTO `sys_role_menu` VALUES (1, 2026);
INSERT INTO `sys_role_menu` VALUES (1, 2030);
INSERT INTO `sys_role_menu` VALUES (1, 2031);
INSERT INTO `sys_role_menu` VALUES (1, 2032);
INSERT INTO `sys_role_menu` VALUES (1, 2033);
INSERT INTO `sys_role_menu` VALUES (1, 2034);
INSERT INTO `sys_role_menu` VALUES (1, 2035);
INSERT INTO `sys_role_menu` VALUES (1, 2036);
INSERT INTO `sys_role_menu` VALUES (1, 2037);
INSERT INTO `sys_role_menu` VALUES (1, 2038);
INSERT INTO `sys_role_menu` VALUES (1, 2039);
INSERT INTO `sys_role_menu` VALUES (1, 2040);
INSERT INTO `sys_role_menu` VALUES (1, 2041);
INSERT INTO `sys_role_menu` VALUES (1, 2042);
INSERT INTO `sys_role_menu` VALUES (1, 2043);
INSERT INTO `sys_role_menu` VALUES (1, 2044);
INSERT INTO `sys_role_menu` VALUES (1, 2045);
INSERT INTO `sys_role_menu` VALUES (1, 2046);
INSERT INTO `sys_role_menu` VALUES (1, 2047);
INSERT INTO `sys_role_menu` VALUES (1, 2048);
INSERT INTO `sys_role_menu` VALUES (1, 2098);
INSERT INTO `sys_role_menu` VALUES (1, 2099);
INSERT INTO `sys_role_menu` VALUES (1, 2100);
INSERT INTO `sys_role_menu` VALUES (1, 2101);
INSERT INTO `sys_role_menu` VALUES (1, 2102);
INSERT INTO `sys_role_menu` VALUES (1, 2103);
INSERT INTO `sys_role_menu` VALUES (1, 2104);
INSERT INTO `sys_role_menu` VALUES (1, 2105);
INSERT INTO `sys_role_menu` VALUES (1, 2106);
INSERT INTO `sys_role_menu` VALUES (1, 2107);
INSERT INTO `sys_role_menu` VALUES (1, 2108);
INSERT INTO `sys_role_menu` VALUES (1, 2109);
INSERT INTO `sys_role_menu` VALUES (1, 2110);
INSERT INTO `sys_role_menu` VALUES (1, 2111);
INSERT INTO `sys_role_menu` VALUES (1, 2112);
INSERT INTO `sys_role_menu` VALUES (1, 2113);
INSERT INTO `sys_role_menu` VALUES (1, 2114);
INSERT INTO `sys_role_menu` VALUES (1, 2115);
INSERT INTO `sys_role_menu` VALUES (1, 2116);
INSERT INTO `sys_role_menu` VALUES (1, 2117);
INSERT INTO `sys_role_menu` VALUES (1, 2144);
INSERT INTO `sys_role_menu` VALUES (1, 2145);
INSERT INTO `sys_role_menu` VALUES (1, 2146);
INSERT INTO `sys_role_menu` VALUES (1, 2147);
INSERT INTO `sys_role_menu` VALUES (1, 2148);
INSERT INTO `sys_role_menu` VALUES (1, 2149);
INSERT INTO `sys_role_menu` VALUES (1, 2150);
INSERT INTO `sys_role_menu` VALUES (1, 2151);
INSERT INTO `sys_role_menu` VALUES (1, 2152);
INSERT INTO `sys_role_menu` VALUES (1, 2153);
INSERT INTO `sys_role_menu` VALUES (1, 2154);
INSERT INTO `sys_role_menu` VALUES (1, 2155);
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 101);
INSERT INTO `sys_role_menu` VALUES (2, 102);
INSERT INTO `sys_role_menu` VALUES (2, 103);
INSERT INTO `sys_role_menu` VALUES (2, 104);
INSERT INTO `sys_role_menu` VALUES (2, 105);
INSERT INTO `sys_role_menu` VALUES (2, 106);
INSERT INTO `sys_role_menu` VALUES (2, 107);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 114);
INSERT INTO `sys_role_menu` VALUES (2, 115);
INSERT INTO `sys_role_menu` VALUES (2, 116);
INSERT INTO `sys_role_menu` VALUES (2, 500);
INSERT INTO `sys_role_menu` VALUES (2, 501);
INSERT INTO `sys_role_menu` VALUES (2, 1000);
INSERT INTO `sys_role_menu` VALUES (2, 1001);
INSERT INTO `sys_role_menu` VALUES (2, 1002);
INSERT INTO `sys_role_menu` VALUES (2, 1003);
INSERT INTO `sys_role_menu` VALUES (2, 1004);
INSERT INTO `sys_role_menu` VALUES (2, 1005);
INSERT INTO `sys_role_menu` VALUES (2, 1006);
INSERT INTO `sys_role_menu` VALUES (2, 1007);
INSERT INTO `sys_role_menu` VALUES (2, 1008);
INSERT INTO `sys_role_menu` VALUES (2, 1009);
INSERT INTO `sys_role_menu` VALUES (2, 1010);
INSERT INTO `sys_role_menu` VALUES (2, 1011);
INSERT INTO `sys_role_menu` VALUES (2, 1012);
INSERT INTO `sys_role_menu` VALUES (2, 1013);
INSERT INTO `sys_role_menu` VALUES (2, 1014);
INSERT INTO `sys_role_menu` VALUES (2, 1015);
INSERT INTO `sys_role_menu` VALUES (2, 1016);
INSERT INTO `sys_role_menu` VALUES (2, 1017);
INSERT INTO `sys_role_menu` VALUES (2, 1018);
INSERT INTO `sys_role_menu` VALUES (2, 1019);
INSERT INTO `sys_role_menu` VALUES (2, 1020);
INSERT INTO `sys_role_menu` VALUES (2, 1021);
INSERT INTO `sys_role_menu` VALUES (2, 1022);
INSERT INTO `sys_role_menu` VALUES (2, 1023);
INSERT INTO `sys_role_menu` VALUES (2, 1024);
INSERT INTO `sys_role_menu` VALUES (2, 1025);
INSERT INTO `sys_role_menu` VALUES (2, 1026);
INSERT INTO `sys_role_menu` VALUES (2, 1027);
INSERT INTO `sys_role_menu` VALUES (2, 1028);
INSERT INTO `sys_role_menu` VALUES (2, 1029);
INSERT INTO `sys_role_menu` VALUES (2, 1030);
INSERT INTO `sys_role_menu` VALUES (2, 1031);
INSERT INTO `sys_role_menu` VALUES (2, 1032);
INSERT INTO `sys_role_menu` VALUES (2, 1033);
INSERT INTO `sys_role_menu` VALUES (2, 1034);
INSERT INTO `sys_role_menu` VALUES (2, 1035);
INSERT INTO `sys_role_menu` VALUES (2, 1036);
INSERT INTO `sys_role_menu` VALUES (2, 1037);
INSERT INTO `sys_role_menu` VALUES (2, 1038);
INSERT INTO `sys_role_menu` VALUES (2, 1039);
INSERT INTO `sys_role_menu` VALUES (2, 1040);
INSERT INTO `sys_role_menu` VALUES (2, 1041);
INSERT INTO `sys_role_menu` VALUES (2, 1042);
INSERT INTO `sys_role_menu` VALUES (2, 1043);
INSERT INTO `sys_role_menu` VALUES (2, 1044);
INSERT INTO `sys_role_menu` VALUES (2, 1045);
INSERT INTO `sys_role_menu` VALUES (2, 1055);
INSERT INTO `sys_role_menu` VALUES (2, 1056);
INSERT INTO `sys_role_menu` VALUES (2, 1057);
INSERT INTO `sys_role_menu` VALUES (2, 1058);
INSERT INTO `sys_role_menu` VALUES (2, 1059);
INSERT INTO `sys_role_menu` VALUES (2, 1060);
INSERT INTO `sys_role_menu` VALUES (2, 2098);
INSERT INTO `sys_role_menu` VALUES (2, 2099);
INSERT INTO `sys_role_menu` VALUES (2, 2100);
INSERT INTO `sys_role_menu` VALUES (2, 2101);
INSERT INTO `sys_role_menu` VALUES (2, 2102);
INSERT INTO `sys_role_menu` VALUES (2, 2103);
INSERT INTO `sys_role_menu` VALUES (2, 2104);
INSERT INTO `sys_role_menu` VALUES (2, 2105);
INSERT INTO `sys_role_menu` VALUES (2, 2106);
INSERT INTO `sys_role_menu` VALUES (2, 2107);
INSERT INTO `sys_role_menu` VALUES (2, 2108);
INSERT INTO `sys_role_menu` VALUES (2, 2109);
INSERT INTO `sys_role_menu` VALUES (2, 2110);
INSERT INTO `sys_role_menu` VALUES (2, 2111);
INSERT INTO `sys_role_menu` VALUES (2, 2144);
INSERT INTO `sys_role_menu` VALUES (2, 2145);
INSERT INTO `sys_role_menu` VALUES (2, 2146);
INSERT INTO `sys_role_menu` VALUES (2, 2147);
INSERT INTO `sys_role_menu` VALUES (2, 2148);
INSERT INTO `sys_role_menu` VALUES (2, 2149);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `delete_flag` tinyint NULL DEFAULT 0,
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime NULL DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 100, 'admin', '超级管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', 0, '127.0.0.1', '2025-07-16 10:17:31', '2025-06-04 11:01:40', 1, '2025-06-04 11:01:40', 1, '2025-07-16 10:17:30', '管理员');
INSERT INTO `sys_user` VALUES (2, 100, 'ry', '管理员', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', 0, '127.0.0.1', '2025-06-04 11:01:40', '2025-06-04 11:01:40', 1, '2025-06-04 11:01:40', 1, '2025-06-04 14:24:14', '测试员');

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (2, 1);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 2);

-- ----------------------------
-- Table structure for wx_user
-- ----------------------------
DROP TABLE IF EXISTS `wx_user`;
CREATE TABLE `wx_user`  (
  `id` bigint NOT NULL COMMENT '用户id',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `phone_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信用户唯一id',
  `img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户头像',
  `status` int NOT NULL DEFAULT 0 COMMENT '是否停用0 正常 1 停用/封禁',
  `user_type` int NOT NULL DEFAULT 0 COMMENT '用户类型（普通用户为0、集团客户为1、VIP客户为2）',
  `other_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '第三方id',
  `we_chat_balance` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '微信余额',
  `platform_balance` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '平台余额',
  `donate_balance` decimal(11, 2) NOT NULL DEFAULT 0.00 COMMENT '赠送余额',
  `delete_flag` tinyint NULL DEFAULT 0,
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`, `user_type`) USING BTREE,
  INDEX `idx_wx_user_delete_flag`(`delete_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of wx_user
-- ----------------------------
INSERT INTO `wx_user` VALUES (731341338896896000, '17718892842', '默认用户', '17718892842', NULL, 'o3y96608snBhXvOKCJOBMnw05u_M', NULL, 0, 0, NULL, 0.00, 0.00, 0.00, 0, NULL, '2025-07-11 10:49:52', NULL, '2025-07-11 10:49:52');

-- ----------------------------
-- Table structure for wx_user_car
-- ----------------------------
DROP TABLE IF EXISTS `wx_user_car`;
CREATE TABLE `wx_user_car`  (
  `id` bigint NOT NULL COMMENT '车辆id',
  `plate_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '车牌号',
  `car_brand` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `car_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车型',
  `is_default` tinyint NULL DEFAULT 0 COMMENT '是否是默认车辆',
  `user_id` bigint NOT NULL COMMENT '用户',
  `energy_type` int NULL DEFAULT NULL COMMENT '能源类型(1燃油、2纯电、3混动)',
  `delete_flag` tinyint NOT NULL DEFAULT 0,
  `create_by` bigint NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户车辆表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of wx_user_car
-- ----------------------------
INSERT INTO `wx_user_car` VALUES (731336911850967040, '沪S91111', '123', 'SUV', 1, 0, 1, 0, NULL, '2025-07-11 10:31:47', NULL, '2025-07-11 10:31:47');

SET FOREIGN_KEY_CHECKS = 1;
