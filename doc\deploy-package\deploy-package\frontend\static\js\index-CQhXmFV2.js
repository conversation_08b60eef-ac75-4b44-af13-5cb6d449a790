import{_ as N,r as b,e as l,c as v,o as p,i as t,f as e,n as o,h as s,L as k,M as z,j as E,t as c,l as g,a1 as F,a2 as j,a3 as D,A as M,B as U}from"./index-D_FV2sri.js";const i=u=>(M("data-v-35821bf2"),u=u(),U(),u),q={class:"app-container"},G={class:"merchant-member-container"},H={class:"page-header"},J={class:"page-title"},K=i(()=>t("p",{class:"page-description"},"这是商户会员页面 - 管理商户的会员用户信息",-1)),O={class:"member-stats"},Q={class:"stat-content"},R={class:"stat-icon"},W=i(()=>t("div",{class:"stat-info"},[t("div",{class:"stat-number"},"1,234"),t("div",{class:"stat-label"},"总会员数")],-1)),X={class:"stat-content"},Y={class:"stat-icon"},Z=i(()=>t("div",{class:"stat-info"},[t("div",{class:"stat-number"},"856"),t("div",{class:"stat-label"},"活跃会员")],-1)),$={class:"stat-content"},ee={class:"stat-icon"},te=i(()=>t("div",{class:"stat-info"},[t("div",{class:"stat-number"},"156"),t("div",{class:"stat-label"},"VIP会员")],-1)),se={class:"stat-content"},ae={class:"stat-icon"},le=i(()=>t("div",{class:"stat-info"},[t("div",{class:"stat-number"},"45"),t("div",{class:"stat-label"},"本月新增")],-1)),oe=i(()=>t("span",null,"会员等级分布",-1)),ne={class:"level-items"},ce={class:"level-item"},ie={class:"level-header"},de={class:"level-name"},re={class:"level-progress"},_e={class:"level-benefits"},pe={class:"card-header"},ue=i(()=>t("span",null,"会员列表",-1)),he={class:"header-actions"},ve={__name:"index",setup(u){const w=b(""),I=b([{name:"普通会员",count:678,percentage:55,color:"#909399",tagType:"info",benefits:["基础停车优惠","积分累积"]},{name:"VIP会员",count:400,percentage:32,color:"#E6A23C",tagType:"warning",benefits:["停车8折优惠","免费洗车","优先客服"]},{name:"至尊会员",count:156,percentage:13,color:"#F56C6C",tagType:"danger",benefits:["停车5折优惠","无限洗车","专属客服","道路救援"]}]),T=b([{name:"张三",phone:"13800138001",level:"VIP会员",points:1250,balance:156.8,lastLogin:"2024-01-20 14:30:00",status:"正常",avatar:""},{name:"李四",phone:"13800138002",level:"普通会员",points:680,balance:89.5,lastLogin:"2024-01-19 09:15:00",status:"正常",avatar:""},{name:"王五",phone:"13800138003",level:"至尊会员",points:2580,balance:320,lastLogin:"2024-01-20 16:45:00",status:"正常",avatar:""},{name:"赵六",phone:"13800138004",level:"普通会员",points:420,balance:45.2,lastLogin:"2024-01-18 11:20:00",status:"停用",avatar:""}]),B=y=>{switch(y){case"普通会员":return"info";case"VIP会员":return"warning";case"至尊会员":return"danger";default:return"info"}};return(y,x)=>{const r=l("svg-icon"),d=l("el-card"),_=l("el-col"),L=l("el-row"),m=l("el-tag"),C=l("el-progress"),f=l("el-icon"),P=l("el-input"),h=l("el-button"),S=l("el-avatar"),n=l("el-table-column"),A=l("el-table");return p(),v("div",q,[t("div",G,[t("div",H,[t("h1",J,[e(r,{"icon-class":"merchant-member",class:"title-icon"}),o(" 商户会员管理 ")]),K]),t("div",O,[e(L,{gutter:20},{default:s(()=>[e(_,{span:6},{default:s(()=>[e(d,{class:"stat-card total",shadow:"hover"},{default:s(()=>[t("div",Q,[t("div",R,[e(r,{"icon-class":"user"})]),W])]),_:1})]),_:1}),e(_,{span:6},{default:s(()=>[e(d,{class:"stat-card active",shadow:"hover"},{default:s(()=>[t("div",X,[t("div",Y,[e(r,{"icon-class":"star"})]),Z])]),_:1})]),_:1}),e(_,{span:6},{default:s(()=>[e(d,{class:"stat-card vip",shadow:"hover"},{default:s(()=>[t("div",$,[t("div",ee,[e(r,{"icon-class":"star"})]),te])]),_:1})]),_:1}),e(_,{span:6},{default:s(()=>[e(d,{class:"stat-card new",shadow:"hover"},{default:s(()=>[t("div",se,[t("div",ae,[e(r,{"icon-class":"user"})]),le])]),_:1})]),_:1})]),_:1})]),e(d,{class:"level-distribution-card",shadow:"never"},{header:s(()=>[oe]),default:s(()=>[t("div",ne,[e(L,{gutter:20},{default:s(()=>[(p(!0),v(k,null,z(I.value,a=>(p(),E(_,{span:8,key:a.name},{default:s(()=>[t("div",ce,[t("div",ie,[t("div",de,c(a.name),1),e(m,{type:a.tagType,size:"small"},{default:s(()=>[o(c(a.count)+"人",1)]),_:2},1032,["type"])]),t("div",re,[e(C,{percentage:a.percentage,color:a.color,"show-text":!1},null,8,["percentage","color"])]),t("div",_e,[(p(!0),v(k,null,z(a.benefits,V=>(p(),v("div",{class:"benefit",key:V},[e(f,null,{default:s(()=>[e(g(F))]),_:1}),o(" "+c(V),1)]))),128))])])]),_:2},1024))),128))]),_:1})])]),_:1}),e(d,{class:"member-list-card",shadow:"never"},{header:s(()=>[t("div",pe,[ue,t("div",he,[e(P,{modelValue:w.value,"onUpdate:modelValue":x[0]||(x[0]=a=>w.value=a),placeholder:"搜索会员",size:"small",style:{width:"200px","margin-right":"10px"}},{prefix:s(()=>[e(f,null,{default:s(()=>[e(g(j))]),_:1})]),_:1},8,["modelValue"]),e(h,{type:"primary",size:"small"},{default:s(()=>[e(f,null,{default:s(()=>[e(g(D))]),_:1}),o(" 导出 ")]),_:1})])])]),default:s(()=>[e(A,{data:T.value,style:{width:"100%"}},{default:s(()=>[e(n,{prop:"avatar",label:"头像",width:"80"},{default:s(a=>[e(S,{size:40,src:a.row.avatar},{default:s(()=>[o(c(a.row.name.charAt(0)),1)]),_:2},1032,["src"])]),_:1}),e(n,{prop:"name",label:"姓名",width:"120"}),e(n,{prop:"phone",label:"手机号",width:"130"}),e(n,{prop:"level",label:"会员等级",width:"100"},{default:s(a=>[e(m,{type:B(a.row.level),size:"small"},{default:s(()=>[o(c(a.row.level),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"points",label:"积分",width:"100"}),e(n,{prop:"balance",label:"余额",width:"100"},{default:s(a=>[o(" ¥"+c(a.row.balance),1)]),_:1}),e(n,{prop:"lastLogin",label:"最后登录",width:"180"}),e(n,{prop:"status",label:"状态",width:"100"},{default:s(a=>[e(m,{type:a.row.status==="正常"?"success":"danger",size:"small"},{default:s(()=>[o(c(a.row.status),1)]),_:2},1032,["type"])]),_:1}),e(n,{label:"操作",width:"200"},{default:s(a=>[e(h,{type:"primary",size:"small",link:""},{default:s(()=>[o("查看")]),_:1}),e(h,{type:"warning",size:"small",link:""},{default:s(()=>[o("编辑")]),_:1}),e(h,{type:"info",size:"small",link:""},{default:s(()=>[o("充值")]),_:1})]),_:1})]),_:1},8,["data"])]),_:1})])])}}},fe=N(ve,[["__scopeId","data-v-35821bf2"]]);export{fe as default};
