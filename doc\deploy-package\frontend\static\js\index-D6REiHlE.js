import{v as fe,_ as gl,O as hl,d as _l,r as f,C as vl,T as yl,y as Ue,e as h,Q as $e,c as v,o as n,R as W,f as l,S as wl,l as o,h as a,m as Se,L as S,M as T,j as i,k as O,n as p,P as F,i as I,a8 as Te,t as E,a9 as Q,A as bl,B as Vl}from"./index-Cox2ohSR.js";import{l as ce,a as kl,g as me,d as xe,u as Pe,b as Oe}from"./warehouse-D8FfdMFO.js";import{o as Il}from"./operator-DsAnS6bq.js";import{C as De}from"./index-Cy2yB1cz.js";function Cl(){return fe({url:"/system/area/provinces",method:"get"})}function Ee(j){return fe({url:"/system/area/cities/"+j,method:"get"})}function je(j){return fe({url:"/system/area/districts/"+j,method:"get"})}const Ae=j=>(bl("data-v-0bbb4e5e"),j=j(),Vl(),j),Nl={class:"app-container"},Wl={key:0,class:"table-carousel-container"},Ul={class:"carousel-wrapper"},$l={class:"image-badge"},Sl={class:"badge-text"},Tl={key:2,class:"custom-indicators"},xl={key:1,class:"no-image-container"},Pl=Ae(()=>I("span",{class:"no-image-text"},"暂无图片",-1)),Ol={class:"dialog-footer"},Dl={class:"child-warehouse-management"},El={class:"mb-4"},jl={class:"dialog-footer"},Al={class:"detail-container"},Rl={key:0},ql={key:1},Fl={key:2},Jl={key:0,class:"detail-carousel-section"},zl=Ae(()=>I("h4",null,"轮播图",-1)),Ll={class:"detail-carousel-container"},Bl={class:"dialog-footer"},Ql=hl({name:"Warehouse"}),Kl=Object.assign(Ql,{components:{CustomPagination:De}},{setup(j){const{proxy:m}=_l(),{warehouse_status:z}=m.useDict("warehouse_status"),ge=f([]),K=f([]),le=f([]),Re=f([]),he=f([]),L=f([]),J=f([]),A=f(!1),M=f(!0),G=f(!0),ae=f([]),_e=f(!0),ve=f(!0),ye=f(0),te=f(""),H=f(!1),R=f(!1),X=f(!1),oe=f([]),re=f(""),q=f({}),B=f(!1),_=f({}),we=f(!1),qe=vl({form:{},queryParams:{pageNum:1,pageSize:10,projectName:null,warehouseName:null,operatorId:null,status:null},rules:{warehouseName:[{required:!0,message:"场库名称不能为空",trigger:"blur"}],operatorId:[{required:!0,message:"所属运营商不能为空",trigger:"change"}],warehouseType:[{required:!0,message:"类型不能为空",trigger:"change"}],parentId:[{validator:(r,e,s)=>{u.value.warehouseType==="child"&&(!e||e===0)?s(new Error("选择子场库时，所属场库不能为空")):s()},trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],totalParking:[{required:!0,message:"总停车位数不能为空",trigger:"blur"},{type:"number",min:1,message:"总停车位数必须大于0",trigger:"blur"}],longitude:[{pattern:/^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$|^-?180(\.0+)?$/,message:"经度范围应在-180到180之间",trigger:"blur"}],latitude:[{pattern:/^-?([1-8]?\d(\.\d+)?|90(\.0+)?)$/,message:"纬度范围应在-90到90之间",trigger:"blur"}]},childWarehouseForm:{},childWarehouseRules:{warehouseName:[{required:!0,message:"子场库名称不能为空",trigger:"blur"}],operatorId:[{required:!0,message:"所属运营商不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:w,form:u,rules:Fe,childWarehouseForm:b,childWarehouseRules:Je}=yl(qe),se=Ue(()=>u.value.warehouseType==="main"?"主场库":"子场库"),ue=Ue({get(){if(!u.value.carouselImages)return"";try{if(typeof u.value.carouselImages=="string"&&u.value.carouselImages.startsWith("[")){let r=JSON.parse(u.value.carouselImages);if(Array.isArray(r)&&r.length>0&&typeof r[0]=="string"&&r[0].startsWith("[")&&r[0].endsWith("]"))try{r=JSON.parse(r[0])}catch(s){console.warn("解析内层JSON失败，使用原始数据:",s)}return Array.isArray(r)||(r=[r]),r.filter(s=>s&&typeof s=="string"&&s.trim()&&s!=="undefined"&&s!=="null"&&!s.includes("[")&&!s.includes("]")).join(",")}return u.value.carouselImages||""}catch(r){return console.error("轮播图数据格式转换失败:",r),""}},set(r){u.value.carouselImages=r||null}});function C(r){if(!r)return[];try{let e=JSON.parse(r);if(Array.isArray(e)&&e.length>0&&typeof e[0]=="string"&&e[0].startsWith("[")&&e[0].endsWith("]"))try{e=JSON.parse(e[0])}catch(d){console.warn("解析内层JSON失败，使用原始数据:",d)}Array.isArray(e)||(e=[e]);const s="/prod-api";return e.map(d=>{if(!d||typeof d!="string")return console.warn("无效的图片URL:",d),"";if(d.startsWith("http://")||d.startsWith("https://"))return d;const c=d.replace(/["%\[\]]/g,"").trim();return c.startsWith("/statics/")?s+c:c&&!c.startsWith("/")?s+"/statics/"+c:s+c}).filter(d=>d&&d!==s)}catch(e){return console.error("解析轮播图数据失败:",e),[]}}function Y(){w.value.pageNum=1,x()}function ze(){m.resetForm("queryRef"),Y()}function x(){M.value=!0;const r={...w.value,parentId:0};ce(r).then(e=>{ge.value=e.rows||[],ye.value=e.total||0,M.value=!1}).catch(e=>{console.error("获取场库列表失败:",e),M.value=!1,Q(e)||m.$modal.msgError("获取数据失败，请刷新页面重试")})}function Le(){kl({}).then(r=>{Re.value=r.data.filter(e=>e.parentId===0)})}function Be(){Il().then(r=>{K.value=r.data})}function Qe(){Cl().then(r=>{he.value=r.data}).catch(r=>{console.error("获取省份列表失败:",r)})}function Ke(r){u.value.cityCode=null,u.value.areaCode=null,L.value=[],J.value=[],r&&Ee(r).then(e=>{L.value=e.data}).catch(e=>{console.error("获取城市列表失败:",e)})}function Me(r){u.value.areaCode=null,J.value=[],r&&je(r).then(e=>{J.value=e.data}).catch(e=>{console.error("获取区县列表失败:",e)})}function be(){ce({parentId:0,status:1}).then(r=>{le.value=r.rows||[]}).catch(r=>{console.error("获取父级场库列表失败:",r)})}function Ge(){A.value=!1,ne()}function ne(){u.value={id:null,projectName:null,warehouseName:null,operatorId:null,warehouseType:"main",parentId:0,smartsType:0,totalParking:0,provinceCode:null,cityCode:null,areaCode:null,address:null,longitude:null,latitude:null,carouselImages:null,status:1,remark:null},L.value=[],J.value=[],le.value=[],we.value=!1,m.resetForm("warehouseRef")}function He(r){ae.value=r.map(e=>e.id),_e.value=r.length!=1,ve.value=!r.length}function Xe(){ne(),A.value=!0,te.value="添加场库信息"}function Ve(r){ne();const e=r.id||ae.value;me(e).then(s=>{if(!s.data){console.error("获取场库详情失败: 返回数据为空"),m.$modal.msgError("获取场库信息失败，数据为空");return}u.value=s.data,u.value.warehouseType=u.value.parentId==0||u.value.parentId===null||u.value.parentId===void 0?"main":"child",u.value.parentId===0&&(we.value=!1),u.value.warehouseType==="child"&&be(),u.value&&u.value.provinceCode&&Ee(u.value.provinceCode).then(d=>{L.value=d.data,u.value.cityCode&&je(u.value.cityCode).then(c=>{J.value=c.data})}),A.value=!0,te.value="修改场库信息"}).catch(s=>{console.error("获取场库详情失败:",s),Q(s)||m.$modal.msgError("获取场库信息失败，请稍后重试")})}function Ye(){m.$refs.warehouseRef.validate(r=>{if(r){const e={...u.value};if(e.warehouseType==="main")e.parentId=0;else if(e.warehouseType==="child"&&(!e.parentId||e.parentId===0)){m.$modal.msgError("选择子场库时，必须选择所属场库");return}delete e.warehouseType;try{const s=m.$store.state.user;s&&s.id&&(u.value.id||(e.createBy=s.id),e.updateBy=s.id)}catch(s){console.warn("获取用户ID失败:",s)}if(e.carouselImages)try{let s=!1;try{const d=JSON.parse(e.carouselImages);Array.isArray(d)&&(s=!0)}catch{}if(!s)if(typeof e.carouselImages=="string"&&e.carouselImages.includes(",")){const d=e.carouselImages.split(",").map(c=>c.trim()).filter(c=>c&&c!=="undefined"&&c!=="null");e.carouselImages=d.length>0?JSON.stringify(d):null}else if(typeof e.carouselImages=="string"&&e.carouselImages.trim()){const d=e.carouselImages.trim();d!=="undefined"&&d!=="null"?e.carouselImages=JSON.stringify([d]):e.carouselImages=null}else(!e.carouselImages||e.carouselImages.trim()==="")&&(e.carouselImages=null)}catch(s){console.error("轮播图数据格式转换失败:",s),e.carouselImages=null}else e.carouselImages=null;u.value.id!=null?Pe(e).then(s=>{m.$modal.msgSuccess("修改成功"),A.value=!1,x()}).catch(s=>{console.error("修改场库失败:",s),Q(s)||m.$modal.msgError("修改失败，请稍后重试")}):Oe(e).then(s=>{m.$modal.msgSuccess("新增成功"),A.value=!1,x()}).catch(s=>{console.error("新增场库失败:",s),Q(s)||m.$modal.msgError("新增失败，请稍后重试")})}})}function Ze(r){const e=r.id;me(e).then(s=>{if(!s.data){m.$modal.msgError("获取场库信息失败，数据为空");return}_.value=s.data,B.value=!0}).catch(s=>{console.error("获取场库详情失败:",s),Q(s)||m.$modal.msgError("获取场库信息失败，请稍后重试")})}function el(r){if(!r)return"";const e=r.provinceName||"",s=r.cityName||"",d=r.areaName||"",c=r.address||"";return!e&&!s&&!d?c||"暂无地址信息":ll(e)?`${e}${d}${c}`:`${e}${s}${d}${c}`}function ll(r){return r&&(r.includes("上海")||r.includes("北京")||r.includes("天津")||r.includes("重庆"))}function ke(r){const e=r.id||ae.value;m.$modal.confirm('是否确认删除场库信息编号为"'+e+'"的数据项？').then(function(){return xe(e)}).then(()=>{x(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}function al(){m.download("system/platform/warehouse/export",{...w.value},`warehouse_${new Date().getTime()}.xlsx`)}function tl(r){q.value=r,H.value=!0,Z(r.id)}function Z(r){X.value=!0,ce({parentId:r,pageNum:1,pageSize:1e3}).then(s=>{oe.value=s.rows||[],X.value=!1}).catch(()=>{oe.value=[],X.value=!1})}function de(){b.value={id:null,warehouseName:null,parentId:q.value.id,operatorId:q.value.operatorId,totalParking:0,status:1,remark:null},m.resetForm("childWarehouseRef")}function ol(){de(),R.value=!0,re.value="新增子场库"}function rl(r){de();const e=r.id;me(e).then(s=>{b.value=s.data,R.value=!0,re.value="修改子场库"})}function sl(){m.$refs.childWarehouseRef.validate(r=>{r&&(b.value.id!=null?Pe(b.value).then(e=>{m.$modal.msgSuccess("修改成功"),R.value=!1,Z(q.value.id),x()}):Oe(b.value).then(e=>{m.$modal.msgSuccess("新增成功"),R.value=!1,Z(q.value.id),x()}))})}function ul(){R.value=!1,de()}function nl(r){const e=r.id;m.$modal.confirm(`是否确认删除子场库"${r.warehouseName}"？`).then(function(){return xe(e)}).then(()=>{Z(q.value.id),x(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}return x(),Be(),Qe(),be(),Le(),(r,e)=>{const s=h("el-input"),d=h("el-form-item"),c=h("el-option"),U=h("el-select"),y=h("el-button"),ie=h("el-form"),g=h("el-col"),dl=h("right-toolbar"),k=h("el-row"),V=h("el-table-column"),$=h("el-tag"),pe=h("el-image"),il=h("el-carousel-item"),pl=h("el-carousel"),Ie=h("el-icon"),Ce=h("el-table"),Ne=h("el-input-number"),cl=h("image-upload"),ee=h("el-dialog"),P=h("el-descriptions-item"),ml=h("el-descriptions"),D=$e("hasPermi"),We=$e("loading");return n(),v("div",Nl,[W(l(ie,{model:o(w),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[l(d,{label:"项目名称",prop:"projectName"},{default:a(()=>[l(s,{modelValue:o(w).projectName,"onUpdate:modelValue":e[0]||(e[0]=t=>o(w).projectName=t),placeholder:"请输入项目名称",clearable:"",style:{width:"200px"},onKeyup:Se(Y,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"场库名称",prop:"warehouseName"},{default:a(()=>[l(s,{modelValue:o(w).warehouseName,"onUpdate:modelValue":e[1]||(e[1]=t=>o(w).warehouseName=t),placeholder:"请输入场库名称",clearable:"",style:{width:"200px"},onKeyup:Se(Y,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"所属运营商",prop:"operatorId"},{default:a(()=>[l(U,{modelValue:o(w).operatorId,"onUpdate:modelValue":e[2]||(e[2]=t=>o(w).operatorId=t),placeholder:"请选择运营商",clearable:"",style:{width:"200px"}},{default:a(()=>[(n(!0),v(S,null,T(o(K),t=>(n(),i(c,{key:t.id,label:t.companyName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"状态",prop:"status"},{default:a(()=>[l(U,{modelValue:o(w).status,"onUpdate:modelValue":e[3]||(e[3]=t=>o(w).status=t),placeholder:"请选择状态",clearable:"",style:{width:"200px"}},{default:a(()=>[o(z)&&o(z).length>0?(n(!0),v(S,{key:0},T(o(z),t=>(n(),i(c,{key:t.value,label:t.label,value:parseInt(t.value)},null,8,["label","value"]))),128)):O("",!0),!o(z)||o(z).length===0?(n(),i(c,{key:1,disabled:"",value:""},{default:a(()=>[p("字典数据加载中...")]),_:1})):O("",!0)]),_:1},8,["modelValue"])]),_:1}),l(d,null,{default:a(()=>[l(y,{type:"primary",icon:"Search",onClick:Y},{default:a(()=>[p("搜索")]),_:1}),l(y,{icon:"Refresh",onClick:ze},{default:a(()=>[p("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[wl,o(G)]]),l(k,{gutter:10,class:"mb8"},{default:a(()=>[l(g,{span:1.5},{default:a(()=>[W((n(),i(y,{type:"primary",plain:"",icon:"Plus",onClick:Xe},{default:a(()=>[p("新增")]),_:1})),[[D,["platform:warehouse:add"]]])]),_:1}),l(g,{span:1.5},{default:a(()=>[W((n(),i(y,{type:"success",plain:"",icon:"Edit",disabled:o(_e),onClick:Ve},{default:a(()=>[p("修改")]),_:1},8,["disabled"])),[[D,["platform:warehouse:edit"]]])]),_:1}),l(g,{span:1.5},{default:a(()=>[W((n(),i(y,{type:"danger",plain:"",icon:"Delete",disabled:o(ve),onClick:ke},{default:a(()=>[p("删除")]),_:1},8,["disabled"])),[[D,["platform:warehouse:remove"]]])]),_:1}),l(g,{span:1.5},{default:a(()=>[W((n(),i(y,{type:"warning",plain:"",icon:"Download",onClick:al},{default:a(()=>[p("导出")]),_:1})),[[D,["platform:warehouse:export"]]])]),_:1}),l(dl,{showSearch:o(G),"onUpdate:showSearch":e[4]||(e[4]=t=>F(G)?G.value=t:null),onQueryTable:x},null,8,["showSearch"])]),_:1}),W((n(),i(Ce,{data:o(ge),onSelectionChange:He},{default:a(()=>[l(V,{type:"selection",width:"55",align:"center"}),l(V,{label:"项目名称",align:"center",prop:"projectName","show-overflow-tooltip":!0}),l(V,{label:"场库名称",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),l(V,{label:"所属运营商",align:"center",prop:"operatorName","show-overflow-tooltip":!0}),l(V,{label:"类型",align:"center",width:"100"},{default:a(t=>[t.row.parentId==0||t.row.parentId===null||t.row.parentId===void 0?(n(),i($,{key:0,type:"primary"},{default:a(()=>[p("主场库")]),_:1})):(n(),i($,{key:1,type:"success"},{default:a(()=>[p("子场库")]),_:1}))]),_:1}),l(V,{label:"总车位",align:"center",prop:"totalParking"}),l(V,{label:"轮播图",align:"center",width:"220"},{default:a(t=>[C(t.row.carouselImages).length>0?(n(),v("div",Wl,[I("div",Ul,[C(t.row.carouselImages).length>1?(n(),i(pl,{key:0,height:"90px",interval:4e3,"indicator-position":"none",arrow:"hover",class:"table-carousel"},{default:a(()=>[(n(!0),v(S,null,T(C(t.row.carouselImages),(N,fl)=>(n(),i(il,{key:fl},{default:a(()=>[l(pe,{src:N,"preview-src-list":C(t.row.carouselImages),fit:"cover",class:"carousel-image-item","preview-teleported":!0},null,8,["src","preview-src-list"])]),_:2},1024))),128))]),_:2},1024)):(n(),i(pe,{key:1,src:C(t.row.carouselImages)[0],"preview-src-list":C(t.row.carouselImages),fit:"cover",class:"single-image","preview-teleported":!0},null,8,["src","preview-src-list"])),I("div",$l,[l(Ie,{class:"badge-icon"},{default:a(()=>[l(o(Te))]),_:1}),I("span",Sl,E(C(t.row.carouselImages).length),1)]),C(t.row.carouselImages).length>1?(n(),v("div",Tl,[(n(!0),v(S,null,T(C(t.row.carouselImages).length,N=>(n(),v("span",{key:N,class:"indicator-dot"}))),128))])):O("",!0)])])):(n(),v("div",xl,[l(Ie,{class:"no-image-icon"},{default:a(()=>[l(o(Te))]),_:1}),Pl]))]),_:1}),l(V,{label:"状态",align:"center",prop:"status",width:"120"},{default:a(t=>[t.row.status===1?(n(),i($,{key:0,type:"success"},{default:a(()=>[p("营业中")]),_:1})):t.row.status===2?(n(),i($,{key:1,type:"warning"},{default:a(()=>[p("未营业")]),_:1})):O("",!0)]),_:1}),l(V,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(t=>[I("span",null,E(r.parseTime(t.row.createTime)),1)]),_:1}),l(V,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"200"},{default:a(t=>[l(y,{link:"",type:"info",icon:"View",onClick:N=>Ze(t.row)},{default:a(()=>[p("详情")]),_:2},1032,["onClick"]),l(y,{link:"",type:"primary",icon:"View",onClick:N=>tl(t.row)},{default:a(()=>[p("子场库")]),_:2},1032,["onClick"]),W((n(),i(y,{link:"",type:"primary",icon:"Edit",onClick:N=>Ve(t.row)},{default:a(()=>[p("修改")]),_:2},1032,["onClick"])),[[D,["platform:warehouse:edit"]]]),W((n(),i(y,{link:"",type:"danger",icon:"Delete",onClick:N=>ke(t.row)},{default:a(()=>[p("删除")]),_:2},1032,["onClick"])),[[D,["platform:warehouse:remove"]]])]),_:1})]),_:1},8,["data"])),[[We,o(M)]]),l(De,{total:o(ye),"current-page":o(w).pageNum,"onUpdate:currentPage":e[5]||(e[5]=t=>o(w).pageNum=t),"page-size":o(w).pageSize,"onUpdate:pageSize":e[6]||(e[6]=t=>o(w).pageSize=t),onPagination:x},null,8,["total","current-page","page-size"]),l(ee,{title:o(te),modelValue:o(A),"onUpdate:modelValue":e[23]||(e[23]=t=>F(A)?A.value=t:null),width:"1000px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[I("div",Ol,[l(y,{type:"primary",onClick:Ye},{default:a(()=>[p("确 定")]),_:1}),l(y,{onClick:Ge},{default:a(()=>[p("取 消")]),_:1})])]),default:a(()=>[l(ie,{ref:"warehouseRef",model:o(u),rules:o(Fe),"label-width":"100px"},{default:a(()=>[l(k,null,{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"场库名称",prop:"warehouseName"},{default:a(()=>[l(s,{modelValue:o(u).warehouseName,"onUpdate:modelValue":e[7]||(e[7]=t=>o(u).warehouseName=t),placeholder:"请输入场库名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(g,{span:12},{default:a(()=>[l(d,{label:"类型",prop:"warehouseType"},{default:a(()=>[l(s,{modelValue:o(se),"onUpdate:modelValue":e[8]||(e[8]=t=>F(se)?se.value=t:null),readonly:"",placeholder:"类型"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k,null,{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"所属运营商",prop:"operatorId"},{default:a(()=>[l(U,{modelValue:o(u).operatorId,"onUpdate:modelValue":e[9]||(e[9]=t=>o(u).operatorId=t),placeholder:"请选择运营商",style:{width:"100%"},"popper-class":"operator-select-dropdown"},{default:a(()=>[(n(!0),v(S,null,T(o(K),t=>(n(),i(c,{key:t.id,label:t.companyName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(g,{span:12},{default:a(()=>[l(d,{label:"状态",prop:"status"},{default:a(()=>[l(U,{modelValue:o(u).status,"onUpdate:modelValue":e[10]||(e[10]=t=>o(u).status=t),placeholder:"请选择状态"},{default:a(()=>[l(c,{label:"营业中",value:1}),l(c,{label:"未营业",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(u).warehouseType==="child"?(n(),i(k,{key:0},{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"所属场库",prop:"parentId"},{default:a(()=>[l(U,{modelValue:o(u).parentId,"onUpdate:modelValue":e[11]||(e[11]=t=>o(u).parentId=t),placeholder:"请选择所属场库",style:{width:"100%"},clearable:""},{default:a(()=>[(n(!0),v(S,null,T(o(le),t=>(n(),i(c,{key:t.id,label:t.warehouseName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):O("",!0),l(k,null,{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"项目名称",prop:"projectName"},{default:a(()=>[l(s,{modelValue:o(u).projectName,"onUpdate:modelValue":e[12]||(e[12]=t=>o(u).projectName=t),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(g,{span:12},{default:a(()=>[l(d,{label:"智能类型",prop:"smartsType"},{default:a(()=>[l(U,{modelValue:o(u).smartsType,"onUpdate:modelValue":e[13]||(e[13]=t=>o(u).smartsType=t),placeholder:"请选择智能类型"},{default:a(()=>[l(c,{label:"普通停车场",value:0}),l(c,{label:"智能停车场",value:1}),l(c,{label:"无人值守",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k,null,{default:a(()=>[l(g,{span:8},{default:a(()=>[l(d,{label:"总停车位",prop:"totalParking"},{default:a(()=>[l(Ne,{modelValue:o(u).totalParking,"onUpdate:modelValue":e[14]||(e[14]=t=>o(u).totalParking=t),min:0,placeholder:"总停车位数"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k,null,{default:a(()=>[l(g,{span:8},{default:a(()=>[l(d,{label:"省份",prop:"provinceCode"},{default:a(()=>[l(U,{modelValue:o(u).provinceCode,"onUpdate:modelValue":e[15]||(e[15]=t=>o(u).provinceCode=t),placeholder:"请选择省份",clearable:"",onChange:Ke},{default:a(()=>[(n(!0),v(S,null,T(o(he),t=>(n(),i(c,{key:t.areaCode,label:t.areaName,value:t.areaCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(g,{span:8},{default:a(()=>[l(d,{label:"城市",prop:"cityCode"},{default:a(()=>[l(U,{modelValue:o(u).cityCode,"onUpdate:modelValue":e[16]||(e[16]=t=>o(u).cityCode=t),placeholder:"请选择城市",clearable:"",onChange:Me,disabled:!o(u).provinceCode},{default:a(()=>[(n(!0),v(S,null,T(o(L),t=>(n(),i(c,{key:t.areaCode,label:t.areaName,value:t.areaCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(g,{span:8},{default:a(()=>[l(d,{label:"区县",prop:"areaCode"},{default:a(()=>[l(U,{modelValue:o(u).areaCode,"onUpdate:modelValue":e[17]||(e[17]=t=>o(u).areaCode=t),placeholder:"请选择区县",clearable:"",disabled:!o(u).cityCode},{default:a(()=>[(n(!0),v(S,null,T(o(J),t=>(n(),i(c,{key:t.areaCode,label:t.areaName,value:t.areaCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(d,{label:"详细地址",prop:"address"},{default:a(()=>[l(s,{modelValue:o(u).address,"onUpdate:modelValue":e[18]||(e[18]=t=>o(u).address=t),placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1}),l(k,null,{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"经度",prop:"longitude"},{default:a(()=>[l(s,{modelValue:o(u).longitude,"onUpdate:modelValue":e[19]||(e[19]=t=>o(u).longitude=t),placeholder:"请输入经度（-180到180）"},null,8,["modelValue"])]),_:1})]),_:1}),l(g,{span:12},{default:a(()=>[l(d,{label:"纬度",prop:"latitude"},{default:a(()=>[l(s,{modelValue:o(u).latitude,"onUpdate:modelValue":e[20]||(e[20]=t=>o(u).latitude=t),placeholder:"请输入纬度（-90到90）"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k),l(d,{label:"轮播图片",prop:"carouselImages"},{default:a(()=>[l(cl,{modelValue:o(ue),"onUpdate:modelValue":e[21]||(e[21]=t=>F(ue)?ue.value=t:null),limit:6,"file-size":5,"file-type":["png","jpg","jpeg","gif","webp"]},null,8,["modelValue"])]),_:1}),l(d,{label:"备注",prop:"remark"},{default:a(()=>[l(s,{modelValue:o(u).remark,"onUpdate:modelValue":e[22]||(e[22]=t=>o(u).remark=t),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(ee,{title:`${o(q).warehouseName} - 子场库管理`,modelValue:o(H),"onUpdate:modelValue":e[24]||(e[24]=t=>F(H)?H.value=t:null),width:"1200px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:a(()=>[I("div",Dl,[I("div",El,[W((n(),i(y,{type:"primary",icon:"Plus",onClick:ol},{default:a(()=>[p(" 新增子场库 ")]),_:1})),[[D,["platform:warehouse:add"]]])]),W((n(),i(Ce,{data:o(oe),border:""},{default:a(()=>[l(V,{label:"子场库名称",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),l(V,{label:"总车位",align:"center",prop:"totalParking"}),l(V,{label:"状态",align:"center",prop:"status",width:"100"},{default:a(t=>[t.row.status===1?(n(),i($,{key:0,type:"success"},{default:a(()=>[p("正常使用")]),_:1})):t.row.status===2?(n(),i($,{key:1,type:"warning"},{default:a(()=>[p("暂停使用")]),_:1})):t.row.status===3?(n(),i($,{key:2,type:"info"},{default:a(()=>[p("维护中")]),_:1})):O("",!0)]),_:1}),l(V,{label:"操作",align:"center",width:"150"},{default:a(t=>[W((n(),i(y,{link:"",type:"primary",icon:"Edit",onClick:N=>rl(t.row)},{default:a(()=>[p(" 修改 ")]),_:2},1032,["onClick"])),[[D,["platform:warehouse:edit"]]]),W((n(),i(y,{link:"",type:"danger",icon:"Delete",onClick:N=>nl(t.row)},{default:a(()=>[p(" 删除 ")]),_:2},1032,["onClick"])),[[D,["platform:warehouse:remove"]]])]),_:1})]),_:1},8,["data"])),[[We,o(X)]])])]),_:1},8,["title","modelValue"]),l(ee,{title:o(re),modelValue:o(R),"onUpdate:modelValue":e[30]||(e[30]=t=>F(R)?R.value=t:null),width:"1000px","append-to-body":""},{footer:a(()=>[I("div",jl,[l(y,{type:"primary",onClick:sl},{default:a(()=>[p("确 定")]),_:1}),l(y,{onClick:ul},{default:a(()=>[p("取 消")]),_:1})])]),default:a(()=>[l(ie,{ref:"childWarehouseRef",model:o(b),rules:o(Je),"label-width":"120px"},{default:a(()=>[l(k,null,{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"子场库名称",prop:"warehouseName"},{default:a(()=>[l(s,{modelValue:o(b).warehouseName,"onUpdate:modelValue":e[25]||(e[25]=t=>o(b).warehouseName=t),placeholder:"请输入子场库名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k,null,{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"所属运营商",prop:"operatorId"},{default:a(()=>[l(U,{modelValue:o(b).operatorId,"onUpdate:modelValue":e[26]||(e[26]=t=>o(b).operatorId=t),placeholder:"请选择运营商",style:{width:"100%"}},{default:a(()=>[(n(!0),v(S,null,T(o(K),t=>(n(),i(c,{key:t.id,label:t.companyName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k,null,{default:a(()=>[l(g,{span:8},{default:a(()=>[l(d,{label:"总车位数",prop:"totalParking"},{default:a(()=>[l(Ne,{modelValue:o(b).totalParking,"onUpdate:modelValue":e[27]||(e[27]=t=>o(b).totalParking=t),min:0,placeholder:"总车位数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(k,null,{default:a(()=>[l(g,{span:12},{default:a(()=>[l(d,{label:"状态",prop:"status"},{default:a(()=>[l(U,{modelValue:o(b).status,"onUpdate:modelValue":e[28]||(e[28]=t=>o(b).status=t),placeholder:"请选择状态"},{default:a(()=>[l(c,{label:"营业中",value:1}),l(c,{label:"未营业",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(d,{label:"备注",prop:"remark"},{default:a(()=>[l(s,{modelValue:o(b).remark,"onUpdate:modelValue":e[29]||(e[29]=t=>o(b).remark=t),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(ee,{title:"场库详情",modelValue:o(B),"onUpdate:modelValue":e[32]||(e[32]=t=>F(B)?B.value=t:null),width:"800px","append-to-body":""},{footer:a(()=>[I("div",Bl,[l(y,{onClick:e[31]||(e[31]=t=>B.value=!1)},{default:a(()=>[p("关 闭")]),_:1})])]),default:a(()=>[I("div",Al,[l(ml,{column:2,border:""},{default:a(()=>[l(P,{label:"场库名称"},{default:a(()=>[p(E(o(_).warehouseName),1)]),_:1}),l(P,{label:"项目名称"},{default:a(()=>[p(E(o(_).projectName),1)]),_:1}),l(P,{label:"类型"},{default:a(()=>[o(_).parentId==0||o(_).parentId===null||o(_).parentId===void 0?(n(),i($,{key:0,type:"primary"},{default:a(()=>[p("主场库")]),_:1})):(n(),i($,{key:1,type:"success"},{default:a(()=>[p("子场库")]),_:1}))]),_:1}),l(P,{label:"状态"},{default:a(()=>[o(_).status===1?(n(),i($,{key:0,type:"success"},{default:a(()=>[p("营业中")]),_:1})):o(_).status===2?(n(),i($,{key:1,type:"warning"},{default:a(()=>[p("未营业")]),_:1})):O("",!0)]),_:1}),l(P,{label:"运营商"},{default:a(()=>[p(E(o(_).operatorName),1)]),_:1}),l(P,{label:"总车位"},{default:a(()=>[p(E(o(_).totalParking),1)]),_:1}),l(P,{label:"详细位置",span:2},{default:a(()=>[p(E(el(o(_))),1)]),_:1}),l(P,{label:"智能类型"},{default:a(()=>[o(_).smartsType===0?(n(),v("span",Rl,"普通停车场")):o(_).smartsType===1?(n(),v("span",ql,"智能停车场")):o(_).smartsType===2?(n(),v("span",Fl,"无人值守")):O("",!0)]),_:1}),l(P,{label:"创建时间",span:2},{default:a(()=>[p(E(r.parseTime(o(_).createTime)),1)]),_:1}),o(_).remark?(n(),i(P,{key:0,label:"备注",span:2},{default:a(()=>[p(E(o(_).remark),1)]),_:1})):O("",!0)]),_:1}),C(o(_).carouselImages).length>0?(n(),v("div",Jl,[zl,I("div",Ll,[(n(!0),v(S,null,T(C(o(_).carouselImages),(t,N)=>(n(),i(pe,{key:N,src:t,"preview-src-list":C(o(_).carouselImages),"initial-index":N,fit:"cover",class:"detail-carousel-image"},null,8,["src","preview-src-list","initial-index"]))),128))])])):O("",!0)])]),_:1},8,["modelValue"])])}}}),Yl=gl(Kl,[["__scopeId","data-v-0bbb4e5e"]]);export{Yl as default};
