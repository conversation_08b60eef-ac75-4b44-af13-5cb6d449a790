import{v as b,u as I,d as C,r as u,x as V,e as y,c as T,i as e,t as l,f as _,h,n as w,o as A}from"./index-D_FV2sri.js";function D(c){return b({url:"/wx/parking/order/channelPayQuery",method:"post",data:c})}function P(c){return b({url:"/wx/parking/order/paymentTemporaryAlipay",method:"post",data:c})}const B={class:"home"},$={class:"home_bg"},Q={class:"cell"},R={class:"scan_cell"},S=e("div",{class:"cell_header"},[e("div",{class:"scan_cell_title"},"临停缴费"),e("div",{class:"scan_cell_img"})],-1),E={class:"cell_input"},H={class:"cell_title"},L={class:"cell_item"},M=e("div",{class:""},"停车地点",-1),U={class:""},j={class:"cell_item"},z=e("div",{class:""},"入场时间",-1),F={class:""},G={class:"cell_item"},J=e("div",{class:""},"停车时长",-1),K={class:""},O={class:"cell_item"},W=e("div",{class:""},"应缴金额",-1),X={class:""},Y=e("div",{class:"cell_input_title"},"选择支付方式",-1),Z={class:"cell_input_radio"},se={__name:"outPayH5",setup(c){const s=I(),{proxy:d}=C(),a=u(null),r=u("1"),n=u(!1);function N(t){r.value=t}function x(){var t;if(!a.value){d.$message.error("当前订单不存在~");return}if(!((t=a.value)!=null&&t.paymentAmount)){d.$message.error("当前应缴金额为0~");return}if(!n.value)if(n.value=!0,r.value==="1")window.open("weixin://dl/business/?appid=wxf5a8941d0d5617c1&path=pages/carStop&query=stopNo%3D"+s.query.gateNo+"wId"+s.query.warehouseId,"_blank"),n.value=!1;else{let o={gateNo:s.query.gateNo,warehouseId:s.query.warehouseId};P(o).then(i=>{i.data&&(location.href=i.data,setTimeout(()=>{n.value=!1},5e3))})}}return V(()=>{if(s.query.gateNo&&s.query.warehouseId){let t={gateNo:s.query.gateNo,warehouseId:s.query.warehouseId};console.log(t),D(t).then(o=>{a.value=o.data||{}}).catch(o=>{d.$message.error("当前订单不存在~")})}}),(t,o)=>{var v,p,m,f,g;const i=y("el-radio"),k=y("el-radio-group");return A(),T("div",B,[e("div",$,[e("div",Q,[e("div",R,[S,e("div",E,[e("div",H,l(((v=a.value)==null?void 0:v.plateNo)||"--"),1),e("div",L,[M,e("div",U,l(((p=a.value)==null?void 0:p.warehouseName)||"--"),1)]),e("div",j,[z,e("div",F,l(((m=a.value)==null?void 0:m.beginParkingTime)||"--"),1)]),e("div",G,[J,e("div",K,l(((f=a.value)==null?void 0:f.parkingDurationLabel)||"--"),1)]),e("div",O,[W,e("div",X,l(((g=a.value)==null?void 0:g.paymentAmount)||"--"),1)]),Y,e("div",Z,[_(k,{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=q=>r.value=q),onChange:N},{default:h(()=>[_(i,{label:"1",border:""},{default:h(()=>[w("微信")]),_:1}),_(i,{label:"2",border:""},{default:h(()=>[w("支付宝")]),_:1})]),_:1},8,["modelValue"])])]),e("div",{class:"submit-btn",onClick:x},l(n.value?"正在跳转":"确认支付"),1)])])])])}}};export{se as default};
