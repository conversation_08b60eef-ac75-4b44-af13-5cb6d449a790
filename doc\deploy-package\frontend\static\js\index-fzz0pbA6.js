import{O as _e,d as ge,r as m,C as ye,T as ve,e as n,Q as Y,c as U,o as i,R as g,f as e,S as be,l as a,h as l,m as K,L as M,M as j,j as f,P as I,n as r,i as P,t as $,X as he}from"./index-Cox2ohSR.js";import{l as we,g as ke,d as Ve,r as Ce,u as Te,a as xe}from"./type-fCKnKrvY.js";import{C as O}from"./index-Cy2yB1cz.js";const Se={class:"app-container"},Ne={class:"dialog-footer"},De=_e({name:"Dict"}),$e=Object.assign(De,{components:{CustomPagination:O}},{setup(Re){const{proxy:p}=ge(),{sys_normal_disable:x}=p.useDict("sys_normal_disable"),q=m([]),y=m(!1),S=m(!0),C=m(!0),N=m([]),z=m(!0),B=m(!0),E=m(0),D=m(""),k=m([]),A=ye({form:{},queryParams:{pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0},rules:{dictName:[{required:!0,message:"字典名称不能为空",trigger:"blur"}],dictType:[{required:!0,message:"字典类型不能为空",trigger:"blur"}]}}),{queryParams:d,form:s,rules:X}=ve(A);function b(){S.value=!0,we(p.addDateRange(d.value,k.value)).then(u=>{q.value=u.rows,E.value=u.total,S.value=!1})}function G(){y.value=!1,R()}function R(){s.value={dictId:void 0,dictName:void 0,dictType:void 0,status:"0",remark:void 0},p.resetForm("dictRef")}function T(){d.value.pageNum=1,b()}function H(){k.value=[],p.resetForm("queryRef"),T()}function J(){R(),y.value=!0,D.value="添加字典类型"}function W(u){N.value=u.map(o=>o.dictId),z.value=u.length!=1,B.value=!u.length}function F(u){R();const o=u.dictId||N.value;ke(o).then(h=>{s.value=h.data,y.value=!0,D.value="修改字典类型"})}function Z(){p.$refs.dictRef.validate(u=>{u&&(s.value.dictId!=null?Te(s.value).then(o=>{p.$modal.msgSuccess("修改成功"),y.value=!1,b()}):xe(s.value).then(o=>{p.$modal.msgSuccess("新增成功"),y.value=!1,b()}))})}function L(u){const o=u.dictId||N.value;p.$modal.confirm('是否确认删除字典编号为"'+o+'"的数据项？').then(function(){return Ve(o)}).then(()=>{b(),p.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(){p.download("system/dict/type/export",{...d.value},`dict_${new Date().getTime()}.xlsx`)}function te(){Ce().then(()=>{p.$modal.msgSuccess("刷新成功"),he().cleanDict()})}return b(),(u,o)=>{const h=n("el-input"),_=n("el-form-item"),le=n("el-option"),ae=n("el-select"),oe=n("el-date-picker"),c=n("el-button"),Q=n("el-form"),V=n("el-col"),ne=n("right-toolbar"),de=n("el-row"),v=n("el-table-column"),se=n("router-link"),ue=n("dict-tag"),ie=n("el-table"),re=n("el-radio"),pe=n("el-radio-group"),ce=n("el-dialog"),w=Y("hasPermi"),me=Y("loading");return i(),U("div",Se,[g(e(Q,{model:a(d),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(_,{label:"字典名称",prop:"dictName"},{default:l(()=>[e(h,{modelValue:a(d).dictName,"onUpdate:modelValue":o[0]||(o[0]=t=>a(d).dictName=t),placeholder:"请输入字典名称",clearable:"",style:{width:"240px"},onKeyup:K(T,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"字典类型",prop:"dictType"},{default:l(()=>[e(h,{modelValue:a(d).dictType,"onUpdate:modelValue":o[1]||(o[1]=t=>a(d).dictType=t),placeholder:"请输入字典类型",clearable:"",style:{width:"240px"},onKeyup:K(T,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"状态",prop:"status"},{default:l(()=>[e(ae,{modelValue:a(d).status,"onUpdate:modelValue":o[2]||(o[2]=t=>a(d).status=t),placeholder:"字典状态",clearable:"",style:{width:"240px"}},{default:l(()=>[(i(!0),U(M,null,j(a(x),t=>(i(),f(le,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"创建时间",style:{width:"308px"}},{default:l(()=>[e(oe,{modelValue:a(k),"onUpdate:modelValue":o[3]||(o[3]=t=>I(k)?k.value=t:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(_,null,{default:l(()=>[e(c,{type:"primary",icon:"Search",onClick:T},{default:l(()=>[r("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:H},{default:l(()=>[r("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[be,a(C)]]),e(de,{gutter:10,class:"mb8"},{default:l(()=>[e(V,{span:1.5},{default:l(()=>[g((i(),f(c,{type:"primary",plain:"",icon:"Plus",onClick:J},{default:l(()=>[r("新增")]),_:1})),[[w,["system:dict:add"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[g((i(),f(c,{type:"success",plain:"",icon:"Edit",disabled:a(z),onClick:F},{default:l(()=>[r("修改")]),_:1},8,["disabled"])),[[w,["system:dict:edit"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[g((i(),f(c,{type:"danger",plain:"",icon:"Delete",disabled:a(B),onClick:L},{default:l(()=>[r("删除")]),_:1},8,["disabled"])),[[w,["system:dict:remove"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[g((i(),f(c,{type:"warning",plain:"",icon:"Download",onClick:ee},{default:l(()=>[r("导出")]),_:1})),[[w,["system:dict:export"]]])]),_:1}),e(V,{span:1.5},{default:l(()=>[g((i(),f(c,{type:"danger",plain:"",icon:"Refresh",onClick:te},{default:l(()=>[r("刷新缓存")]),_:1})),[[w,["system:dict:remove"]]])]),_:1}),e(ne,{showSearch:a(C),"onUpdate:showSearch":o[4]||(o[4]=t=>I(C)?C.value=t:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),g((i(),f(ie,{data:a(q),onSelectionChange:W},{default:l(()=>[e(v,{type:"selection",width:"55",align:"center"}),e(v,{label:"字典编号",align:"center",prop:"dictId"}),e(v,{label:"字典名称",align:"center",prop:"dictName","show-overflow-tooltip":!0}),e(v,{label:"字典类型",align:"center","show-overflow-tooltip":!0},{default:l(t=>[e(se,{to:"/system/dict-data/index/"+t.row.dictId,class:"link-type"},{default:l(()=>[P("span",null,$(t.row.dictType),1)]),_:2},1032,["to"])]),_:1}),e(v,{label:"状态",align:"center",prop:"status"},{default:l(t=>[e(ue,{options:a(x),value:t.row.status},null,8,["options","value"])]),_:1}),e(v,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),e(v,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(t=>[P("span",null,$(u.parseTime(t.row.createTime)),1)]),_:1}),e(v,{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},{default:l(t=>[g((i(),f(c,{link:"",type:"primary",icon:"Edit",onClick:fe=>F(t.row)},{default:l(()=>[r("修改")]),_:2},1032,["onClick"])),[[w,["system:dict:edit"]]]),g((i(),f(c,{link:"",type:"primary",icon:"Delete",onClick:fe=>L(t.row)},{default:l(()=>[r("删除")]),_:2},1032,["onClick"])),[[w,["system:dict:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,a(S)]]),e(O,{total:a(E),"current-page":a(d).pageNum,"onUpdate:currentPage":o[5]||(o[5]=t=>a(d).pageNum=t),"page-size":a(d).pageSize,"onUpdate:pageSize":o[6]||(o[6]=t=>a(d).pageSize=t),onPagination:b},null,8,["total","current-page","page-size"]),e(ce,{title:a(D),modelValue:a(y),"onUpdate:modelValue":o[11]||(o[11]=t=>I(y)?y.value=t:null),width:"500px","append-to-body":""},{footer:l(()=>[P("div",Ne,[e(c,{type:"primary",onClick:Z},{default:l(()=>[r("确 定")]),_:1}),e(c,{onClick:G},{default:l(()=>[r("取 消")]),_:1})])]),default:l(()=>[e(Q,{ref:"dictRef",model:a(s),rules:a(X),"label-width":"80px"},{default:l(()=>[e(_,{label:"字典名称",prop:"dictName"},{default:l(()=>[e(h,{modelValue:a(s).dictName,"onUpdate:modelValue":o[7]||(o[7]=t=>a(s).dictName=t),placeholder:"请输入字典名称"},null,8,["modelValue"])]),_:1}),e(_,{label:"字典类型",prop:"dictType"},{default:l(()=>[e(h,{modelValue:a(s).dictType,"onUpdate:modelValue":o[8]||(o[8]=t=>a(s).dictType=t),placeholder:"请输入字典类型"},null,8,["modelValue"])]),_:1}),e(_,{label:"状态",prop:"status"},{default:l(()=>[e(pe,{modelValue:a(s).status,"onUpdate:modelValue":o[9]||(o[9]=t=>a(s).status=t)},{default:l(()=>[(i(!0),U(M,null,j(a(x),t=>(i(),f(re,{key:t.value,value:t.value},{default:l(()=>[r($(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"备注",prop:"remark"},{default:l(()=>[e(h,{modelValue:a(s).remark,"onUpdate:modelValue":o[10]||(o[10]=t=>a(s).remark=t),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{$e as default};
