#!/bin/bash

# 网络架构验证脚本
# 验证系统nginx转发架构: 外部访问 → 系统nginx → Docker容器
# 外部(3443) → 系统nginx(443) → 分流(9001/9443) → Docker容器

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 加载通用函数
source "$SCRIPTS_ROOT/utils/common.sh"

# 初始化
init_common

# 网络架构配置
EXTERNAL_PORT="3443"
SYSTEM_NGINX_PORT="443"
JENKINS_SYSTEM_PORT="9001"
FRONTEND_SYSTEM_PORT="9443"
BASE_DOMAIN="test-parknew.lgfw24hours.com"

# Docker容器配置
JENKINS_CONTAINER="jenkins-park"
NGINX_CONTAINER="park-nginx"
BACKEND_CONTAINER="park-backend"

# URL配置
EXTERNAL_URL="https://${BASE_DOMAIN}:${EXTERNAL_PORT}"
JENKINS_URL="${EXTERNAL_URL}/jenkins"
FRONTEND_URL="${EXTERNAL_URL}/"
API_URL="${EXTERNAL_URL}/prod-api"

# 默认参数
VERBOSE="false"
TIMEOUT=30

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --verbose)
            VERBOSE="true"
            shift
            ;;
        --timeout=*)
            TIMEOUT="${1#*=}"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --verbose        详细输出"
            echo "  --timeout=SEC    超时时间（秒） 默认: 30"
            echo "  -h, --help       显示帮助信息"
            exit 0
            ;;
        *)
            log "ERROR" "未知参数: $1"
            exit 1
            ;;
    esac
done

log "INFO" "开始网络架构验证"
log "INFO" "外部访问地址: $EXTERNAL_URL"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$VERBOSE" = "true" ]; then
        log "INFO" "执行测试: $test_name"
    fi
    
    if eval "$test_command"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        if [ "$VERBOSE" = "true" ]; then
            log "INFO" "✓ $test_name - 通过"
        fi
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log "ERROR" "✗ $test_name - 失败"
        return 1
    fi
}

# 1. 系统端口监听检查
test_system_ports() {
    log "INFO" "检查系统端口监听..."
    netstat -tlnp | grep -q ":${SYSTEM_NGINX_PORT}" &&
    netstat -tlnp | grep -q ":${JENKINS_SYSTEM_PORT}" &&
    netstat -tlnp | grep -q ":${FRONTEND_SYSTEM_PORT}"
}

# 2. Nginx配置检查
test_nginx_config() {
    log "INFO" "检查nginx配置..."
    nginx -t > /dev/null 2>&1
}

# 3. Nginx服务状态检查
test_nginx_service() {
    log "INFO" "检查nginx服务状态..."
    systemctl is-active nginx > /dev/null 2>&1
}

# 5. Docker容器状态检查
test_docker_containers() {
    log "INFO" "检查Docker容器状态..."
    docker ps --filter "name=${JENKINS_CONTAINER}" --filter "status=running" | grep -q "${JENKINS_CONTAINER}" &&
    docker ps --filter "name=${NGINX_CONTAINER}" --filter "status=running" | grep -q "${NGINX_CONTAINER}"
}

# 6. 容器端口映射检查
test_container_ports() {
    log "INFO" "检查容器端口映射..."
    docker port "${JENKINS_CONTAINER}" | grep -q "${JENKINS_SYSTEM_PORT}" &&
    docker port "${NGINX_CONTAINER}" | grep -q "${FRONTEND_SYSTEM_PORT}"
}

# 7. DNS解析检查
test_dns_resolution() {
    log "INFO" "检查DNS解析..."
    nslookup "${BASE_DOMAIN}" > /dev/null 2>&1
}

# 8. 外部连通性检查
test_external_connectivity() {
    log "INFO" "检查外部连通性..."
    curl -f -s --max-time $TIMEOUT "${FRONTEND_URL}" > /dev/null
}

# 9. Jenkins路径转发检查
test_jenkins_path_forwarding() {
    log "INFO" "检查Jenkins路径转发..."
    curl -f -s --max-time $TIMEOUT "${JENKINS_URL}" > /dev/null
}

# 10. API路径转发检查
test_api_path_forwarding() {
    log "INFO" "检查API路径转发..."
    curl -f -s --max-time $TIMEOUT "${API_URL}/actuator/health" > /dev/null
}

# 11. SSL证书检查
test_ssl_certificate() {
    log "INFO" "检查SSL证书..."
    echo | openssl s_client -servername "${BASE_DOMAIN}" -connect "${BASE_DOMAIN}:${EXTERNAL_PORT}" 2>/dev/null | openssl x509 -noout -dates > /dev/null 2>&1
}

# 12. 网络延迟检查
test_network_latency() {
    log "INFO" "检查网络延迟..."
    local response_time=$(curl -o /dev/null -s -w "%{time_total}" --max-time $TIMEOUT "${FRONTEND_URL}")
    # 检查响应时间是否小于5秒
    awk "BEGIN {exit !($response_time < 5.0)}"
}

# 执行所有测试
log "INFO" "=== 系统基础设施验证 ==="
run_test "系统端口监听" "test_system_ports"
run_test "Nginx配置" "test_nginx_config"
run_test "Nginx服务状态" "test_nginx_service"

log "INFO" "=== Docker容器验证 ==="
run_test "Docker容器状态" "test_docker_containers"
run_test "容器端口映射" "test_container_ports"

log "INFO" "=== 网络连通性验证 ==="
run_test "DNS解析" "test_dns_resolution"
run_test "SSL证书" "test_ssl_certificate"
run_test "外部连通性" "test_external_connectivity"
run_test "网络延迟" "test_network_latency"

log "INFO" "=== 路径转发验证 ==="
run_test "Jenkins路径转发" "test_jenkins_path_forwarding"
run_test "API路径转发" "test_api_path_forwarding"

# 输出测试结果
log "INFO" "网络架构验证完成"
log "INFO" "测试结果: 总计 $TOTAL_TESTS, 通过 $PASSED_TESTS, 失败 $FAILED_TESTS"

# 生成详细报告
if [ "$VERBOSE" = "true" ]; then
    log "INFO" "=== 详细网络架构信息 ==="
    echo "防火墙转发规则:"
    iptables -t nat -L -n | grep "${EXTERNAL_PORT}" || echo "未找到相关规则"
    
    echo "系统端口监听:"
    netstat -tlnp | grep -E ":(${FIREWALL_INTERNAL_PORT}|${JENKINS_SYSTEM_PORT}|${FRONTEND_SYSTEM_PORT})"
    
    echo "Docker容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=${JENKINS_CONTAINER}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=${NGINX_CONTAINER}"
    
    echo "Nginx配置测试:"
    nginx -t
fi

if [ $FAILED_TESTS -gt 0 ]; then
    log "ERROR" "网络架构验证失败，有 $FAILED_TESTS 个测试未通过"
    exit 1
else
    log "INFO" "网络架构验证全部通过"
    exit 0
fi
